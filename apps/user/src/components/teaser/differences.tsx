import { Container } from "@vtuber/ui/components/container";
import { HorizontalScrollIcon } from "@vtuber/ui/components/icons/horizontal-scroll-icon";
import { ScrollArea, ScrollBar } from "@vtuber/ui/components/scroll-area";
import { XIcon } from "lucide-react";
import { TeaserTitle } from "./teaser-title";

export const Differences = () => {
  return (
    <div className="relative overflow-hidden pb-20">
      <div
        className="bg-[#207DF8] h-[1px] md:w-[816px] w-[422px] rotate-[-30deg] absolute -right-[100px] bottom-20 -z-10"
        style={{
          filter: "drop-shadow(0px 0px 9px #fff)",
          msFilter: "drop-shadow(0px 0px 9px #fff)",
          WebkitFilter: "drop-shadow(0px 0px 9px #fff)",
        }}
      />
      <Container className="space-y-12">
        <TeaserTitle
          title="Differences"
          subTitle="他社クラウドファンディングサービスとの違い"
          leftIconClassName="pt-5"
        />
        <div className="relative">
          <div className="flex lg:hidden flex-col absolute top-1/2 left-1/2 -translate-x-1/2 -translate-y-1/2 z-20 items-center bg-[#423F5C]/90 py-[10px] px-2 rounded-[6px]">
            <HorizontalScrollIcon />
            <p>横スクロール可能です</p>
          </div>
          <ScrollArea>
            <div className="flex items-end flex-nowrap">
              <section className="divide-y divide-gray01 bg-[#29273B] text-center  text-sm font-bold text-font min-w-[170px] border border-gray01">
                <div className="py-5 min-h-[65px]"></div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  手数料
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  制作依頼・サポート
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  ストーリー性
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  PR効果
                </div>
              </section>
              <section className="text-center bg-background min-w-[255px]">
                <div className="py-5 min-h-[80px] relative bg-gradient-to-b from-[#FCC068] to-[#F0940E] flex items-center justify-center font-bold text-[22px] p-5">
                  V祭
                </div>
                <div className="border-l-2 border-r-2 border-b-2 border-tertiary divide-y divide-gray01 border-t-transparent text-sm font-bold text-font">
                  <div className="min-h-[117px] flex items-center justify-center px-5 flex-col gap-[9px]">
                    <p className="text-[35px] text-tertiary font-bold">
                      0<span className="text-[15px]">円</span>
                    </p>
                    <p className="text-xs text-gray01">
                      {" "}
                      ※リリース記念キャンペーン
                    </p>
                  </div>
                  <div className="min-h-[117px] flex items-center justify-center px-5 flex-col gap-[9px]">
                    <div className="size-[28px] rounded-full border-2 border-tertiary" />
                    制作会社が直接対応
                  </div>
                  <div className="min-h-[117px] flex items-center justify-center px-5 flex-col gap-[9px]">
                    <div className="size-[28px] rounded-full border-2 border-tertiary" />
                    イベントを通じて一体感を演出
                  </div>
                  <div className="min-h-[117px] flex items-center justify-center px-5 flex-col gap-[9px]">
                    <div className="size-[28px] rounded-full border-2 border-tertiary" />
                    イベントで新規ファン獲得が可能
                  </div>
                </div>
              </section>
              <section className="text-center text-sm text-font font-medium divide-y divide-gray01 border border-gray01 bg-background min-w-[255px]">
                <div className="py-5 min-h-[65px] font-bold bg-[#29273B]">
                  A社
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  17%
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  自分で手配
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  資金調達に留まる
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  既存ファン内で留まる
                </div>
              </section>
              <section className="text-center text-sm text-font font-medium divide-y divide-gray01 border border-gray01 border-l-transparent bg-background min-w-[255px]">
                <div className="py-5 min-h-[65px] font-bold bg-[#29273B]">
                  B社
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <p>14%</p>
                  <p>支援者が負担</p>
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  自分で手配
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  資金調達に留まる
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  既存ファン内で留まる
                </div>
              </section>
              <section className="text-center text-sm text-font font-medium divide-y divide-gray01 border border-gray01 border-l-transparent bg-background min-w-[255px]">
                <div className="py-5 min-h-[65px] font-bold bg-[#29273B]">
                  C社
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5">
                  20%
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  自分で手配
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  資金調達に留まる
                </div>
                <div className="min-h-[118px] flex items-center justify-center px-5 flex-col gap-y-[9px]">
                  <XIcon />
                  既存ファン内で留まる
                </div>
              </section>
            </div>
            <ScrollBar orientation="horizontal" />
          </ScrollArea>
        </div>
      </Container>
    </div>
  );
};
