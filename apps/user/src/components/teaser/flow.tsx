import { Container } from "@vtuber/ui/components/container";
import { HandShakeIcon } from "@vtuber/ui/components/icons/hand-shake";
import { LikeIcon } from "@vtuber/ui/components/icons/like-icon";
import { PapersIcon } from "@vtuber/ui/components/icons/papers";
import { SittingTogetherIcon } from "@vtuber/ui/components/icons/sitting-together-icon";
import { StampIcon } from "@vtuber/ui/components/icons/stamp";
import { WritingIcon } from "@vtuber/ui/components/icons/writing-icon";
import { TeaserTitle } from "./teaser-title";

const steps = [
  {
    title: "ヒアリング（プロジェクトの詳細決定）",
    description:
      "クラウドファンディングの目的や目標金額、実施期間、支援者へのリターン内容などを、担当スタッフと一緒に具体的に決めていきます。はじめての方でも安心して進められるよう、丁寧にサポートします。",
    icon: <SittingTogetherIcon />,
  },
  {
    title: "プロジェクトの作成",
    description:
      "決定した内容をもとに、プロジェクトページを作成します。タイトル、ビジュアル、紹介文、リターン説明など、ファンに魅力が伝わる構成で仕上げていきます。デザイン面のご相談も可能です。",
    icon: <WritingIcon />,
  },
  {
    title: "プロジェクト申請および審査",
    description:
      "完成したプロジェクトは、プラットフォーム運営側で内容確認と審査を行います。表現や条件に問題がないかをチェックし、必要に応じて微調整をご提案します。",
    icon: <StampIcon />,
  },
  {
    title: "プロモーション開始",
    description:
      "審査通過後は、SNSや配信内での告知など、プロジェクトの告知・拡散をスタートします。バナーや紹介画像などの素材も提供可能で、効果的な広報をご支援します。",
    icon: <HandShakeIcon />,
  },
  {
    title: "クラウドファンディング募集開始",
    description:
      "いよいよ公開スタート。支援募集期間中は、進捗の報告やファンとの交流を通じて、プロジェクトを盛り上げていきましょう。運営側も随時サポートします。",
    icon: <PapersIcon />,
  },
  {
    title: "プロジェクト完了",
    description:
      "募集期間が終了し、目標金額に到達した場合はリターンの制作・発送に移ります。支援者への感謝の気持ちを込めたお礼メッセージや活動報告も、次回につながる大切なステップです。",
    icon: <LikeIcon />,
  },
];

export const Flow = () => {
  return (
    <Container className="space-y-12">
      <TeaserTitle
        title="FLOW"
        subTitle="クラウドファンディング実施から完了までの流れ"
      />
      <div className="border border-blue02 rounded-[5px] md:px-20 py-20 px-5 grid gap-y-[60px] relative overflow-hidden bg-[url('https://cdn.v-sai.com/assets/teaser_flow_bg.png')] bg-cover bg-no-repeat">
        {steps.map((step, i) => (
          <div
            key={i}
            className="gap-y-[60px] grid">
            <section className="relative w-full h-[1px] bg-blue02">
              <div className="absolute left-1/2 -translate-x-1/2 top-1/2 -translate-y-1/2 rounded-full bg-background z-10 px-[22px] py-[5px] border border-blue02">
                Step {i + 1}
              </div>
            </section>
            <section className="flex md:flex-row flex-col md:justify-between md:items-stretch items-center text-font md:gap-[107px] gap-[35px]">
              <div className="md:space-y-[40px] space-y-6 flex-1 md:order-1 order-2">
                <h6 className="md:text-[26px] text-2xl font-bold">
                  {step.title}
                </h6>
                <p>{step.description}</p>
              </div>
              <div
                className="size-[140px] rounded-full bg-background flex md:order-2 order-1 items-center justify-center"
                style={{
                  filter: "drop-shadow(4px 4px 7px #0E0B23)",
                }}>
                {step.icon}
              </div>
            </section>
          </div>
        ))}
      </div>
    </Container>
  );
};
