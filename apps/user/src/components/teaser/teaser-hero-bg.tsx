import { GridPatternIcon } from "@vtuber/ui/components/icons/grid-pattern-icon";

export const TeaserHeroBg = () => {
  return (
    <div className="absolute inset-0">
      <div
        className="h-1 w-[422px] absolute sm:top-5 -top-5 bg-[#207DF8] z-40 rotate-[-30deg] sm:-left-16 -left-10"
        style={{
          filter: "drop-shadow(0px 0px 8px #FFFFFF)",
          WebkitFilter: "drop-shadow(0px 0px 8px #FFFFFF)",
          msFilter: "drop-shadow(0px 0px 8px #FFFFFF)",
        }}
      />
      <div
        className="h-1 w-[816px] sm:block hidden absolute bottom-16 bg-[#207DF8] z-40 rotate-[-30deg] -right-14"
        style={{
          filter: "drop-shadow(0px 0px 8px #FFFFFF)",
          WebkitFilter: "drop-shadow(0px 0px 8px #FFFFFF)",
          msFilter: "drop-shadow(0px 0px 8px #FFFFFF)",
        }}
      />
      <div className="absolute z-30 w-full bottom-0">
        <div
          className="h-[1132px] w-[103%] md:block hidden"
          style={{
            clipPath: "polygon(68% 50%, 0% 100%, 100% 100%)",
            background: "linear-gradient(-136deg, #2A1853, #19172C)",
          }}
        />
        <div
          className="h-[417px] w-full md:hidden block"
          style={{
            clipPath: "polygon(100% 0, 0% 100%, 100% 100%)",
            background: "linear-gradient(-136deg, rgba(25,23,44,0.5), #19172C)",
          }}
        />
      </div>

      <div className="absolute inset-0 bg-[#010113]" />
      <div className="w-[1174px] h-[1096px] bg-[#1C1733] z-10 absolute rotate-[-68deg] -right-[5%] top-1/2 -translate-y-1/2" />
      <div
        className="absolute inset-0 z-20"
        style={{
          background:
            "linear-gradient(-30deg, rgba(25,23,44,0) 0%, rgba(31,23,57,0.7) 35%, rgba(33,24,63,1) 49%, rgba(31,23,57,0.69) 69%, rgba(25,23,44,0) 100%)",
        }}
      />
      <div
        className="absolute w-full h-[982px] z-10 "
        style={{
          background: "linear-gradient(to bottom, #19172C, #2A1853)",
        }}
      />
      <div className="absolute min-h-[1070.83px] min-w-[1823.47px] z-10">
        <GridPatternIcon className="!size-full" />
      </div>
    </div>
  );
};
