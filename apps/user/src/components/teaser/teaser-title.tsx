import { cn } from "@vtuber/ui/lib/utils";

interface Props {
  title: string;
  subTitle: string;
  caption?: string;
  className?: string;
  leftIconClassName?: string;
  rightIconClassName?: string;
}

export const TeaserTitle = ({
  title,
  subTitle,
  caption,
  className,
  leftIconClassName,
  rightIconClassName,
}: Props) => {
  return (
    <section className={cn("text-center lg:space-y-0 space-y-6", className)}>
      <div
        className={cn(
          "flex flex-col gap-y-6 items-end lg:pr-14 lg:translate-x-0 translate-x-5",
          rightIconClassName,
        )}>
        <div className="h-[1px] w-[113px] bg-gradient-4" />
        <div className="h-[1px] w-[57px] bg-gradient-4 mr-[120px]" />
      </div>
      <h3
        className="font-montserrat font-medium lg:text-[103px] text-[34px] text-background"
        style={{
          textShadow: "0px 0px 1px #56477D, 0px 0px 2px #4C8B95",
        }}>
        {title}
      </h3>
      {!!caption ? (
        <div className="lg:-mt-10">
          <p className="font-bold italic text-[43px] text-center bg-gradient-4 w-fit mx-auto text-transparent bg-clip-text relative">
            {subTitle}
            <div className="w-full absolute h-0.5 bg-gradient-4 bottom-0" />
          </p>
          <p className="font-bold italic lg:text-[43px] text-[28px] text-center bg-gradient-4 w-fit mx-auto text-transparent bg-clip-text relative">
            {caption}
            <div className="w-full absolute h-0.5 bg-gradient-4 bottom-0" />
          </p>
        </div>
      ) : (
        <p className="font-bold italic lg:text-[43px] text-center bg-gradient-4 w-fit mx-auto text-transparent bg-clip-text lg:-mt-10 relative text-[28px] ">
          {subTitle}
          <div className="w-full absolute h-0.5 bg-gradient-4 bottom-0" />
        </p>
      )}
      <div
        className={cn(
          "flex flex-col gap-y-6 lg:pl-14 lg:translate-x-0 -translate-x-5",
          leftIconClassName,
        )}>
        <div className="h-[1px] w-[113px] bg-gradient-4 ml-11" />
        <div className="h-[1px] w-[57px] bg-gradient-4" />
      </div>
    </section>
  );
};
