import { Link } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Container } from "@vtuber/ui/components/container";
import { Image } from "@vtuber/ui/components/image";
import { Logo } from "@vtuber/ui/components/logo";

export const TeaserFooter = () => {
  const { getText } = useLanguage();
  return (
    <Container className="pb-10 space-y-10">
      <Link
        to="/"
        className="flex items-center flex-col gap-y-2">
        <Logo />
        <Image
          src="https://cdn.v-sai.com/assets/-V%E7%A5%AD-.png"
          alt="-V祭-"
          className="w-[87px] h-[52px] object-contain"
        />
      </Link>
      <div className="flex items-center justify-center gap-6 text-gray01 text-sm pt-4 md:flex-nowrap flex-wrap">
        <Link
          className="hover:underline"
          to="/terms">
          {getText("terms_of_use")}
        </Link>
        <Link
          className="hover:underline"
          to="/transaction-act">
          {getText("transaction_act")}
        </Link>
        <Link
          className="hover:underline"
          to="/privacy">
          {getText("privacy_policy")}
        </Link>
        <Link
          className="hover:underline"
          to="/operating-company">
          {getText("operating_company")}
        </Link>
      </div>
      <small className="text-xs text-font text-center block">
        ©{new Date().getFullYear()} Vsai. All Rights Reserved.
      </small>
    </Container>
  );
};
