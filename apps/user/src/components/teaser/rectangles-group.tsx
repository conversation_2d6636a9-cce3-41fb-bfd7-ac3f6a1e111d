export const RectanglesGroup = () => {
  return (
    <svg
      width="298"
      height="229"
      viewBox="0 0 298 229"
      fill="none"
      xmlns="http://www.w3.org/2000/svg">
      <g filter="url(#filter0_d_4001_5858)">
        <rect
          x="73.5"
          y="52.5"
          width="105"
          height="106"
          rx="1.5"
          stroke="#88E5F6"
          shapeRendering="crispEdges"
        />
      </g>
      <g filter="url(#filter1_d_4001_5858)">
        <rect
          x="103.5"
          y="184.5"
          width="35"
          height="35"
          rx="1.5"
          stroke="#88E5F6"
          shapeRendering="crispEdges"
        />
      </g>
      <rect
        y="27"
        width="30"
        height="30"
        rx="2"
        fill="#64A3B5"
      />
      <g filter="url(#filter2_d_4001_5858)">
        <rect
          x="164.5"
          y="100.5"
          width="159"
          height="96"
          rx="1.5"
          stroke="#88E5F6"
          shapeRendering="crispEdges"
        />
      </g>
      <g filter="url(#filter3_d_4001_5858)">
        <rect
          x="142.5"
          y="-11.5"
          width="138"
          height="138"
          rx="1.5"
          stroke="#88E5F6"
          shapeRendering="crispEdges"
        />
      </g>
      <defs>
        <filter
          id="filter0_d_4001_5858"
          x="64"
          y="43"
          width="124"
          height="125"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood
            flood-opacity="0"
            result="BackgroundImageFix"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="4.5" />
          <feComposite
            in2="hardAlpha"
            operator="out"
          />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4001_5858"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4001_5858"
            result="shape"
          />
        </filter>
        <filter
          id="filter1_d_4001_5858"
          x="94"
          y="175"
          width="54"
          height="54"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood
            flood-opacity="0"
            result="BackgroundImageFix"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="4.5" />
          <feComposite
            in2="hardAlpha"
            operator="out"
          />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4001_5858"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4001_5858"
            result="shape"
          />
        </filter>
        <filter
          id="filter2_d_4001_5858"
          x="155"
          y="91"
          width="178"
          height="115"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood
            flood-opacity="0"
            result="BackgroundImageFix"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="4.5" />
          <feComposite
            in2="hardAlpha"
            operator="out"
          />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4001_5858"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4001_5858"
            result="shape"
          />
        </filter>
        <filter
          id="filter3_d_4001_5858"
          x="133"
          y="-21"
          width="157"
          height="157"
          filterUnits="userSpaceOnUse"
          colorInterpolationFilters="sRGB">
          <feFlood
            flood-opacity="0"
            result="BackgroundImageFix"
          />
          <feColorMatrix
            in="SourceAlpha"
            type="matrix"
            values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0"
            result="hardAlpha"
          />
          <feOffset />
          <feGaussianBlur stdDeviation="4.5" />
          <feComposite
            in2="hardAlpha"
            operator="out"
          />
          <feColorMatrix
            type="matrix"
            values="0 0 0 0 1 0 0 0 0 1 0 0 0 0 1 0 0 0 1 0"
          />
          <feBlend
            mode="normal"
            in2="BackgroundImageFix"
            result="effect1_dropShadow_4001_5858"
          />
          <feBlend
            mode="normal"
            in="SourceGraphic"
            in2="effect1_dropShadow_4001_5858"
            result="shape"
          />
        </filter>
      </defs>
    </svg>
  );
};
