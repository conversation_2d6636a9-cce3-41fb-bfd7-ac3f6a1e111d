import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@vtuber/ui/components/accordion";
import { Container } from "@vtuber/ui/components/container";
import { AnswerIcon } from "@vtuber/ui/components/icons/answer-icon";
import { QuestionIcon } from "@vtuber/ui/components/icons/question-icon";
import { Minus, Plus } from "lucide-react";
import { TeaserTitle } from "./teaser-title";

const faqs = [
  {
    question: "よくある質問の内容が入ります。",
    answer:
      "はい、あります。プロジェクト開始から終了、リターンまで、担当者がサポートいたします。",
  },
  {
    question: "クラファンのサポート体制はありますか？",
    answer:
      "はい、あります。プロジェクト開始から終了、リターンまで、担当者がサポートいたします。",
  },
  {
    question: "クラファンで資金達成した後の制作はどのように制作されますか？",
    answer:
      "クラファンで目標資金に達成し、実施期間の終了後、担当者からヒアリングを行い、要望に合わせたデザイン案をご用意いたします。その後修正を経て完成となります。",
  },
  {
    question: "プロジェクトが成功（SUCCESS）した場合、いつから開始できますか？",
    answer:
      "募集期間内で目標金額を達成した場合、プロジェクトの開始が可能です。（要確認）",
  },
];

export const TeaserFAQ = () => {
  return (
    <div className="relative overflow-hidden -translate-y-20 pb-32">
      <div
        className="bg-[#207DF8] h-[1px] md:w-[816px] w-[422px] rotate-[-30deg] absolute left-[-140px] top-24 -z-10"
        style={{
          filter: "drop-shadow(0px 0px 9px #fff)",
          msFilter: "drop-shadow(0px 0px 9px #fff)",
          WebkitFilter: "drop-shadow(0px 0px 9px #fff)",
        }}
      />
      <div
        className="bg-[#207DF8] h-[1px] w-[816px] md:block hidden rotate-[-30deg] absolute -right-[100px] bottom-20 -z-10"
        style={{
          filter: "drop-shadow(0px 0px 9px #fff)",
          msFilter: "drop-shadow(0px 0px 9px #fff)",
          WebkitFilter: "drop-shadow(0px 0px 9px #fff)",
        }}
      />
      <Container className="space-y-12 pt-20">
        <TeaserTitle
          title="FAQ"
          subTitle="よくある質問"
        />
        <Accordion
          type="single"
          defaultValue={faqs[0].question}
          className="gap-y-[24px] grid lg:max-w-[960px] mx-auto"
          collapsible>
          {faqs?.map((f, i) => (
            <AccordionItem
              key={i}
              value={f.question}
              className="border-b-0">
              <AccordionTrigger className="bg-[#2C2A37] hover:text-purple01 data-[state=open]:text-purple01 sm:px-10 px-[11px] sm:py-8 py-4 rounded-10 [&>svg]:hidden group">
                <div className="flex items-center gap-x-6">
                  <div className="sm:size-10 size-7 rounded-full bg-sub items-center justify-center flex">
                    <QuestionIcon className="sm:w-[19px] sm:h-[21px] w-[13.5px] h-[14.5px] [&>path]:fill-purple01" />
                  </div>
                  <h3 className="sm:font-bold font-medium sm:text-xl text-base flex-1">
                    {f.question}
                  </h3>
                </div>
                <Plus className="!block sm:!size-8 !size-6 group-data-[state=open]:!hidden" />
                <Minus className="!hidden sm:!size-10 !size-[30px] group-data-[state=open]:!block" />
              </AccordionTrigger>
              <AccordionContent className="sm:px-10 px-[11px] sm:py-10 py-8">
                <div className="flex items-start gap-x-6">
                  <div className="size-7 sm:size-10 rounded-full items-center justify-center flex bg-sub min-w-[1.75rem] sm:min-w-[2.5rem]">
                    <AnswerIcon className="w-[13px] h-[14px] sm:w-[19px] sm:h-[21px] [&>path]:fill-blue02" />
                  </div>
                  <p>{f.answer}</p>
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </Container>
    </div>
  );
};
