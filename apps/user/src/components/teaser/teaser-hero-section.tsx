import { buttonVariants } from "@vtuber/ui/components/button";
import { Container } from "@vtuber/ui/components/container";
import { GiftIcon } from "@vtuber/ui/components/icons/gift-icon";
import { StarIcon } from "@vtuber/ui/components/icons/star-icon";
import { YenIcon } from "@vtuber/ui/components/icons/yen-icon";
import { Image } from "@vtuber/ui/components/image";
import { Logo } from "@vtuber/ui/components/logo";
import { cn } from "@vtuber/ui/lib/utils";
import { TeaserHeroBg } from "./teaser-hero-bg";

const priceList = [
  {
    title: "業界最安クラス",
    price: (
      <p className="font-bold">
        利用手数料 <span className="sm:text-[33px] text-2xl">0</span>
        <span className="text-xs">円</span>
        <span className="text-[8px]">※</span>
      </p>
    ),
    icon: <YenIcon />,
  },
  {
    title: "VTuberの手間なく",
    price: (
      <p className="font-bold">
        モデル制作を <span className="sm:text-xl">実現</span>
      </p>
    ),
    icon: <GiftIcon />,
  },
  {
    title: "業界最安クラス",
    price: <p className="font-bold sm:text-xl">“共創” 体験</p>,
    icon: <StarIcon />,
  },
];

export const TeaserHeroSection = () => {
  return (
    <div className="relative">
      <div
        className="absolute bottom-24 -right-[500px] w-[816px] h-1 sm:hidden block z-40 rotate-[-30deg] bg-[#207DF8]"
        style={{
          filter: "drop-shadow(0px 0px 9px #FFFFFF)",
          WebkitFilter: "drop-shadow(0px 0px 9px #FFFFFF)",
          msFilter: "drop-shadow(0px 0px 9px #FFFFFF)",
        }}
      />
      <div className="relative overflow-hidden min-h-screen flex items-center justify-center lg:pt-0 pt-16">
        <TeaserHeroBg />
        <Container className="grid grid-cols-12 items-center relative z-40">
          <section className="grid gap-y-10 lg:col-span-8 col-span-12">
            <section>
              <div className="flex items-center">
                <Logo />
                <Image
                  src="https://cdn.v-sai.com/assets/-V%E7%A5%AD-.png"
                  alt="-V祭-"
                  className="h-[35px] w-[85px] object-contain"
                />
              </div>
              <div className="grid gap-y-[10px]">
                <div className="sm:text-[53px] text-[25px] font-bold text-[#F7F5F5]">
                  <h3>
                    <span
                      className="sm:text-[94px] text-[44px] italic tracking-[15%]"
                      style={{
                        filter:
                          "drop-shadow(0px 0px 2px #0C1542) drop-shadow(-4px 0px 0px #85F3FB) drop-shadow(4px 0px 0px #9775CD)",
                        WebkitFilter:
                          "drop-shadow(0px 0px 2px #0C1542) drop-shadow(-4px 0px 0px #85F3FB) drop-shadow(4px 0px 0px #9775CD)",
                      }}>
                      挑戦
                    </span>{" "}
                    したい気持ちに、
                  </h3>
                  <h3>
                    止まらない、
                    <span
                      style={{
                        filter:
                          "drop-shadow(0px 0px 2px #0C1542) drop-shadow(-4px 0px 0px #85F3FB) drop-shadow(4px 0px 0px #9775CD)",
                        WebkitFilter:
                          "drop-shadow(0px 0px 2px #0C1542) drop-shadow(-4px 0px 0px #85F3FB) drop-shadow(4px 0px 0px #9775CD)",
                      }}
                      className="sm:text-[84px] text-[44px] tracking-[15%] italic">
                      支援
                    </span>
                    を。
                  </h3>
                </div>
                <p className="lg:w-[679px] text-lg font-medium text-[#F7F5F5]">
                  100件以上のVTuberなどを手がけてきた2D/3DCG制作会社が運営する、
                  VTuberの夢を応援できるクラウドファンディングプラットフォーム。
                </p>
              </div>
            </section>
            <section className="grid gap-y-[42px]">
              <a
                href="https://v-sai.com"
                target="_blank"
                rel="noreferrer"
                className={cn(
                  buttonVariants(),
                  "bg-gradient-2 sm:text-lg font-bold sm:h-[64px] h-[61px] text-white sm:w-[320px] w-full",
                )}>
                公式サイトを見る
              </a>
              <div className="grid gap-y-2">
                <section className="flex sm:gap-x-6 gap-x-3 sm:justify-start justify-center">
                  {priceList.map((c, i) => (
                    <div
                      key={i}
                      className="flex flex-col justify-between items-center px-5 py-3 gap-y-[3px] h-full border border-[#082955] bg-gradient-to-r from-[#0C1746] to-[#0D0B2D]">
                      {c.icon}
                      <div className="space-y-1 text-center">
                        <h4 className="text-font sm:text-sm text-xs font-bold">
                          {c.title}
                        </h4>
                        <div className="text-font sm:text-base text-xs">
                          {c.price}
                        </div>
                      </div>
                    </div>
                  ))}
                </section>
                <p className="text-xs text-[#929191]">
                  ※リリース記念キャンペーンにより調達金額に関わらず利用手数料を0円にて実施となります。
                </p>
              </div>
            </section>
          </section>
          <section className="lg:col-span-4 col-span-12">
            <Image
              src="https://cdn.v-sai.com/assets/teaser_hero_image.png.png"
              alt="Teaser Hero Image"
            />
          </section>
        </Container>
      </div>
    </div>
  );
};
