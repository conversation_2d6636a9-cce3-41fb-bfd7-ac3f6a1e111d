import { useSuspenseInfiniteQuery } from "@tanstack/react-query";
import { useLanguage } from "@vtuber/language/hooks";
import useIntersectionObserver from "@vtuber/ui/hooks/use-intersection-observer";
import { vtuberPostQueryOptions } from "~/utils/api";
import { PostCard } from "../post/post-card";
import { PostCardSkeleton } from "../skeletons/post-card-skeleton";

interface Props {
  id?: string;
  vtuberId: bigint;
  hasSubscribed: boolean;
  campaignId?: bigint;
  isPlanActive: boolean;
}

export const PostList = ({
  hasSubscribed,
  vtuberId,
  isPlanActive,
  ...rest
}: Props) => {
  const { getText } = useLanguage();

  const { data, isFetchingNextPage, hasNextPage, fetchNextPage } =
    useSuspenseInfiniteQuery(
      vtuberPostQueryOptions({
        ...rest,
        size: 2,
      }),
    );

  const post = data.pages.flatMap((page) => page?.data || []);

  const postAvalaible = post && post.length > 0;

  const { ref: loader } = useIntersectionObserver({
    rootMargin: "100px",
    onChange: (intersecting) => {
      if (intersecting && hasNextPage && !isFetchingNextPage) {
        fetchNextPage();
      }
    },
  });

  return (
    <section>
      {postAvalaible ? (
        <div className="space-y-10">
          {post.map((p) => (
            <PostCard
              shouldBlur={p.membershipOnly && !hasSubscribed}
              key={p.id}
              post={p}
              vtuberId={vtuberId}
              isPlanActive={isPlanActive}
            />
          ))}
          {isFetchingNextPage && <PostCardSkeleton />}
          <div ref={loader} />
        </div>
      ) : (
        <div className="h-40 flex items-center justify-center text-3xl font-semibold">
          {getText("no_posts_available")}
        </div>
      )}
    </section>
  );
};
