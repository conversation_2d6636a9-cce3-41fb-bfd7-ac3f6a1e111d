import { Separator } from "@vtuber/ui/components/separator";
import { Skeleton } from "@vtuber/ui/components/skeleton";

interface Props {
  className?: string;
  length?: number;
}

export const CampaignSupportersSkeleton = ({
  className,
  length = 5,
}: Props) => {
  return Array.from({ length }).map((_, i) => (
    <div
      className="text-font grid gap-y-6"
      key={i}>
      <div className="grid gap-y-4">
        <div className="flex sm:items-center items-start sm:justify-between sm:flex-row flex-col sm:gap-y-0 gap-y-[11px]">
          <div className="flex items-center gap-x-3 flex-1">
            <Skeleton className="size-[45px] rounded-full" />
            <Skeleton className="h-3 w-[45%] rounded-none" />
          </div>
          <Skeleton className="h-3 w-[30%]" />
        </div>
        <div className="space-y-1">
          <Skeleton className="h-3 rounded-none w-full" />
          <Skeleton className="h-3 rounded-none w-[75%]" />
          <Skeleton className="h-3 rounded-none w-[50%]" />
        </div>
      </div>
      <Separator />
    </div>
  ));
};
