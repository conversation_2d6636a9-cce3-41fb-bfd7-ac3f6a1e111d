import { createFileRoute, useNavigate } from "@tanstack/react-router";
import { useLanguage } from "@vtuber/language/hooks";
import { Spinner } from "@vtuber/ui/components/spinner";
import { CAN_USE_DOM } from "@vtuber/ui/lib/utils";
import { useEffect } from "react";
import { z } from "zod";

export const Route = createFileRoute("/_auth/oauth-complete")({
  component: RouteComponent,
  validateSearch: z.object({
    token: z.string(),
    refreshToken: z.string(),
  }),
});

function RouteComponent() {
  const { getText } = useLanguage();
  const { refreshToken, token } = Route.useSearch();
  const navigate = useNavigate();

  useEffect(() => {
    if (CAN_USE_DOM) {
      setCookie("token", token);
      setCookie("refresh_token", refreshToken);
      navigate({
        to: "/",
      });
    }
  }, []);

  return (
    <div className="flex h-full w-full items-center justify-center flex-col gap-y-3">
      <Spinner />
      <p>{getText("redirecting_to_the_app")}</p>
    </div>
  );
}
