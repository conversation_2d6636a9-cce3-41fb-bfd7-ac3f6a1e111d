import { createFileRoute } from "@tanstack/react-router";
import { userDeliveryAddressClient } from "@vtuber/services/client";
import { DeliveryAddressForm } from "~/components/delivery-address-form";

export const Route = createFileRoute("/_protected/delivery-address")({
  component: RouteComponent,
  loader: async () => {
    const [res] = await userDeliveryAddressClient.getCurrentUserDeliveryAddress(
      {},
    );

    return res;
  },
});

function RouteComponent() {
  const data = Route.useLoaderData();

  return <DeliveryAddressForm data={data?.data} />;
}
