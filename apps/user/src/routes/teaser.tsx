import { createFileRoute } from "@tanstack/react-router";
import { AboutVfestival } from "~/components/teaser/about-v-festival";
import { Differences } from "~/components/teaser/differences";
import { Flow } from "~/components/teaser/flow";
import { Merits } from "~/components/teaser/merits";
import { Reasons } from "~/components/teaser/reasons";
import { TeaserBanner } from "~/components/teaser/teaser-banner";
import { TeaserFAQ } from "~/components/teaser/teaser-faq";
import { TeaserFooter } from "~/components/teaser/teaser-footer";
import { TeaserHeroSection } from "~/components/teaser/teaser-hero-section";
import { VCreated } from "~/components/teaser/v-created";

export const Route = createFileRoute("/teaser")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <div className="space-y-20 overflow-x-hidden">
      <TeaserHeroSection />
      <AboutVfestival />
      <Reasons />
      <TeaserBanner className="!mt-0" />
      <VCreated />
      <Merits />
      <Differences />
      <TeaserBanner />
      <Flow />
      <TeaserFAQ />
      <TeaserBanner className="!mt-0" />
      <TeaserFooter />
    </div>
  );
}
