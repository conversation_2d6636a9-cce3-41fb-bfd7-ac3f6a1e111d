import { useLanguage } from "@vtuber/language/hooks";
import { useEffect, useState } from "react";

export const useGetFaqTags = (lan?: string) => {
  const { language } = useLanguage();
  const [tags, setTags] = useState<{ label: string; value: string }[]>([]);

  const TagsEng = [
    { label: "Account/Registration", value: "Account/Registration" },
    { label: "Payment", value: "Payment" },
    { label: "Plan Purchase", value: "Plan Purchase" },
    { label: "Return", value: "Return" },
    { label: "Activity Report", value: "Activity Report" },
    { label: "Cancel", value: "Cancel" },
    { label: "Others", value: "Others" },
  ];

  const TagsJa = [
    { label: "アカウント/会員登録", value: "アカウント/会員登録" },
    { label: "お支払い", value: "お支払い" },
    { label: "プラン購入", value: "プラン購入" },
    { label: "リターン", value: "リターン" },
    { label: "活動報告", value: "活動報告" },
    { label: "キャンセル", value: "キャンセル" },
    { label: "その他", value: "その他" },
  ];

  useEffect(() => {
    // If lan parameter is provided, use it
    if (lan) {
      if (lan === "ja-jp") {
        setTags(TagsJa);
      } else {
        setTags(TagsEng);
      }
    }
    // Otherwise use the language from context
    else if (language === "ja") {
      setTags(TagsJa);
    } else {
      setTags(TagsEng);
    }
  }, [language, lan]);

  return tags;
};
