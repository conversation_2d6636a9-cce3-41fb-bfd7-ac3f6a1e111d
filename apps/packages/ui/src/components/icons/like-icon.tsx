import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const LikeIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="71"
        height="84"
        viewBox="0 0 71 84"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <g clipPath="url(#clip0_4023_1998)">
          <path
            d="M45.5671 8.64407L51.0813 10.0498L45.5671 11.4555C44.7501 11.6638 44.1122 12.3126 43.9074 13.1435L42.5251 18.7509L41.1427 13.1435C40.9379 12.3126 40.2999 11.6638 39.4829 11.4555L33.9688 10.0498L39.4829 8.64407C40.2999 8.43573 40.9379 7.78705 41.1427 6.9561L42.5251 1.34863L43.9074 6.9561C44.1122 7.78705 44.7501 8.43573 45.5671 8.64407Z"
            stroke="url(#paint0_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M62.1519 26.0834L69.6739 28.001L62.1519 29.9186C61.0373 30.2028 60.1671 31.0877 59.8876 32.2212L58.002 39.8706L56.1163 32.2212C55.8369 31.0877 54.9667 30.2028 53.8521 29.9186L46.3301 28.001L53.8521 26.0834C54.9667 25.7992 55.8369 24.9142 56.1163 23.7808L58.002 16.1313L59.8876 23.7808C60.1671 24.9142 61.0373 25.7992 62.1519 26.0834Z"
            stroke="url(#paint1_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M13.7578 80.2401H18.8194V50.5532H13.7578"
            stroke="url(#paint2_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M1.32617 81.7331H13.7583V48.9209H1.32617"
            stroke="url(#paint3_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M37.6019 46.9664L38.8551 37.4638C38.8551 30.5124 35.3454 27.2407 32.5619 27.2407C29.7784 27.2407 28.775 28.2609 28.775 31.0916V45.4753C28.775 46.888 28.1987 48.2371 27.1834 49.2012L22.8355 53.1022H18.8203"
            stroke="url(#paint4_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M18.8203 77.2739H25.9764L28.5423 79.8836C30.8484 82.2287 33.9016 82.651 36.3655 82.651"
            stroke="url(#paint5_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M36.3652 82.6509H52.1555C54.8798 82.6509 57.1087 80.3842 57.1087 77.6137C57.1087 76.43 56.6851 75.3526 56.0064 74.4903C58.442 74.1786 60.3488 72.0741 60.3488 69.5163C60.3488 67.9507 59.6224 66.5637 58.5118 65.6379C60.6723 65.0968 62.2928 63.1178 62.2928 60.76C62.2928 58.4019 60.6723 56.4231 58.5118 55.8818C59.6224 54.9561 60.3488 53.5689 60.3488 52.0035C60.3488 49.2331 58.1198 46.9663 55.3956 46.9663H37.8398"
            stroke="url(#paint6_linear_4023_1998)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_1998"
            x1="51.0813"
            y1="10.5176"
            x2="37.454"
            y2="16.9573"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_1998"
            x1="69.6739"
            y1="28.6392"
            x2="51.0843"
            y2="37.4237"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_1998"
            x1="18.8194"
            y1="66.1948"
            x2="13.9215"
            y2="66.5961"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_1998"
            x1="13.7583"
            y1="66.2091"
            x2="2.03602"
            y2="68.3435"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_1998"
            x1="38.8551"
            y1="40.8667"
            x2="21.7137"
            y2="47.2482"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4023_1998"
            x1="36.3655"
            y1="80.107"
            x2="31.4238"
            y2="87.8558"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint6_linear_4023_1998"
            x1="62.2928"
            y1="65.7679"
            x2="39.7798"
            y2="73.6285"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_1998">
            <rect
              width="71"
              height="84"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

LikeIcon.displayName = "LikeIcon";
