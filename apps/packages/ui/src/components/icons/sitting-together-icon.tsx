import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const SittingTogetherIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="81"
        height="68"
        viewBox="0 0 81 68"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...rest}
        ref={ref}
        className={className}>
        <g clipPath="url(#clip0_4023_1117)">
          <path
            d="M18.2689 10.5729C19.1756 6.71105 16.7844 2.84429 12.9281 1.93629C9.07175 1.02829 5.21054 3.42288 4.30384 7.28477C3.39713 11.1467 5.7883 15.0134 9.64465 15.9214C13.501 16.8294 17.3622 14.4348 18.2689 10.5729Z"
            stroke="url(#paint0_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M17.8637 36.8029V42.001L27.4327 43.3593C29.2013 43.6107 30.5155 45.1263 30.5155 46.9152V66.6761H23.9086V50.4136H17.8637H10.6867C7.38527 50.4136 4.70898 47.7335 4.70898 44.4273V26.0888C4.70898 22.7827 7.38527 20.1025 10.6867 20.1025H11.8868C15.1882 20.1025 17.8645 22.7827 17.8645 26.0888V31.1418H24.6423C26.2027 31.1418 27.4684 32.4085 27.4684 33.9719C27.4684 35.5354 26.2027 36.8029 24.6423 36.8029H15.798C13.8173 36.8029 12.2115 35.1948 12.2115 33.2113V27.5104"
            stroke="url(#paint1_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M74.7102 14.0085C77.5115 11.2033 77.5115 6.65511 74.7102 3.84987C71.909 1.04464 67.3674 1.04464 64.5662 3.84988C61.765 6.65511 61.765 11.2033 64.5662 14.0085C67.3674 16.8138 71.909 16.8138 74.7102 14.0085Z"
            stroke="url(#paint2_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M63.06 36.8029V42.001L53.491 43.3593C51.7225 43.6107 50.4082 45.1263 50.4082 46.9152V66.6761H57.0151V50.4136H63.06H70.237C73.5385 50.4136 76.2148 47.7335 76.2148 44.4273V26.0888C76.2148 22.7827 73.5385 20.1025 70.237 20.1025H69.037C65.7355 20.1025 63.0592 22.7827 63.0592 26.0888V31.1418H56.2815C54.721 31.1418 53.4554 32.4085 53.4554 33.9719C53.4554 35.5354 54.721 36.8029 56.2815 36.8029H65.1258C67.1065 36.8029 68.7122 35.1948 68.7122 33.2113V27.5104"
            stroke="url(#paint3_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M78.811 54.7813H69.6379H60.4648V50.4136H78.811V54.7813Z"
            stroke="url(#paint4_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M69.6387 66.6752V54.7812"
            stroke="url(#paint5_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M40.4629 66.6756V41.1709"
            stroke="url(#paint6_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M56.3865 36.8032H24.5391V41.1709H56.3865V36.8032Z"
            stroke="url(#paint7_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M20.4594 54.7813H11.2864H2.11328V50.4136H20.4594V54.7813Z"
            stroke="url(#paint8_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M11.2871 66.6752V54.7812"
            stroke="url(#paint9_linear_4023_1117)"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_1117"
            x1="13.3035"
            y1="2.02469"
            x2="16.0235"
            y2="14.3414"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_1117"
            x1="30.5155"
            y1="44.6414"
            x2="7.04026"
            y2="50.8923"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_1117"
            x1="74.983"
            y1="4.12298"
            x2="70.8021"
            y2="16.0222"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_1117"
            x1="76.2148"
            y1="44.6414"
            x2="52.7395"
            y2="50.8923"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_1117"
            x1="78.811"
            y1="52.7148"
            x2="75.289"
            y2="59.8241"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4023_1117"
            x1="70.6387"
            y1="61.048"
            x2="69.6661"
            y2="61.0873"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint6_linear_4023_1117"
            x1="41.4629"
            y1="54.6089"
            x2="40.4891"
            y2="54.6273"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint7_linear_4023_1117"
            x1="56.3865"
            y1="39.1045"
            x2="54.05"
            y2="47.2917"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint8_linear_4023_1117"
            x1="20.4594"
            y1="52.7148"
            x2="16.9374"
            y2="59.8241"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint9_linear_4023_1117"
            x1="12.2871"
            y1="61.048"
            x2="11.3145"
            y2="61.0873"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_1117">
            <rect
              width="79.1275"
              height="67.3637"
              fill="white"
              transform="translate(0.898438 0.528809)"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

SittingTogetherIcon.displayName = "SittingTogetherIcon";
