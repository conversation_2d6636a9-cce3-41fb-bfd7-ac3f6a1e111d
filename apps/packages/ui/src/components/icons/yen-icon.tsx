import { forwardRef } from "react";

export const YenIcon = forwardRef<SVGSVGElement, React.ComponentProps<"svg">>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="36"
        height="37"
        viewBox="0 0 36 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...rest}
        ref={ref}
        className={className}>
        <path
          d="M22.773 18.8354H18M18 18.8354H13.227M18 18.8354L12.75 11.3354M18 18.8354L23.25 11.3354M18 18.8354V22.5854M18 22.5854H22.773M18 22.5854H13.227M18 22.5854V26.3354M31.5 18.0854C31.5 19.8583 31.1508 21.6138 30.4724 23.2517C29.7939 24.8896 28.7995 26.3778 27.5459 27.6314C26.2923 28.885 24.8041 29.8794 23.1662 30.5578C21.5283 31.2363 19.7728 31.5854 18 31.5854C16.2272 31.5854 14.4717 31.2363 12.8338 30.5578C11.1959 29.8794 9.70765 28.885 8.45406 27.6314C7.20047 26.3778 6.20606 24.8896 5.52763 23.2517C4.84919 21.6138 4.5 19.8583 4.5 18.0854C4.5 14.505 5.92232 11.0712 8.45406 8.53951C10.9858 6.00777 14.4196 4.58545 18 4.58545C21.5804 4.58545 25.0142 6.00777 27.5459 8.53951C30.0777 11.0712 31.5 14.505 31.5 18.0854Z"
          stroke="url(#paint0_linear_2701_50510)"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <defs>
          <linearGradient
            id="paint0_linear_2701_50510"
            x1="31.5"
            y1="18.8113"
            x2="10.1321"
            y2="29.0797"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
        </defs>
      </svg>
    );
  },
);
YenIcon.displayName = "YenIcon";
