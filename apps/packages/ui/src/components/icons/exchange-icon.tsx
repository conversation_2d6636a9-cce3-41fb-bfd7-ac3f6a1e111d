import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const ExchangeIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="282"
        height="82"
        viewBox="0 0 282 82"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <path
          d="M1 28.5549H277.619C278.615 28.5549 278.999 27.2577 278.163 26.7159L238.5 1"
          stroke="#85F3FB"
          strokeWidth="2"
          strokeLinecap="round"
        />
        <path
          d="M281 52.5549H4.38055C3.38452 52.5549 3.00079 53.8521 3.83654 54.394L43.5 80.1099"
          stroke="#9376CD"
          strokeWidth="2"
          strokeLinecap="round"
        />
      </svg>
    );
  },
);

ExchangeIcon.displayName = "ExchangeIcon";
