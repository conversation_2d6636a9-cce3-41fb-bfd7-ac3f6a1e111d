import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const StampIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="81"
        height="72"
        viewBox="0 0 81 72"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <g clipPath="url(#clip0_4023_3486)">
          <path
            d="M55.3944 38.3152V70.6819H1.29688V1.31787H55.3944V7.14007"
            stroke="url(#paint0_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10.6211 31.106H46.0705"
            stroke="url(#paint1_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10.6211 39.9072H46.0705"
            stroke="url(#paint2_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10.6211 48.7085H46.0705"
            stroke="url(#paint3_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M10.6211 57.5093H28.3458"
            stroke="url(#paint4_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M21.5414 10.5273H10.6211V21.6281H21.5414V10.5273Z"
            stroke="url(#paint5_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M71.3046 17.7864C74.5785 23.0182 75.1434 28.5333 72.2875 31.4364C68.619 35.1655 60.717 33.1593 54.626 26.9677C48.5349 20.776 46.5614 12.7434 50.2299 9.01421C53.0858 6.11119 58.5112 6.68549 63.658 10.0134"
            stroke="url(#paint6_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M65.0511 22.51C64.0477 23.5299 61.8816 22.9818 60.213 21.2856C58.5445 19.5895 58.0052 17.3876 59.0087 16.3676C59.0087 16.3676 64.7305 8.63684 68.1122 3.71966C70.1684 0.729574 75.2794 0.812081 77.7632 3.44543C80.3539 5.97036 80.4349 11.1658 77.4935 13.2561C72.6562 16.6935 65.0511 22.51 65.0511 22.51Z"
            stroke="url(#paint7_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M49.6734 9.57715L44.2508 15.0892C40.5878 18.8129 42.5562 26.851 48.6475 33.043C54.7388 39.2349 62.6463 41.2359 66.3095 37.5123L71.7319 32.0002"
            stroke="url(#paint8_linear_4023_3486)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_3486"
            x1="55.3944"
            y1="37.8647"
            x2="9.18543"
            y2="55.1832"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_3486"
            x1="46.0705"
            y1="31.6328"
            x2="45.9519"
            y2="33.6531"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_3486"
            x1="46.0705"
            y1="40.4341"
            x2="45.9519"
            y2="42.4543"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_3486"
            x1="46.0705"
            y1="49.2354"
            x2="45.9519"
            y2="51.2556"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_3486"
            x1="28.3458"
            y1="58.0362"
            x2="28.1111"
            y2="60.0358"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4023_3486"
            x1="21.5414"
            y1="16.3762"
            x2="12.8465"
            y2="20.4866"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint6_linear_4023_3486"
            x1="74.1312"
            y1="20.9297"
            x2="53.6314"
            y2="30.6208"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint7_linear_4023_3486"
            x1="79.7035"
            y1="12.8274"
            x2="62.824"
            y2="20.8071"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint8_linear_4023_3486"
            x1="71.7319"
            y1="25.2826"
            x2="48.3838"
            y2="36.3202"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_3486">
            <rect
              width="81"
              height="72"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

StampIcon.displayName = "StampIcon";
