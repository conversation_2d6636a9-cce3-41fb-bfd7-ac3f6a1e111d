import { forwardRef } from "react";

export const GiftIcon = forwardRef<SVGSVGElement, React.ComponentProps<"svg">>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="36"
        height="37"
        viewBox="0 0 36 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <path
          d="M30.4724 23.2517C31.1508 21.6138 31.5 19.8583 31.5 18.0854C31.5 14.505 30.0777 11.0712 27.5459 8.53951C25.0142 6.00777 21.5804 4.58545 18 4.58545C14.4196 4.58545 10.9858 6.00777 8.45406 8.53951C5.92232 11.0712 4.5 14.505 4.5 18.0854C4.5 19.8583 4.84919 21.6138 5.52763 23.2517C6.20606 24.8896 7.20047 26.3778 8.45406 27.6314C9.70765 28.885 11.1959 29.8794 12.8338 30.5578C14.4717 31.2363 16.2272 31.5854 18 31.5854C19.7728 31.5854 21.5283 31.2363 23.1662 30.5578C24.8041 29.8794 26.2923 28.885 27.5459 27.6314C28.7995 26.3778 29.7939 24.8896 30.4724 23.2517Z"
          stroke="url(#paint0_linear_2701_50516)"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <g clip-path="url(#clip0_2701_50516)">
          <path
            d="M25 15.3813C25 14.898 24.6082 14.5063 24.125 14.5063H22.6314C23.0179 14.125 23.2653 13.6251 23.2653 13.0096C23.2653 12.2516 22.83 11.4282 21.6081 11.4282C20.0002 11.4282 18.6571 13.1402 18.026 14.0998C17.3945 13.1404 16.0103 11.4284 14.4027 11.4284C13.1807 11.4284 12.7454 12.2518 12.7454 13.0098C12.7454 13.6251 12.9996 14.1252 13.394 14.5065H11.875C11.3918 14.5065 11 14.8983 11 15.3815V18.0006H11.8827V23.6957C11.8827 24.1789 12.2744 24.5707 12.7577 24.5707H23.2533C23.7365 24.5707 24.1283 24.1789 24.1283 23.6957V18.0003H25L25 15.3813ZM21.6081 12.3034C22.149 12.3034 22.3903 12.5307 22.3903 13.0098C22.3903 13.9817 21.3055 14.506 20.2853 14.506H18.8041C19.4258 13.5827 20.52 12.3034 21.6081 12.3034ZM14.4026 12.3035C15.4909 12.3035 16.6264 13.5827 17.2481 14.5058H15.7665C14.7463 14.5058 13.6204 13.9679 13.6204 12.996C13.6204 12.5172 13.8617 12.3035 14.4026 12.3035ZM24.125 17.1253H18.4375V15.3813H24.125V17.1253ZM11.875 15.3813H17.5625V17.1253H11.875V15.3813ZM12.7576 18.0003H17.5625V23.6955H12.7576V18.0003ZM23.2535 23.6955H18.4375V18.0003H23.2535V23.6955Z"
            fill="url(#paint1_linear_2701_50516)"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_2701_50516"
            x1="31.5"
            y1="18.8113"
            x2="10.1321"
            y2="29.0797"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_2701_50516"
            x1="25"
            y1="18.3528"
            x2="14.1936"
            y2="23.8847"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_2701_50516">
            <rect
              width="14"
              height="14"
              fill="white"
              transform="translate(11 10.9995)"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);
GiftIcon.displayName = "GiftIcon";
