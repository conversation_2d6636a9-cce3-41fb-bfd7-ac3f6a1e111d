import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const WritingIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="80"
        height="71"
        viewBox="0 0 80 71"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <g clipPath="url(#clip0_4023_2282)">
          <path
            d="M18.5029 54.0923L12.5938 64.4295"
            stroke="url(#paint0_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.93164 59.9593L38.5035 1.20947L41.1401 1.36593L44.7039 3.44014L46.1643 5.67888L23.5601 45.2309"
            stroke="url(#paint1_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M12.5965 64.431L4.42578 69.7899L4.93331 59.96"
            stroke="url(#paint2_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M4.93359 59.96L7.57816 60.1205L11.1364 62.1963L12.5968 64.431"
            stroke="url(#paint3_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M42.2869 40.709L39.8666 43.2969C38.6385 44.5381 36.9738 45.2348 35.2374 45.2348H20.879C18.2871 45.2348 16.166 47.3784 16.166 49.9978C16.166 52.618 18.2871 54.7608 20.879 54.7608H31.4389C33.3086 54.7608 35.0746 55.5761 36.3275 56.9793C38.8547 59.8092 45.9298 64.1633 56.2902 59.5769H64.7338"
            stroke="url(#paint4_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M64.7349 35.5823L47.1326 23.4466C44.7242 21.7861 41.8068 21.0547 38.9092 21.3845L37.3906 21.5571"
            stroke="url(#paint5_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M18.8615 35.5824L10.0915 50.8479C8.853 52.9931 6.20045 54.01 3.96366 52.981C1.3965 51.7995 0.450871 48.6439 1.83779 46.2422L11.9612 27.9985C13.0904 25.9638 15.0431 24.5291 17.307 24.0718L26.4992 22.2153"
            stroke="url(#paint6_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M39.8672 43.2971L36.0081 38.3736C35.0848 37.1954 33.6787 36.5083 32.1912 36.5083H28.8867"
            stroke="url(#paint7_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M69.6393 61.7464H64.9941V33.9243H69.6393"
            stroke="url(#paint8_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M78.8029 63.5562H69.6387V31.9453H78.6784"
            stroke="url(#paint9_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M23.7603 54.7617L23.6565 57.5561C23.615 60.04 21.7932 62.2376 19.3441 62.4457C17.6204 62.5916 16.0699 61.79 15.1387 60.5061"
            stroke="url(#paint10_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M31.7108 55.1714C31.6757 56.177 31.3326 57.1658 30.6535 57.9787C28.8324 60.1561 25.571 60.2206 23.6582 58.2214"
            stroke="url(#paint11_linear_4023_2282)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_2282"
            x1="18.5029"
            y1="59.5388"
            x2="13.1504"
            y2="61.0092"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_2282"
            x1="46.1643"
            y1="32.1639"
            x2="10.0994"
            y2="44.3274"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_2282"
            x1="12.5965"
            y1="65.1392"
            x2="5.73212"
            y2="67.8811"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_2282"
            x1="12.5968"
            y1="62.3157"
            x2="8.14899"
            y2="65.9791"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_2282"
            x1="64.7338"
            y1="51.7039"
            x2="43.7144"
            y2="75.2129"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4023_2282"
            x1="64.7349"
            y1="28.8285"
            x2="50.3153"
            y2="42.1018"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint6_linear_4023_2282"
            x1="26.4992"
            y1="38.6382"
            x2="5.10752"
            y2="46.9821"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint7_linear_4023_2282"
            x1="39.8672"
            y1="40.0852"
            x2="33.199"
            y2="45.2682"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint8_linear_4023_2282"
            x1="69.6393"
            y1="48.5833"
            x2="65.1431"
            y2="48.9441"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint9_linear_4023_2282"
            x1="78.8029"
            y1="48.6006"
            x2="70.0454"
            y2="49.8206"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint10_linear_4023_2282"
            x1="23.7603"
            y1="58.8193"
            x2="17.2467"
            y2="62.3235"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint11_linear_4023_2282"
            x1="31.7108"
            y1="57.5408"
            x2="27.2037"
            y2="61.4192"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_2282">
            <rect
              width="80"
              height="71"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

WritingIcon.displayName = "WritingIcon";
