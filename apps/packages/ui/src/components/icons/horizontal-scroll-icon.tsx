import { forwardRef } from "react";

export const HorizontalScrollIcon = forwardRef<
  SVGSVGElement,
  React.ComponentProps<"svg">
>(({ className, ...rest }, ref) => {
  return (
    <svg
      width="64"
      height="64"
      viewBox="0 0 64 64"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...rest}
      ref={ref}
      className={className}>
      <path
        d="M46.8762 58.6396C46.7607 54.3035 47.0473 53.7822 47.3518 52.8166C47.654 51.851 49.7805 48.3739 50.5315 45.8874C52.9647 37.8452 50.696 36.1339 47.674 33.8536C44.3231 31.3263 37.9991 30.0456 34.8638 30.3222V17.2866C34.8638 16.2381 34.4555 15.2327 33.7288 14.4913C33.002 13.75 32.0163 13.3335 30.9885 13.3335C29.9608 13.3335 28.9751 13.75 28.2483 14.4913C27.5216 15.2327 27.1133 16.2381 27.1133 17.2866V40.5426L22.5358 35.6012C21.1137 34.0282 18.8295 33.8695 17.2696 35.2998C16.5562 35.9527 16.1094 36.8551 16.0176 37.8283C15.9258 38.8014 16.1958 39.7742 16.7741 40.5539L19.6449 44.4367M19.6449 44.4367C20.3542 45.3741 21.0535 46.3194 21.7426 47.2723M19.6449 44.4367L21.7426 47.2723M19.6449 44.4367C18.3762 42.7548 17.4184 41.5331 16.5918 40.3204M26.8244 58.6668L26.78 56.2868C26.8777 53.4943 24.8868 51.6742 22.2914 48.0339L21.7426 47.2723M21.7426 47.2723L24.3424 50.7834"
        stroke="#F7F5F5"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 8.55521L23.5955 8.55521M10 8.55521C10 10.1107 14.5175 13.0171 15.6644 14.1104M10 8.55521C10 6.99977 14.5175 4.09111 15.6644 3.00008"
        stroke="#F7F5F5"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M51.5957 8.55521L38.0002 8.55521M51.5957 8.55521C51.5957 10.1107 47.0782 13.0171 45.9313 14.1104M51.5957 8.55521C51.5957 6.99977 47.0782 4.09111 45.9313 3.00008"
        stroke="#F7F5F5"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
});
HorizontalScrollIcon.displayName = "HorizontalScrollIcon";
