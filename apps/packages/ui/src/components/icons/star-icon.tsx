import { forwardRef } from "react";

export const StarIcon = forwardRef<SVGSVGElement, React.ComponentProps<"svg">>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="36"
        height="37"
        viewBox="0 0 36 37"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        {...rest}
        ref={ref}
        className={className}>
        <path
          d="M30.4724 23.2517C31.1508 21.6138 31.5 19.8583 31.5 18.0854C31.5 14.505 30.0777 11.0712 27.5459 8.53951C25.0142 6.00777 21.5804 4.58545 18 4.58545C14.4196 4.58545 10.9858 6.00777 8.45406 8.53951C5.92232 11.0712 4.5 14.505 4.5 18.0854C4.5 19.8583 4.84919 21.6138 5.52763 23.2517C6.20606 24.8896 7.20047 26.3778 8.45406 27.6314C9.70765 28.885 11.1959 29.8794 12.8338 30.5578C14.4717 31.2363 16.2272 31.5854 18 31.5854C19.7728 31.5854 21.5283 31.2363 23.1662 30.5578C24.8041 29.8794 26.2923 28.885 27.5459 27.6314C28.7995 26.3778 29.7939 24.8896 30.4724 23.2517Z"
          stroke="url(#paint0_linear_2701_50524)"
          strokeWidth="1.5"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M17.9997 10.9995L15.8601 15.5967L11 16.3379L14.5206 19.961L13.6789 24.9995L18 22.5753L22.3208 24.9995L21.486 19.9614L25 16.3383L20.1668 15.5967L17.9997 10.9995Z"
          stroke="url(#paint1_linear_2701_50524)"
          strokeLinejoin="round"
        />
        <defs>
          <linearGradient
            id="paint0_linear_2701_50524"
            x1="31.5"
            y1="18.8113"
            x2="10.1321"
            y2="29.0797"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_2701_50524"
            x1="25"
            y1="18.3759"
            x2="13.9204"
            y2="23.7002"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
        </defs>
      </svg>
    );
  },
);
StarIcon.displayName = "StarIcon";
