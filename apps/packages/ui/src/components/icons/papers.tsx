import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const PapersIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="73"
        height="82"
        viewBox="0 0 73 82"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <g clipPath="url(#clip0_4023_3327)">
          <path
            d="M64.095 24.7129L71.7951 32.4549L41.6728 62.7411L25.0536 63.1963L1.20508 39.2181C1.20508 39.2181 5.40671 36.7174 10.5983 31.4976C15.7839 26.2838 40.7206 1.21143 40.7206 1.21143L48.4208 8.95342"
            stroke="url(#paint0_linear_4023_3327)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M60.2177 17.3785L61.3038 15.5862C62.8335 15.8891 64.4805 15.4454 65.6658 14.2537C67.556 12.3532 67.556 9.27203 65.6659 7.37165C63.7758 5.47126 60.7111 5.4711 58.8209 7.37157C57.6357 8.5633 57.1944 10.2194 57.4958 11.7574L55.7129 12.8493L50.127 7.23305L43.9863 13.4071L59.663 29.169L65.8037 22.995L60.2177 17.3785Z"
            stroke="url(#paint1_linear_4023_3327)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M25.0566 63.1944L33.5874 54.6172L41.6708 62.7444L25.0566 63.1944Z"
            stroke="url(#paint2_linear_4023_3327)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M69.2235 35.0405L71.7891 37.6201L41.6668 67.9062C36.4812 73.12 32.2856 75.6268 32.2856 75.6268L1.20508 44.3772C1.20508 44.3772 2.3702 43.6792 4.23557 42.265"
            stroke="url(#paint3_linear_4023_3327)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M69.2235 40.2021L71.7891 42.7817L41.6668 73.0679C36.4812 78.2816 32.2856 80.7884 32.2856 80.7884L1.20508 49.5388C1.20508 49.5388 2.3702 48.8408 4.23557 47.4266"
            stroke="url(#paint4_linear_4023_3327)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_3327"
            x1="71.7952"
            y1="33.8703"
            x2="18.8778"
            y2="62.8301"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_3327"
            x1="67.0835"
            y1="18.182"
            x2="48.7673"
            y2="26.9363"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_3327"
            x1="41.6708"
            y1="59.1364"
            x2="32.9993"
            y2="67.2081"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_3327"
            x1="71.7891"
            y1="56.4248"
            x2="31.305"
            y2="90.2587"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_3327"
            x1="71.7891"
            y1="61.5864"
            x2="31.3051"
            y2="95.4203"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_3327">
            <rect
              width="73"
              height="82"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

PapersIcon.displayName = "PapersIcon";
