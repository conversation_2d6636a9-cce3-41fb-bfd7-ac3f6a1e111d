import { forwardRef } from "react";

type Props = React.ComponentProps<"svg">;

export const HandShakeIcon = forwardRef<SVGSVGElement, Props>(
  ({ className, ...rest }, ref) => {
    return (
      <svg
        width="91"
        height="57"
        viewBox="0 0 91 57"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
        className={className}
        ref={ref}
        {...rest}>
        <g clipPath="url(#clip0_4023_3039)">
          <path
            d="M68.08 7.4266L58.8408 9.7577C57.5362 10.0867 56.1636 10.0516 54.8777 9.65584L48.4416 7.6754C45.8919 6.89058 43.1093 7.55017 41.2051 9.39117L34.2102 16.1515C32.7305 17.5817 32.7568 19.9312 34.2672 21.3297H34.2689C35.6909 22.6472 37.9148 22.6347 39.3223 21.303L45.7805 15.1905L53.1173 18.885"
            stroke="url(#paint0_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M89.7247 25.491L74.6957 29.4444L68.0801 5.20579L83.1091 1.25244"
            stroke="url(#paint1_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M73.4188 27.7295L67.8008 32.5094"
            stroke="url(#paint2_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M22.9219 7.42676L32.9172 9.94905C33.737 10.1561 34.5883 10.2196 35.4311 10.1377L41.0083 9.59504"
            stroke="url(#paint3_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M1.27734 25.491L16.3064 29.4444L22.922 5.20579L7.89295 1.25244"
            stroke="url(#paint4_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M17.582 27.7295L22.808 34.4164"
            stroke="url(#paint5_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M67.7484 33.25L69.4875 34.9574C70.9698 36.4135 70.9698 38.773 69.4875 40.2291L69.4858 40.2308C68.0026 41.6869 65.5993 41.6869 64.1161 40.2308L62.377 38.5234"
            stroke="url(#paint6_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M57.8095 34.0396L64.1164 40.2305C65.5996 41.6866 65.5996 44.0469 64.1164 45.5022L64.1147 45.5038C62.6324 46.9599 60.2282 46.9599 58.745 45.5038L57.0059 43.7964"
            stroke="url(#paint7_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M52.5379 39.4106L57.7707 44.5479C59.2539 46.004 59.2539 48.3635 57.7707 49.8196L57.769 49.8213C56.2858 51.2774 53.8825 51.2774 52.3993 49.8213L50.6602 48.1139"
            stroke="url(#paint8_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M46.8422 44.3643L51.427 48.8653C52.9102 50.3214 52.9102 52.6809 51.427 54.137L51.4253 54.1387C49.9429 55.5948 47.5387 55.5948 46.0556 54.1387L44.3164 52.4313"
            stroke="url(#paint9_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M67.7499 33.2501L53.1172 18.8853"
            stroke="url(#paint10_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M21.651 35.5537L20.0531 37.1225C18.6906 38.4601 18.6906 40.6284 20.0531 41.9659L20.0548 41.9676C21.4172 43.3051 23.6258 43.3051 24.9882 41.9676L26.5862 40.3988"
            stroke="url(#paint11_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M26.5866 40.3975L24.9886 41.9663C23.6262 43.3038 23.6262 45.4721 24.9886 46.8096L24.9903 46.8113C26.3527 48.1489 28.5613 48.1489 29.9237 46.8113L31.5217 45.2425"
            stroke="url(#paint12_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M32.4147 44.3643L30.8167 45.9331C29.4543 47.2706 29.4543 49.4389 30.8167 50.7764L30.8184 50.7781C32.1808 52.1157 34.3894 52.1157 35.7519 50.7781L37.3498 49.2093"
            stroke="url(#paint13_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M38.2428 48.3311L36.6449 49.8999C35.2824 51.2374 35.2824 53.4057 36.6449 54.7432L36.6466 54.7449C38.009 56.0825 40.2176 56.0825 41.58 54.7449L43.178 53.1761"
            stroke="url(#paint14_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M21.6504 35.5534L23.2484 33.9846C24.6108 32.6471 26.8194 32.6471 28.1818 33.9846L28.1835 33.9863C29.5459 35.3238 29.5459 37.4921 28.1835 38.8296L26.5855 40.3985"
            stroke="url(#paint15_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M26.5859 40.3977L28.1839 38.8288C29.5463 37.4913 31.7549 37.4913 33.1174 38.8288L33.1191 38.8305C34.4815 40.1681 34.4815 42.3363 33.1191 43.6739L31.5211 45.2427"
            stroke="url(#paint16_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M31.7109 45.0549L33.3089 43.4861C34.6713 42.1485 36.8799 42.1485 38.2424 43.4861L38.2441 43.4877C39.6065 44.8253 39.6065 46.9936 38.2441 48.3311L36.6461 49.8999"
            stroke="url(#paint17_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
          <path
            d="M36.6465 49.9001L38.2445 48.3313C39.6069 46.9937 41.8155 46.9937 43.1779 48.3313L43.1796 48.333C44.542 49.6705 44.542 51.8388 43.1796 53.1763L41.5816 54.7451"
            stroke="url(#paint18_linear_4023_3039)"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          />
        </g>
        <defs>
          <linearGradient
            id="paint0_linear_4023_3039"
            x1="68.08"
            y1="15.2322"
            x2="53.0187"
            y2="32.1477"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint1_linear_4023_3039"
            x1="89.7247"
            y1="16.1063"
            x2="71.1657"
            y2="22.9537"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint2_linear_4023_3039"
            x1="73.4188"
            y1="30.248"
            x2="69.2696"
            y2="32.5915"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint3_linear_4023_3039"
            x1="41.0083"
            y1="8.87336"
            x2="39.4096"
            y2="13.9341"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint4_linear_4023_3039"
            x1="22.922"
            y1="16.1063"
            x2="4.36295"
            y2="22.9537"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint5_linear_4023_3039"
            x1="22.808"
            y1="31.2527"
            x2="18.3464"
            y2="32.9283"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint6_linear_4023_3039"
            x1="70.5993"
            y1="37.5035"
            x2="64.1374"
            y2="40.6662"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint7_linear_4023_3039"
            x1="65.2288"
            y1="40.6553"
            x2="57.9402"
            y2="42.9491"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint8_linear_4023_3039"
            x1="58.8831"
            y1="45.4712"
            x2="51.7182"
            y2="47.9326"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint9_linear_4023_3039"
            x1="52.5394"
            y1="50.0896"
            x2="45.4645"
            y2="52.6624"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint10_linear_4023_3039"
            x1="67.7499"
            y1="26.4538"
            x2="56.2508"
            y2="32.0829"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint11_linear_4023_3039"
            x1="26.5862"
            y1="39.4616"
            x2="20.649"
            y2="42.3678"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint12_linear_4023_3039"
            x1="31.5217"
            y1="44.3054"
            x2="25.5845"
            y2="47.2115"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint13_linear_4023_3039"
            x1="37.3498"
            y1="48.2722"
            x2="31.4127"
            y2="51.1783"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint14_linear_4023_3039"
            x1="43.178"
            y1="52.239"
            x2="37.2408"
            y2="55.1451"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint15_linear_4023_3039"
            x1="29.2053"
            y1="36.8894"
            x2="23.2681"
            y2="39.7955"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint16_linear_4023_3039"
            x1="34.1409"
            y1="41.7336"
            x2="28.2037"
            y2="44.6398"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint17_linear_4023_3039"
            x1="39.2659"
            y1="46.3908"
            x2="33.3287"
            y2="49.297"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <linearGradient
            id="paint18_linear_4023_3039"
            x1="44.2014"
            y1="51.236"
            x2="38.2642"
            y2="54.1422"
            gradientUnits="userSpaceOnUse">
            <stop stopColor="#9376CD" />
            <stop
              offset="1"
              stopColor="#85F3FB"
            />
          </linearGradient>
          <clipPath id="clip0_4023_3039">
            <rect
              width="91"
              height="57"
              fill="white"
            />
          </clipPath>
        </defs>
      </svg>
    );
  },
);

HandShakeIcon.displayName = "HandShakeIcon";
