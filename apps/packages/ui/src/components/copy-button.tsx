import { Check, Copy } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";
import { CAN_USE_DOM, cn } from "../lib/utils";
import { Button, ButtonProps } from "./button";
import { Tooltip } from "./tooltip";

type Props = Omit<ButtonProps, "children" | "onClick"> & {
  children?: React.ReactNode;
  textToCopy: string;
  tooltipLabel?: string;
};

export const CopyButton = ({
  children,
  textToCopy,
  tooltipLabel,
  ...props
}: Props) => {
  const [copied, setCopied] = useState(false);
  const [open, setOpen] = useState(false);

  const onCopy = async () => {
    if (!CAN_USE_DOM) return;
    try {
      setCopied(true);
      setOpen(true);
      await navigator.clipboard.writeText(textToCopy || "");
      setTimeout(() => {
        setCopied(false);
        setOpen(false);
      }, 1000);
    } catch (error) {
      toast.error("Failed to copy");
    }
  };

  return (
    <Tooltip
      label={copied ? "Copied" : tooltipLabel || "Copy Link"}
      open={open}
      onOpenChange={setOpen}
      delayDuration={0}
      withArrow>
      <Button
        aria-disabled={copied}
        onClick={onCopy}
        disabled={copied}
        variant={"outline"}
        size={"icon"}
        aria-label={copied ? "Copied" : "Copy to clipboard"}
        {...props}
        className={cn("relative", props.className, copied && "bg-green-300")}>
        <span className="sr-only">{copied ? "Copied" : "Copy"}</span>
        <Copy
          className={`h-4 w-4 transition-all duration-300 ${
            copied ? "scale-0" : "scale-100"
          }`}
        />
        <Check
          className={`absolute inset-0 m-auto h-4 w-4 transition-all duration-300 ${
            copied ? "scale-100" : "scale-0"
          }`}
        />
      </Button>
    </Tooltip>
  );
};
