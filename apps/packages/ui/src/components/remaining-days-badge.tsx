import { Timestamp } from "@bufbuild/protobuf/wkt";
import { cn, getRemainingDays } from "../lib/utils";
import { Badge } from "./badge";

type Props = {
  className?: string;
  endDate: Timestamp;
  startDate: Timestamp;
};

export const RemainingDaysBadge = ({
  className,
  endDate,
  startDate,
}: Props) => {
  const daysRemaining = getRemainingDays(startDate, endDate);

  return (
    <Badge
      className={cn(
        className,
        daysRemaining <= 3
          ? "bg-red-700 hover:bg-red-800"
          : daysRemaining <= 7
            ? "bg-orange-700 hover:bg-orange-800"
            : "bg-blue-700 hover:bg-blue-800",
      )}>
      {daysRemaining > 0 ? `${daysRemaining}d ${"LEFT"}` : "Ended"}
    </Badge>
  );
};
