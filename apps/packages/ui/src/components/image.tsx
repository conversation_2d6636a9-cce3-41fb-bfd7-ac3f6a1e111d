import { useLanguage } from "@vtuber/language/hooks";
import { ImageOff } from "lucide-react";
import { DictionaryKey } from "node_modules/@vtuber/language/src/types";
import { forwardRef, useCallback, useMemo, useState } from "react";
import useIntersectionObserver from "../hooks/use-intersection-observer";
import { cn } from "../lib/utils";
import {
  ColorCombination,
  defaultColorCombinations,
  hashString,
  SVGPattern,
} from "./icons/image-svg-pattern";

type Props = React.ComponentPropsWithoutRef<"img"> & {
  errorMessage?: DictionaryKey;
  loadingStateClassName?: string;
};

type ImageWrapperProps = {
  className?: string;
  children?: React.ReactNode;
  style?: React.CSSProperties;
  onClick?: (e: any) => void;
};

const ImageWrapper = forwardRef<HTMLDivElement, ImageWrapperProps>(
  ({ children, className, style, onClick }, ref) => {
    return (
      <div
        onClick={onClick}
        ref={ref}
        style={style}
        className={cn("relative", className)}>
        {children}
      </div>
    );
  },
);

ImageWrapper.displayName = "ImageWrapper";

export const Image = forwardRef<HTMLImageElement, Props>(
  (
    { errorMessage = "image_not_available", loadingStateClassName, ...props },
    ref,
  ) => {
    const { getText } = useLanguage();
    const { ref: imgRef, isIntersecting } = useIntersectionObserver({
      freezeOnceVisible: true,
      rootMargin: "50px",
    });
    const [status, setStatus] = useState<"loading" | "loaded" | "error">(
      "loading",
    );

    const patternData = useMemo(() => {
      const seedValue = hashString(props.src || "default");
      const colorIndex = seedValue % defaultColorCombinations.length;
      const selectedColors = defaultColorCombinations[colorIndex];

      const randomSeed = seedValue % 1000;
      const uniqueId =
        `${hashString(props.src || "default")}-${seedValue}`.slice(0, 9);

      return {
        colors: selectedColors,
        uniqueId,
        randomSeed,
      };
    }, [props.src]);

    if (!props.src) {
      console.warn("Image: src prop is required");
    }
    if (!props.alt) {
      console.warn("Image: alt prop is required for accessibility");
    }

    const handleLoad = useCallback(
      (e: React.SyntheticEvent<HTMLImageElement>) => {
        setStatus("loaded");
        props.onLoad?.(e);
      },
      [props.onLoad],
    );

    const handleError = useCallback(
      (event: React.SyntheticEvent<HTMLImageElement>) => {
        setStatus("error");
        props.onError?.(event);
      },
      [props.onError],
    );

    if (!props.src || status === "error") {
      return (
        <ImageWrapper
          onClick={props.onClick}
          className={cn("h-full text-sm w-full", props.className)}
          style={props.style}>
          <div
            className={cn(
              "h-full w-full flex flex-col items-center justify-center bg-destructive/10 text-font",
              props.className,
            )}>
            <ImageOff className="h-20 w-20 mb-2" />
            <span className="text-center">{getText(errorMessage)}</span>
          </div>
        </ImageWrapper>
      );
    }

    return (
      <ImageWrapper
        style={props.style}
        ref={imgRef}
        onClick={props.onClick}
        className={cn(
          "h-full relative overflow-hidden w-full",
          status === "loading" && loadingStateClassName,
          props.className,
        )}>
        {status === "loading" && (
          <div
            className={cn("absolute inset-0 transition-opacity duration-500")}
            style={{ filter: "blur(50px)" }}
            aria-hidden="true">
            <SVGPattern
              colors={patternData.colors as ColorCombination}
              uniqueId={patternData.uniqueId}
              randomSeed={patternData.randomSeed}
            />
          </div>
        )}
        {isIntersecting && (
          <img
            {...props}
            style={{}}
            ref={imgRef || ref}
            onLoad={(e) => {
              handleLoad(e);
            }}
            onError={(e) => {
              handleError(e);
            }}
            className={cn(
              "h-full w-full object-cover transition-opacity duration-500 ease-in-out",
              status === "loading" ? "opacity-0" : "opacity-100",
              status === "loaded" ? "animate-blur-in" : "",
              props.className,
            )}
          />
        )}
      </ImageWrapper>
    );
  },
);

Image.displayName = "Image";
