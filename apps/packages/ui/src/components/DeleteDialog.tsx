import { useMutation as useTanstackMutation } from "@tanstack/react-query";
import React from "react";
import {
  AlertDialog,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "./alert-dialog";

import { useLanguage } from "@vtuber/language/hooks";

import { useRouter } from "@tanstack/react-router";
import { DictionaryKey } from "@vtuber/language/types";
import { useState } from "react";
import { toast } from "sonner";
import { Button } from "./button";

interface DeleteDialogProps {
  name: string;
  inValidate?: boolean;
  onDelete: () => any | Promise<any>;
  children: React.ReactNode;
  successText?: DictionaryKey;
  onSuccess?: (data?: any, variables?: any) => void | Promise<void>;
  onError?: (err: Error) => void;
  asChild?: boolean;
  className?: string;
}

export const DeleteDialog = ({
  name,
  children,
  inValidate = true,
  successText,
  onDelete,
  onSuccess,
  asChild = true,
  onError,
  className,
}: DeleteDialogProps) => {
  const router = useRouter();
  const { getText } = useLanguage();
  const [open, setOpen] = useState(false);
  const deleteMutation = useTanstackMutation({
    mutationFn: onDelete,
    onSuccess: async (data: any, variables) => {
      const [res, err] = data;
      if (res) {
        inValidate && (await router.invalidate());
        await onSuccess?.(data, variables);
        const message =
          res?.message ||
          (successText && getText(successText)) ||
          "Resource deleted successfully";
        toast.success(message);
      }

      if (err) {
        onError?.(err);
        toast.error(err.message);
      }
    },
  });
  return (
    <AlertDialog
      open={open}
      onOpenChange={setOpen}>
      <AlertDialogTrigger
        asChild={asChild}
        className={className}>
        {children}
      </AlertDialogTrigger>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>Are you absolutely sure?</AlertDialogTitle>
          <AlertDialogDescription>
            This Action cannot be undone. This will permanently delete {name}.
          </AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel className="capitalize">
            {getText("cancel")}
          </AlertDialogCancel>
          <Button
            loading={deleteMutation.isPending}
            onClick={async () => {
              await deleteMutation.mutateAsync();
              setOpen(false);
            }}
            className="capitalize">
            {getText("confirm")}
          </Button>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
};
