// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file campaigns/v1/campaignbanner.proto (package api.campaigns.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";

/**
 * Describes the file campaigns/v1/campaignbanner.proto.
 */
export const file_campaigns_v1_campaignbanner: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [file_authz_v1_authz, file_google_protobuf_timestamp],
  );

/**
 * @generated from message api.campaigns.v1.AddCampaignBannerRequest
 */
export type AddCampaignBannerRequest =
  Message<"api.campaigns.v1.AddCampaignBannerRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: string image = 1;
     */
    image: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_id = 2;
     */
    campaignId: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int32 index = 3;
     */
    index: number;
  };

/**
 * Describes the message api.campaigns.v1.AddCampaignBannerRequest.
 * Use `create(AddCampaignBannerRequestSchema)` to create a new message.
 */
export const AddCampaignBannerRequestSchema: GenMessage<AddCampaignBannerRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 0);

/**
 * @generated from message api.campaigns.v1.AddCampaignBannerResponse
 */
export type AddCampaignBannerResponse =
  Message<"api.campaigns.v1.AddCampaignBannerResponse"> & {
    /**
     * @generated from field: api.campaigns.v1.CampaignBanner data = 1;
     */
    data?: CampaignBanner;
  };

/**
 * Describes the message api.campaigns.v1.AddCampaignBannerResponse.
 * Use `create(AddCampaignBannerResponseSchema)` to create a new message.
 */
export const AddCampaignBannerResponseSchema: GenMessage<AddCampaignBannerResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 1);

/**
 * @generated from message api.campaigns.v1.CampaignBanner
 */
export type CampaignBanner = Message<"api.campaigns.v1.CampaignBanner"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: string image = 2;
   */
  image: string;

  /**
   * @generated from field: int32 index = 3;
   */
  index: number;

  /**
   * @generated from field: int64 campaign_id = 4;
   */
  campaignId: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 6;
   */
  createdAt?: Timestamp;
};

/**
 * Describes the message api.campaigns.v1.CampaignBanner.
 * Use `create(CampaignBannerSchema)` to create a new message.
 */
export const CampaignBannerSchema: GenMessage<CampaignBanner> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 2);

/**
 * @generated from message api.campaigns.v1.GetCampaignBannerByIdRequest
 */
export type GetCampaignBannerByIdRequest =
  Message<"api.campaigns.v1.GetCampaignBannerByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignBannerByIdRequest.
 * Use `create(GetCampaignBannerByIdRequestSchema)` to create a new message.
 */
export const GetCampaignBannerByIdRequestSchema: GenMessage<GetCampaignBannerByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 3);

/**
 * @generated from message api.campaigns.v1.GetCampaignBannerByIdResponse
 */
export type GetCampaignBannerByIdResponse =
  Message<"api.campaigns.v1.GetCampaignBannerByIdResponse"> & {
    /**
     * @generated from field: api.campaigns.v1.CampaignBanner data = 1;
     */
    data?: CampaignBanner;
  };

/**
 * Describes the message api.campaigns.v1.GetCampaignBannerByIdResponse.
 * Use `create(GetCampaignBannerByIdResponseSchema)` to create a new message.
 */
export const GetCampaignBannerByIdResponseSchema: GenMessage<GetCampaignBannerByIdResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 4);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignBannerByIdRequest
 */
export type DeleteCampaignBannerByIdRequest =
  Message<"api.campaigns.v1.DeleteCampaignBannerByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.campaigns.v1.DeleteCampaignBannerByIdRequest.
 * Use `create(DeleteCampaignBannerByIdRequestSchema)` to create a new message.
 */
export const DeleteCampaignBannerByIdRequestSchema: GenMessage<DeleteCampaignBannerByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 5);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignBannerByIdRequest
 */
export type UpdateCampaignBannerByIdRequest =
  Message<"api.campaigns.v1.UpdateCampaignBannerByIdRequest"> & {
    /**
     * @generated from field: string image = 1;
     */
    image: string;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 2;
     */
    id: bigint;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int32 index = 3;
     */
    index: number;
  };

/**
 * Describes the message api.campaigns.v1.UpdateCampaignBannerByIdRequest.
 * Use `create(UpdateCampaignBannerByIdRequestSchema)` to create a new message.
 */
export const UpdateCampaignBannerByIdRequestSchema: GenMessage<UpdateCampaignBannerByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 6);

/**
 * @generated from message api.campaigns.v1.GetBannerByCampaignIdRequest
 */
export type GetBannerByCampaignIdRequest =
  Message<"api.campaigns.v1.GetBannerByCampaignIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 campaign_id = 1;
     */
    campaignId: bigint;
  };

/**
 * Describes the message api.campaigns.v1.GetBannerByCampaignIdRequest.
 * Use `create(GetBannerByCampaignIdRequestSchema)` to create a new message.
 */
export const GetBannerByCampaignIdRequestSchema: GenMessage<GetBannerByCampaignIdRequest> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 7);

/**
 * @generated from message api.campaigns.v1.GetBannerByCampaignIdResponse
 */
export type GetBannerByCampaignIdResponse =
  Message<"api.campaigns.v1.GetBannerByCampaignIdResponse"> & {
    /**
     * @generated from field: repeated api.campaigns.v1.CampaignBanner data = 1;
     */
    data: CampaignBanner[];
  };

/**
 * Describes the message api.campaigns.v1.GetBannerByCampaignIdResponse.
 * Use `create(GetBannerByCampaignIdResponseSchema)` to create a new message.
 */
export const GetBannerByCampaignIdResponseSchema: GenMessage<GetBannerByCampaignIdResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 8);

/**
 * @generated from message api.campaigns.v1.UpdateCampaignBannerResponse
 */
export type UpdateCampaignBannerResponse =
  Message<"api.campaigns.v1.UpdateCampaignBannerResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.campaigns.v1.UpdateCampaignBannerResponse.
 * Use `create(UpdateCampaignBannerResponseSchema)` to create a new message.
 */
export const UpdateCampaignBannerResponseSchema: GenMessage<UpdateCampaignBannerResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 9);

/**
 * @generated from message api.campaigns.v1.DeleteCampaignBannerResponse
 */
export type DeleteCampaignBannerResponse =
  Message<"api.campaigns.v1.DeleteCampaignBannerResponse"> & {
    /**
     * @generated from field: bool success = 1;
     */
    success: boolean;

    /**
     * @generated from field: string message = 2;
     */
    message: string;
  };

/**
 * Describes the message api.campaigns.v1.DeleteCampaignBannerResponse.
 * Use `create(DeleteCampaignBannerResponseSchema)` to create a new message.
 */
export const DeleteCampaignBannerResponseSchema: GenMessage<DeleteCampaignBannerResponse> =
  /*@__PURE__*/
  messageDesc(file_campaigns_v1_campaignbanner, 10);

/**
 * @generated from service api.campaigns.v1.CampaignBannerService
 */
export const CampaignBannerService: GenService<{
  /**
   * @generated from rpc api.campaigns.v1.CampaignBannerService.AddCampaignBanner
   */
  addCampaignBanner: {
    methodKind: "unary";
    input: typeof AddCampaignBannerRequestSchema;
    output: typeof AddCampaignBannerResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignBannerService.GetCampaignBannerById
   */
  getCampaignBannerById: {
    methodKind: "unary";
    input: typeof GetCampaignBannerByIdRequestSchema;
    output: typeof GetCampaignBannerByIdResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignBannerService.DeleteCampaignBannerById
   */
  deleteCampaignBannerById: {
    methodKind: "unary";
    input: typeof DeleteCampaignBannerByIdRequestSchema;
    output: typeof DeleteCampaignBannerResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignBannerService.UpdateCampaignBannerById
   */
  updateCampaignBannerById: {
    methodKind: "unary";
    input: typeof UpdateCampaignBannerByIdRequestSchema;
    output: typeof UpdateCampaignBannerResponseSchema;
  };
  /**
   * @generated from rpc api.campaigns.v1.CampaignBannerService.GetBannerByCampaignId
   */
  getBannerByCampaignId: {
    methodKind: "unary";
    input: typeof GetBannerByCampaignIdRequestSchema;
    output: typeof GetBannerByCampaignIdResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_campaigns_v1_campaignbanner, 0);
