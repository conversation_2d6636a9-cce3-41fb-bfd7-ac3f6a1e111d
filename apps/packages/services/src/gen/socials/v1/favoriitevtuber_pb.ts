// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file socials/v1/favoriitevtuber.proto (package api.socials.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";
import type { VtuberProfile } from "../../vtubers/v1/vtuberprofiles_pb";
import { file_vtubers_v1_vtuberprofiles } from "../../vtubers/v1/vtuberprofiles_pb";

/**
 * Describes the file socials/v1/favoriitevtuber.proto.
 */
export const file_socials_v1_favoriitevtuber: GenFile =
  /*@__PURE__*/
  fileDesc(
    "CiBzb2NpYWxzL3YxL2Zhdm9yaWl0ZXZ0dWJlci5wcm90bxIOYXBpLnNvY2lhbHMudjEiLQoYQWRkRmF2b3JpdGVWdHViZXJSZXF1ZXN0EhEKCXZ0dWJlcl9pZBgBIAEoAyJJChlBZGRGYXZvcml0ZVZ0dWJlclJlc3BvbnNlEiwKBGRhdGEYASABKAsyHi5hcGkuc29jaWFscy52MS5GYXZvcml0ZVZ0dWJlciLsAQoZRmF2b3JpdGVWdHViZXJXaXRoRGV0YWlscxItCgZ2dHViZXIYASABKAsyHS5hcGkudnR1YmVycy52MS5WdHViZXJQcm9maWxlEhEKCXZ0dWJlcl9pZBgCIAEoAxIPCgd1c2VyX2lkGAMgASgDEi4KCmNyZWF0ZWRfYXQYBCABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEi4KCnVwZGF0ZWRfYXQYBSABKAsyGi5nb29nbGUucHJvdG9idWYuVGltZXN0YW1wEgoKAmlkGAYgASgDEhAKCHVzZXJuYW1lGAcgASgJIrIBCg5GYXZvcml0ZVZ0dWJlchIKCgJpZBgBIAEoAxIRCgl2dHViZXJfaWQYAiABKAMSDwoHdXNlcl9pZBgDIAEoAxIuCgpjcmVhdGVkX2F0GAQgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcBIuCgp1cGRhdGVkX2F0GAUgASgLMhouZ29vZ2xlLnByb3RvYnVmLlRpbWVzdGFtcBIQCgh1c2VybmFtZRgGIAEoCSJnChtHZXRBbGxGYXZvcml0ZVZ0dWJlclJlcXVlc3QSOQoKcGFnaW5hdGlvbhgDIAEoCzIgLmFwaS5zaGFyZWQudjEuUGFnaW5hdGlvblJlcXVlc3RIAIgBAUINCgtfcGFnaW5hdGlvbiKVAQocR2V0QWxsRmF2b3JpdGVWdHViZXJSZXNwb25zZRI3CgRkYXRhGAEgAygLMikuYXBpLnNvY2lhbHMudjEuRmF2b3JpdGVWdHViZXJXaXRoRGV0YWlscxI8ChJwYWdpbmF0aW9uX2RldGFpbHMYAiABKAsyIC5hcGkuc2hhcmVkLnYxLlBhZ2luYXRpb25EZXRhaWxzIjAKG0RlbGV0ZUZhdm9yaXRlVnR1YmVyUmVxdWVzdBIRCgl2dHViZXJfaWQYASABKAMy8QIKFUZhdm9yaXRlVnR1YmVyU2VydmljZRJwChFBZGRGYXZvcml0ZVZ0dWJlchIoLmFwaS5zb2NpYWxzLnYxLkFkZEZhdm9yaXRlVnR1YmVyUmVxdWVzdBopLmFwaS5zb2NpYWxzLnYxLkFkZEZhdm9yaXRlVnR1YmVyUmVzcG9uc2UiBoK1GAIIARJ5ChRHZXRBbGxGYXZvcml0ZVZ0dWJlchIrLmFwaS5zb2NpYWxzLnYxLkdldEFsbEZhdm9yaXRlVnR1YmVyUmVxdWVzdBosLmFwaS5zb2NpYWxzLnYxLkdldEFsbEZhdm9yaXRlVnR1YmVyUmVzcG9uc2UiBoK1GAIIARJrChREZWxldGVGYXZvcml0ZVZ0dWJlchIrLmFwaS5zb2NpYWxzLnYxLkRlbGV0ZUZhdm9yaXRlVnR1YmVyUmVxdWVzdBoeLmFwaS5zaGFyZWQudjEuR2VuZXJpY1Jlc3BvbnNlIgaCtRgCCAFCNFoyZ2l0aHViLmNvbS9uc3AtaW5jL3Z0dWJlci9hcGkvc29jaWFscy92MTtzb2NpYWxzdjFiBnByb3RvMw",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
      file_vtubers_v1_vtuberprofiles,
    ],
  );

/**
 * @generated from message api.socials.v1.AddFavoriteVtuberRequest
 */
export type AddFavoriteVtuberRequest =
  Message<"api.socials.v1.AddFavoriteVtuberRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 vtuber_id = 1;
     */
    vtuberId: bigint;
  };

/**
 * Describes the message api.socials.v1.AddFavoriteVtuberRequest.
 * Use `create(AddFavoriteVtuberRequestSchema)` to create a new message.
 */
export const AddFavoriteVtuberRequestSchema: GenMessage<AddFavoriteVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 0);

/**
 * @generated from message api.socials.v1.AddFavoriteVtuberResponse
 */
export type AddFavoriteVtuberResponse =
  Message<"api.socials.v1.AddFavoriteVtuberResponse"> & {
    /**
     * @generated from field: api.socials.v1.FavoriteVtuber data = 1;
     */
    data?: FavoriteVtuber;
  };

/**
 * Describes the message api.socials.v1.AddFavoriteVtuberResponse.
 * Use `create(AddFavoriteVtuberResponseSchema)` to create a new message.
 */
export const AddFavoriteVtuberResponseSchema: GenMessage<AddFavoriteVtuberResponse> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 1);

/**
 * @generated from message api.socials.v1.FavoriteVtuberWithDetails
 */
export type FavoriteVtuberWithDetails =
  Message<"api.socials.v1.FavoriteVtuberWithDetails"> & {
    /**
     * @generated from field: api.vtubers.v1.VtuberProfile vtuber = 1;
     */
    vtuber?: VtuberProfile;

    /**
     * @generated from field: int64 vtuber_id = 2;
     */
    vtuberId: bigint;

    /**
     * @generated from field: int64 user_id = 3;
     */
    userId: bigint;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 4;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: google.protobuf.Timestamp updated_at = 5;
     */
    updatedAt?: Timestamp;

    /**
     * @generated from field: int64 id = 6;
     */
    id: bigint;

    /**
     * @generated from field: string username = 7;
     */
    username: string;
  };

/**
 * Describes the message api.socials.v1.FavoriteVtuberWithDetails.
 * Use `create(FavoriteVtuberWithDetailsSchema)` to create a new message.
 */
export const FavoriteVtuberWithDetailsSchema: GenMessage<FavoriteVtuberWithDetails> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 2);

/**
 * @generated from message api.socials.v1.FavoriteVtuber
 */
export type FavoriteVtuber = Message<"api.socials.v1.FavoriteVtuber"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: int64 vtuber_id = 2;
   */
  vtuberId: bigint;

  /**
   * @generated from field: int64 user_id = 3;
   */
  userId: bigint;

  /**
   * @generated from field: google.protobuf.Timestamp created_at = 4;
   */
  createdAt?: Timestamp;

  /**
   * @generated from field: google.protobuf.Timestamp updated_at = 5;
   */
  updatedAt?: Timestamp;

  /**
   * @generated from field: string username = 6;
   */
  username: string;
};

/**
 * Describes the message api.socials.v1.FavoriteVtuber.
 * Use `create(FavoriteVtuberSchema)` to create a new message.
 */
export const FavoriteVtuberSchema: GenMessage<FavoriteVtuber> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 3);

/**
 * @generated from message api.socials.v1.GetAllFavoriteVtuberRequest
 */
export type GetAllFavoriteVtuberRequest =
  Message<"api.socials.v1.GetAllFavoriteVtuberRequest"> & {
    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 3;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.socials.v1.GetAllFavoriteVtuberRequest.
 * Use `create(GetAllFavoriteVtuberRequestSchema)` to create a new message.
 */
export const GetAllFavoriteVtuberRequestSchema: GenMessage<GetAllFavoriteVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 4);

/**
 * @generated from message api.socials.v1.GetAllFavoriteVtuberResponse
 */
export type GetAllFavoriteVtuberResponse =
  Message<"api.socials.v1.GetAllFavoriteVtuberResponse"> & {
    /**
     * @generated from field: repeated api.socials.v1.FavoriteVtuberWithDetails data = 1;
     */
    data: FavoriteVtuberWithDetails[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.socials.v1.GetAllFavoriteVtuberResponse.
 * Use `create(GetAllFavoriteVtuberResponseSchema)` to create a new message.
 */
export const GetAllFavoriteVtuberResponseSchema: GenMessage<GetAllFavoriteVtuberResponse> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 5);

/**
 * @generated from message api.socials.v1.DeleteFavoriteVtuberRequest
 */
export type DeleteFavoriteVtuberRequest =
  Message<"api.socials.v1.DeleteFavoriteVtuberRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 vtuber_id = 1;
     */
    vtuberId: bigint;
  };

/**
 * Describes the message api.socials.v1.DeleteFavoriteVtuberRequest.
 * Use `create(DeleteFavoriteVtuberRequestSchema)` to create a new message.
 */
export const DeleteFavoriteVtuberRequestSchema: GenMessage<DeleteFavoriteVtuberRequest> =
  /*@__PURE__*/
  messageDesc(file_socials_v1_favoriitevtuber, 6);

/**
 * @generated from service api.socials.v1.FavoriteVtuberService
 */
export const FavoriteVtuberService: GenService<{
  /**
   * @generated from rpc api.socials.v1.FavoriteVtuberService.AddFavoriteVtuber
   */
  addFavoriteVtuber: {
    methodKind: "unary";
    input: typeof AddFavoriteVtuberRequestSchema;
    output: typeof AddFavoriteVtuberResponseSchema;
  };
  /**
   * @generated from rpc api.socials.v1.FavoriteVtuberService.GetAllFavoriteVtuber
   */
  getAllFavoriteVtuber: {
    methodKind: "unary";
    input: typeof GetAllFavoriteVtuberRequestSchema;
    output: typeof GetAllFavoriteVtuberResponseSchema;
  };
  /**
   * @generated from rpc api.socials.v1.FavoriteVtuberService.DeleteFavoriteVtuber
   */
  deleteFavoriteVtuber: {
    methodKind: "unary";
    input: typeof DeleteFavoriteVtuberRequestSchema;
    output: typeof GenericResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_socials_v1_favoriitevtuber, 0);
