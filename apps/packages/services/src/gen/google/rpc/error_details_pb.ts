import type { Message } from "@bufbuild/protobuf";
import type { GenFile, GenMessage } from "@bufbuild/protobuf/codegenv1";
import { fileDesc, messageDesc } from "@bufbuild/protobuf/codegenv1";
import type { Duration } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_duration } from "@bufbuild/protobuf/wkt";

export const file_google_rpc_error_details: GenFile = fileDesc(
  "Ch5nb29nbGUvcnBjL2Vycm9yX2RldGFpbHMucHJvdG8SCmdvb2dsZS5ycGMikwEKCUVycm9ySW5mbxIOCgZyZWFzb24YASABKAkSDgoGZG9tYWluGAIgASgJEjUKCG1ldGFkYXRhGAMgAygLMiMuZ29vZ2xlLnJwYy5FcnJvckluZm8uTWV0YWRhdGFFbnRyeRovCg1NZXRhZGF0YUVudHJ5EgsKA2tleRgBIAEoCRINCgV2YWx1ZRgCIAEoCToCOAEiOwoJUmV0cnlJbmZvEi4KC3JldHJ5X2RlbGF5GAEgASgLMhkuZ29vZ2xlLnByb3RvYnVmLkR1cmF0aW9uIjIKCURlYnVnSW5mbxIVCg1zdGFja19lbnRyaWVzGAEgAygJEg4KBmRldGFpbBgCIAEoCSJ5CgxRdW90YUZhaWx1cmUSNgoKdmlvbGF0aW9ucxgBIAMoCzIiLmdvb2dsZS5ycGMuUXVvdGFGYWlsdXJlLlZpb2xhdGlvbhoxCglWaW9sYXRpb24SDwoHc3ViamVjdBgBIAEoCRITCgtkZXNjcmlwdGlvbhgCIAEoCSKVAQoTUHJlY29uZGl0aW9uRmFpbHVyZRI9Cgp2aW9sYXRpb25zGAEgAygLMikuZ29vZ2xlLnJwYy5QcmVjb25kaXRpb25GYWlsdXJlLlZpb2xhdGlvbho/CglWaW9sYXRpb24SDAoEdHlwZRgBIAEoCRIPCgdzdWJqZWN0GAIgASgJEhMKC2Rlc2NyaXB0aW9uGAMgASgJIswBCgpCYWRSZXF1ZXN0Ej8KEGZpZWxkX3Zpb2xhdGlvbnMYASADKAsyJS5nb29nbGUucnBjLkJhZFJlcXVlc3QuRmllbGRWaW9sYXRpb24afQoORmllbGRWaW9sYXRpb24SDQoFZmllbGQYASABKAkSEwoLZGVzY3JpcHRpb24YAiABKAkSDgoGcmVhc29uGAMgASgJEjcKEWxvY2FsaXplZF9tZXNzYWdlGAQgASgLMhwuZ29vZ2xlLnJwYy5Mb2NhbGl6ZWRNZXNzYWdlIjcKC1JlcXVlc3RJbmZvEhIKCnJlcXVlc3RfaWQYASABKAkSFAoMc2VydmluZ19kYXRhGAIgASgJImAKDFJlc291cmNlSW5mbxIVCg1yZXNvdXJjZV90eXBlGAEgASgJEhUKDXJlc291cmNlX25hbWUYAiABKAkSDQoFb3duZXIYAyABKAkSEwoLZGVzY3JpcHRpb24YBCABKAkiVgoESGVscBIkCgVsaW5rcxgBIAMoCzIVLmdvb2dsZS5ycGMuSGVscC5MaW5rGigKBExpbmsSEwoLZGVzY3JpcHRpb24YASABKAkSCwoDdXJsGAIgASgJIjMKEExvY2FsaXplZE1lc3NhZ2USDgoGbG9jYWxlGAEgASgJEg8KB21lc3NhZ2UYAiABKAlCbAoOY29tLmdvb2dsZS5ycGNCEUVycm9yRGV0YWlsc1Byb3RvUAFaP2dvb2dsZS5nb2xhbmcub3JnL2dlbnByb3RvL2dvb2dsZWFwaXMvcnBjL2VycmRldGFpbHM7ZXJyZGV0YWlsc6ICA1JQQ2IGcHJvdG8z",
  [file_google_protobuf_duration],
);

export type ErrorInfo = Message<"google.rpc.ErrorInfo"> & {
  reason: string;

  domain: string;

  metadata: { [key: string]: string };
};

export const ErrorInfoSchema: GenMessage<ErrorInfo> = messageDesc(
  file_google_rpc_error_details,
  0,
);

export type RetryInfo = Message<"google.rpc.RetryInfo"> & {
  retryDelay?: Duration;
};

export const RetryInfoSchema: GenMessage<RetryInfo> = messageDesc(
  file_google_rpc_error_details,
  1,
);

export type DebugInfo = Message<"google.rpc.DebugInfo"> & {
  stackEntries: string[];

  detail: string;
};

export const DebugInfoSchema: GenMessage<DebugInfo> = messageDesc(
  file_google_rpc_error_details,
  2,
);

export type QuotaFailure = Message<"google.rpc.QuotaFailure"> & {
  violations: QuotaFailure_Violation[];
};

export const QuotaFailureSchema: GenMessage<QuotaFailure> = messageDesc(
  file_google_rpc_error_details,
  3,
);

export type QuotaFailure_Violation =
  Message<"google.rpc.QuotaFailure.Violation"> & {
    subject: string;

    description: string;
  };

export const QuotaFailure_ViolationSchema: GenMessage<QuotaFailure_Violation> =
  messageDesc(file_google_rpc_error_details, 3, 0);

export type PreconditionFailure = Message<"google.rpc.PreconditionFailure"> & {
  violations: PreconditionFailure_Violation[];
};

export const PreconditionFailureSchema: GenMessage<PreconditionFailure> =
  messageDesc(file_google_rpc_error_details, 4);

export type PreconditionFailure_Violation =
  Message<"google.rpc.PreconditionFailure.Violation"> & {
    type: string;

    subject: string;

    description: string;
  };

export const PreconditionFailure_ViolationSchema: GenMessage<PreconditionFailure_Violation> =
  messageDesc(file_google_rpc_error_details, 4, 0);

export type BadRequest = Message<"google.rpc.BadRequest"> & {
  fieldViolations: BadRequest_FieldViolation[];
};

export const BadRequestSchema: GenMessage<BadRequest> = messageDesc(
  file_google_rpc_error_details,
  5,
);

export type BadRequest_FieldViolation =
  Message<"google.rpc.BadRequest.FieldViolation"> & {
    field: string;

    description: string;

    reason: string;

    localizedMessage?: LocalizedMessage;
  };

export const BadRequest_FieldViolationSchema: GenMessage<BadRequest_FieldViolation> =
  messageDesc(file_google_rpc_error_details, 5, 0);

export type RequestInfo = Message<"google.rpc.RequestInfo"> & {
  requestId: string;

  servingData: string;
};

export const RequestInfoSchema: GenMessage<RequestInfo> = messageDesc(
  file_google_rpc_error_details,
  6,
);

export type ResourceInfo = Message<"google.rpc.ResourceInfo"> & {
  resourceType: string;

  resourceName: string;

  owner: string;

  description: string;
};

export const ResourceInfoSchema: GenMessage<ResourceInfo> = messageDesc(
  file_google_rpc_error_details,
  7,
);

export type Help = Message<"google.rpc.Help"> & {
  links: Help_Link[];
};

export const HelpSchema: GenMessage<Help> = messageDesc(
  file_google_rpc_error_details,
  8,
);

export type Help_Link = Message<"google.rpc.Help.Link"> & {
  description: string;

  url: string;
};

export const Help_LinkSchema: GenMessage<Help_Link> = messageDesc(
  file_google_rpc_error_details,
  8,
  0,
);

export type LocalizedMessage = Message<"google.rpc.LocalizedMessage"> & {
  locale: string;

  message: string;
};

export const LocalizedMessageSchema: GenMessage<LocalizedMessage> = messageDesc(
  file_google_rpc_error_details,
  9,
);
