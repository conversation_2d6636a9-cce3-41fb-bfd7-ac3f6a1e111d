import type { GenEnum, GenFile } from "@bufbuild/protobuf/codegenv1";
import { enumDesc, fileDesc } from "@bufbuild/protobuf/codegenv1";

export const file_google_rpc_code: GenFile = fileDesc(
  "ChVnb29nbGUvcnBjL2NvZGUucHJvdG8SCmdvb2dsZS5ycGMqtwIKBENvZGUSBgoCT0sQABINCglDQU5DRUxMRUQQARILCgdVTktOT1dOEAISFAoQSU5WQUxJRF9BUkdVTUVOVBADEhUKEURFQURMSU5FX0VYQ0VFREVEEAQSDQoJTk9UX0ZPVU5EEAUSEgoOQUxSRUFEWV9FWElTVFMQBhIVChFQRVJNSVNTSU9OX0RFTklFRBAHEhMKD1VOQVVUSEVOVElDQVRFRBAQEhYKElJFU09VUkNFX0VYSEFVU1RFRBAIEhcKE0ZBSUxFRF9QUkVDT05ESVRJT04QCRILCgdBQk9SVEVEEAoSEAoMT1VUX09GX1JBTkdFEAsSEQoNVU5JTVBMRU1FTlRFRBAMEgwKCElOVEVSTkFMEA0SDwoLVU5BVkFJTEFCTEUQDhINCglEQVRBX0xPU1MQD0JYCg5jb20uZ29vZ2xlLnJwY0IJQ29kZVByb3RvUAFaM2dvb2dsZS5nb2xhbmcub3JnL2dlbnByb3RvL2dvb2dsZWFwaXMvcnBjL2NvZGU7Y29kZaICA1JQQ2IGcHJvdG8z",
);

export enum Code {
  OK = 0,

  CANCELLED = 1,

  UNKNOWN = 2,

  INVALID_ARGUMENT = 3,

  DEADLINE_EXCEEDED = 4,

  NOT_FOUND = 5,

  ALREADY_EXISTS = 6,

  PERMISSION_DENIED = 7,

  UNAUTHENTICATED = 16,

  RESOURCE_EXHAUSTED = 8,

  FAILED_PRECONDITION = 9,

  ABORTED = 10,

  OUT_OF_RANGE = 11,

  UNIMPLEMENTED = 12,

  INTERNAL = 13,

  UNAVAILABLE = 14,

  DATA_LOSS = 15,
}

export const CodeSchema: GenEnum<Code> = enumDesc(file_google_rpc_code, 0);
