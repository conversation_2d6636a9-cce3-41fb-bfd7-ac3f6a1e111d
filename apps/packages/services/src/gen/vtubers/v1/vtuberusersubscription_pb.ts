// @generated by protoc-gen-es v2.2.3 with parameter "target=ts"
// @generated from file vtubers/v1/vtuberusersubscription.proto (package api.vtubers.v1, syntax proto3)
/* eslint-disable */

import type { Message } from "@bufbuild/protobuf";
import type {
  GenFile,
  GenMessage,
  GenService,
} from "@bufbuild/protobuf/codegenv1";
import {
  fileDesc,
  messageDesc,
  serviceDesc,
} from "@bufbuild/protobuf/codegenv1";
import type { Timestamp } from "@bufbuild/protobuf/wkt";
import { file_google_protobuf_timestamp } from "@bufbuild/protobuf/wkt";
import { file_authz_v1_authz } from "../../authz/v1/authz_pb";
import type { GenericResponseSchema } from "../../shared/v1/generic_pb";
import { file_shared_v1_generic } from "../../shared/v1/generic_pb";
import type {
  PaginationDetails,
  PaginationRequest,
} from "../../shared/v1/pagination_pb";
import { file_shared_v1_pagination } from "../../shared/v1/pagination_pb";
import { file_shared_v1_profile } from "../../shared/v1/profile_pb";
import { file_shared_v1_social_media_links } from "../../shared/v1/social_media_links_pb";

/**
 * Describes the file vtubers/v1/vtuberusersubscription.proto.
 */
export const file_vtubers_v1_vtuberusersubscription: GenFile =
  /*@__PURE__*/
  fileDesc(
    "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",
    [
      file_authz_v1_authz,
      file_google_protobuf_timestamp,
      file_shared_v1_generic,
      file_shared_v1_pagination,
      file_shared_v1_profile,
      file_shared_v1_social_media_links,
    ],
  );

/**
 * @generated from message api.vtubers.v1.AddVtuberUserSubscriptionRequest
 */
export type AddVtuberUserSubscriptionRequest =
  Message<"api.vtubers.v1.AddVtuberUserSubscriptionRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 vtuber_plan_id = 2;
     */
    vtuberPlanId: bigint;

    /**
     * @generated from field: bool is_recurring = 3;
     */
    isRecurring: boolean;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 billing_id = 5;
     */
    billingId: bigint;

    /**
     * @gotag: validate:"required,oneof=monthly annual"
     *
     * @generated from field: string plan_type = 6;
     */
    planType: string;
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberUserSubscriptionRequest.
 * Use `create(AddVtuberUserSubscriptionRequestSchema)` to create a new message.
 */
export const AddVtuberUserSubscriptionRequestSchema: GenMessage<AddVtuberUserSubscriptionRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 0);

/**
 * @generated from message api.vtubers.v1.AddVtuberUserSubscriptionResponse
 */
export type AddVtuberUserSubscriptionResponse =
  Message<"api.vtubers.v1.AddVtuberUserSubscriptionResponse"> & {
    /**
     * @generated from field: api.vtubers.v1.VtuberUserSubscription data = 1;
     */
    data?: VtuberUserSubscription;
  };

/**
 * Describes the message api.vtubers.v1.AddVtuberUserSubscriptionResponse.
 * Use `create(AddVtuberUserSubscriptionResponseSchema)` to create a new message.
 */
export const AddVtuberUserSubscriptionResponseSchema: GenMessage<AddVtuberUserSubscriptionResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 1);

/**
 * @generated from message api.vtubers.v1.VtuberUserSubscription
 */
export type VtuberUserSubscription =
  Message<"api.vtubers.v1.VtuberUserSubscription"> & {
    /**
     * @generated from field: int64 id = 1;
     */
    id: bigint;

    /**
     * @generated from field: int64 vtuber_id = 2;
     */
    vtuberId: bigint;

    /**
     * @generated from field: int64 user_id = 8;
     */
    userId: bigint;

    /**
     * @generated from field: api.vtubers.v1.User user = 3;
     */
    user?: User;

    /**
     * @generated from field: int64 vtuber_subscription_id = 4;
     */
    vtuberSubscriptionId: bigint;

    /**
     * @generated from field: bool is_recurring = 5;
     */
    isRecurring: boolean;

    /**
     * @generated from field: google.protobuf.Timestamp created_at = 6;
     */
    createdAt?: Timestamp;

    /**
     * @generated from field: google.protobuf.Timestamp expires_on = 7;
     */
    expiresOn?: Timestamp;
  };

/**
 * Describes the message api.vtubers.v1.VtuberUserSubscription.
 * Use `create(VtuberUserSubscriptionSchema)` to create a new message.
 */
export const VtuberUserSubscriptionSchema: GenMessage<VtuberUserSubscription> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 2);

/**
 * @generated from message api.vtubers.v1.User
 */
export type User = Message<"api.vtubers.v1.User"> & {
  /**
   * @generated from field: int64 id = 1;
   */
  id: bigint;

  /**
   * @generated from field: optional string email = 2;
   */
  email?: string;

  /**
   * @generated from field: string name = 3;
   */
  name: string;

  /**
   * @generated from field: optional string image = 4;
   */
  image?: string;
};

/**
 * Describes the message api.vtubers.v1.User.
 * Use `create(UserSchema)` to create a new message.
 */
export const UserSchema: GenMessage<User> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 3);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest
 */
export type GetAllVtuberUserSubscriptionsRequest =
  Message<"api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest"> & {
    /**
     * @generated from field: optional int64 user_id = 1;
     */
    userId?: bigint;

    /**
     * @generated from field: optional int64 vtuber_id = 2;
     */
    vtuberId?: bigint;

    /**
     * @generated from field: optional int64 vtuber_subscription_id = 3;
     */
    vtuberSubscriptionId?: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 4;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberUserSubscriptionsRequest.
 * Use `create(GetAllVtuberUserSubscriptionsRequestSchema)` to create a new message.
 */
export const GetAllVtuberUserSubscriptionsRequestSchema: GenMessage<GetAllVtuberUserSubscriptionsRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 4);

/**
 * @generated from message api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest
 */
export type GetVtuberUserSubscriptionByIdRequest =
  Message<"api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.vtubers.v1.GetVtuberUserSubscriptionByIdRequest.
 * Use `create(GetVtuberUserSubscriptionByIdRequestSchema)` to create a new message.
 */
export const GetVtuberUserSubscriptionByIdRequestSchema: GenMessage<GetVtuberUserSubscriptionByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 5);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberUserSubscriptionsOfUserRequest
 */
export type GetAllVtuberUserSubscriptionsOfUserRequest =
  Message<"api.vtubers.v1.GetAllVtuberUserSubscriptionsOfUserRequest"> & {
    /**
     * @generated from field: optional int64 vtuber_id = 2;
     */
    vtuberId?: bigint;

    /**
     * @generated from field: optional int64 vtuber_subscription_id = 3;
     */
    vtuberSubscriptionId?: bigint;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberUserSubscriptionsOfUserRequest.
 * Use `create(GetAllVtuberUserSubscriptionsOfUserRequestSchema)` to create a new message.
 */
export const GetAllVtuberUserSubscriptionsOfUserRequestSchema: GenMessage<GetAllVtuberUserSubscriptionsOfUserRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 6);

/**
 * @generated from message api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse
 */
export type GetAllVtuberUserSubscriptionsResponse =
  Message<"api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse"> & {
    /**
     * @generated from field: repeated api.vtubers.v1.VtuberUserSubscription vtubersubscription = 1;
     */
    vtubersubscription: VtuberUserSubscription[];

    /**
     * @generated from field: api.shared.v1.PaginationDetails pagination_details = 2;
     */
    paginationDetails?: PaginationDetails;
  };

/**
 * Describes the message api.vtubers.v1.GetAllVtuberUserSubscriptionsResponse.
 * Use `create(GetAllVtuberUserSubscriptionsResponseSchema)` to create a new message.
 */
export const GetAllVtuberUserSubscriptionsResponseSchema: GenMessage<GetAllVtuberUserSubscriptionsResponse> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 7);

/**
 * @generated from message api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest
 */
export type GetMyVtuberUserSubscriptionsRequest =
  Message<"api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest"> & {
    /**
     * @generated from field: optional int64 sub_id = 2;
     */
    subId?: bigint;

    /**
     * @generated from field: optional api.shared.v1.PaginationRequest pagination = 1;
     */
    pagination?: PaginationRequest;
  };

/**
 * Describes the message api.vtubers.v1.GetMyVtuberUserSubscriptionsRequest.
 * Use `create(GetMyVtuberUserSubscriptionsRequestSchema)` to create a new message.
 */
export const GetMyVtuberUserSubscriptionsRequestSchema: GenMessage<GetMyVtuberUserSubscriptionsRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 8);

/**
 * @generated from message api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest
 */
export type DeleteVtuberUserSubscriptionByIdRequest =
  Message<"api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest"> & {
    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 1;
     */
    id: bigint;
  };

/**
 * Describes the message api.vtubers.v1.DeleteVtuberUserSubscriptionByIdRequest.
 * Use `create(DeleteVtuberUserSubscriptionByIdRequestSchema)` to create a new message.
 */
export const DeleteVtuberUserSubscriptionByIdRequestSchema: GenMessage<DeleteVtuberUserSubscriptionByIdRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 9);

/**
 * @generated from message api.vtubers.v1.UpdateVtuberUserSubscriptionRequest
 */
export type UpdateVtuberUserSubscriptionRequest =
  Message<"api.vtubers.v1.UpdateVtuberUserSubscriptionRequest"> & {
    /**
     * @generated from field: bool is_recurring = 1;
     */
    isRecurring: boolean;

    /**
     * @gotag: validate:"required"
     *
     * @generated from field: int64 id = 3;
     */
    id: bigint;
  };

/**
 * Describes the message api.vtubers.v1.UpdateVtuberUserSubscriptionRequest.
 * Use `create(UpdateVtuberUserSubscriptionRequestSchema)` to create a new message.
 */
export const UpdateVtuberUserSubscriptionRequestSchema: GenMessage<UpdateVtuberUserSubscriptionRequest> =
  /*@__PURE__*/
  messageDesc(file_vtubers_v1_vtuberusersubscription, 10);

/**
 * @generated from service api.vtubers.v1.VtuberUserSubscriptionService
 */
export const VtuberUserSubscriptionService: GenService<{
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.AddVtuberUserSubscription
   */
  addVtuberUserSubscription: {
    methodKind: "unary";
    input: typeof AddVtuberUserSubscriptionRequestSchema;
    output: typeof AddVtuberUserSubscriptionResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.GetAllVtuberUserSubscriptions
   */
  getAllVtuberUserSubscriptions: {
    methodKind: "unary";
    input: typeof GetAllVtuberUserSubscriptionsRequestSchema;
    output: typeof GetAllVtuberUserSubscriptionsResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.GetVtuberUserSubscriptionById
   */
  getVtuberUserSubscriptionById: {
    methodKind: "unary";
    input: typeof GetVtuberUserSubscriptionByIdRequestSchema;
    output: typeof VtuberUserSubscriptionSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.DeleteVtuberUserSubscriptionById
   */
  deleteVtuberUserSubscriptionById: {
    methodKind: "unary";
    input: typeof DeleteVtuberUserSubscriptionByIdRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.UpdateVtuberUserSubscriptionById
   */
  updateVtuberUserSubscriptionById: {
    methodKind: "unary";
    input: typeof UpdateVtuberUserSubscriptionRequestSchema;
    output: typeof GenericResponseSchema;
  };
  /**
   * @generated from rpc api.vtubers.v1.VtuberUserSubscriptionService.GetMyVtuberUserSubscriptions
   */
  getMyVtuberUserSubscriptions: {
    methodKind: "unary";
    input: typeof GetMyVtuberUserSubscriptionsRequestSchema;
    output: typeof GetAllVtuberUserSubscriptionsResponseSchema;
  };
}> = /*@__PURE__*/ serviceDesc(file_vtubers_v1_vtuberusersubscription, 0);
