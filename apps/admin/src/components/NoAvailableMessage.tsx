import { motion } from "framer-motion";
import {
  BarChart2,
  Calendar,
  Megaphone,
  Target,
  TrendingUp,
} from "lucide-react";
import { floatingVariants } from "~/data/constatns";

export const NoAvailableMessage = ({ message }: { message: string }) => {
  return (
    <div className="flex flex-col items-center justify-center min-h-[80vh] p-6 text-white">
      <div className="max-w-md w-full text-center">
        <div className="relative w-64 h-64 mx-auto mb-8">
          <div className="absolute inset-0 bg-gradient-to-br from-green-500/20 to-purple-500/20 rounded-full blur-xl opacity-50" />

          <motion.div
            className="absolute"
            style={{ top: "30%", left: "15%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={0}>
            <div className="bg-green-500/90 p-3 rounded-full shadow-lg shadow-green-500/20">
              <BarChart2 className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ top: "20%", right: "20%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={1}>
            <div className="bg-blue-500/90 p-3 rounded-full shadow-lg shadow-blue-500/20">
              <Calendar className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ bottom: "25%", right: "15%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={2}>
            <div className="bg-purple-500/90 p-3 rounded-full shadow-lg shadow-purple-500/20">
              <Target className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <motion.div
            className="absolute"
            style={{ bottom: "20%", left: "25%" }}
            variants={floatingVariants}
            initial="initial"
            animate="animate"
            custom={3}>
            <div className="bg-orange-500/90 p-3 rounded-full shadow-lg shadow-orange-500/20">
              <TrendingUp className="w-6 h-6 text-white" />
            </div>
          </motion.div>

          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="bg-slate-800 p-6 rounded-full border-4 border-slate-700 shadow-xl">
              <Megaphone className="w-12 h-12 text-slate-300" />
            </div>
          </div>
        </div>

        <h2 className="text-3xl font-bold mb-3 text-white">{message}</h2>
      </div>
    </div>
  );
};
