package model

import "time"

const TableNameUserDeliveryAddress = "user_delivery_address"

// Session mapped from table <user_delivery_address>
type UserDeliveryAddress struct {
	ID                    int64      `gorm:"column:id;primaryKey;autoIncrement:true" json:"id"`
	UserID                int64      `gorm:"column:user_id;not null" json:"user_id"`
	Recipient             string     `gorm:"column:recipient;not null" json:"recipient"`
	PhoneNumber           string     `gorm:"column:phone_number;not null" json:"phone_number"`
	PostalCode            string     `gorm:"column:postal_code;not null" json:"postal_code"`
	Prefecture            string     `gorm:"column:prefecture;not null" json:"prefecture"`
	City                  string     `gorm:"column:city;not null" json:"city"`
	AddressLine1          string     `gorm:"column:address_line1;not null" json:"address_line1"`
	AddressLine2          string     `gorm:"column:address_line2" json:"address_line2"`
	PreferredDeliveryTime string     `gorm:"column:preferred_delivery_time" json:"preferred_delivery_time"`
	PreferredDeliveryDate *time.Time `gorm:"column:preferred_delivery_date" json:"preferred_delivery_date"`
	CreatedAt             time.Time  `gorm:"column:created_at;not null;default:CURRENT_TIMESTAMP" json:"created_at"`
	UpdatedAt             time.Time  `gorm:"column:updated_at;not null;default:CURRENT_TIMESTAMP" json:"updated_at"`
}

// TableName UserDeliveryAddress's table name
func (*UserDeliveryAddress) TableName() string {
	return TableNameUserDeliveryAddress
}
