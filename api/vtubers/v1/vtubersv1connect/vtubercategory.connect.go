// Code generated by protoc-gen-connect-go. DO NOT EDIT.
//
// Source: vtubers/v1/vtubercategory.proto

package vtubersv1connect

import (
	connect "connectrpc.com/connect"
	context "context"
	errors "errors"
	v1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	http "net/http"
	strings "strings"
)

// This is a compile-time assertion to ensure that this generated file and the connect package are
// compatible. If you get a compiler error that this constant is not defined, this code was
// generated with a version of connect newer than the one compiled into your binary. You can fix the
// problem by either regenerating this code with an older version of connect or updating the connect
// version compiled into your binary.
const _ = connect.IsAtLeastVersion1_13_0

const (
	// VtuberCategoryServiceName is the fully-qualified name of the VtuberCategoryService service.
	VtuberCategoryServiceName = "api.vtubers.v1.VtuberCategoryService"
)

// These constants are the fully-qualified names of the RPCs defined in this package. They're
// exposed at runtime as Spec.Procedure and as the final two segments of the HTTP route.
//
// Note that these are different from the fully-qualified method names used by
// google.golang.org/protobuf/reflect/protoreflect. To convert from these constants to
// reflection-formatted method names, remove the leading slash and convert the remaining slash to a
// period.
const (
	// VtuberCategoryServiceGetAllVtuberCategoriesProcedure is the fully-qualified name of the
	// VtuberCategoryService's GetAllVtuberCategories RPC.
	VtuberCategoryServiceGetAllVtuberCategoriesProcedure = "/api.vtubers.v1.VtuberCategoryService/GetAllVtuberCategories"
)

// VtuberCategoryServiceClient is a client for the api.vtubers.v1.VtuberCategoryService service.
type VtuberCategoryServiceClient interface {
	GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetVtuberCategoriesResponse], error)
}

// NewVtuberCategoryServiceClient constructs a client for the api.vtubers.v1.VtuberCategoryService
// service. By default, it uses the Connect protocol with the binary Protobuf Codec, asks for
// gzipped responses, and sends uncompressed requests. To use the gRPC or gRPC-Web protocols, supply
// the connect.WithGRPC() or connect.WithGRPCWeb() options.
//
// The URL supplied here should be the base URL for the Connect or gRPC server (for example,
// http://api.acme.com or https://acme.com/grpc).
func NewVtuberCategoryServiceClient(httpClient connect.HTTPClient, baseURL string, opts ...connect.ClientOption) VtuberCategoryServiceClient {
	baseURL = strings.TrimRight(baseURL, "/")
	vtuberCategoryServiceMethods := v1.File_vtubers_v1_vtubercategory_proto.Services().ByName("VtuberCategoryService").Methods()
	return &vtuberCategoryServiceClient{
		getAllVtuberCategories: connect.NewClient[v1.GetAllVtuberCategoriesRequest, v1.GetVtuberCategoriesResponse](
			httpClient,
			baseURL+VtuberCategoryServiceGetAllVtuberCategoriesProcedure,
			connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetAllVtuberCategories")),
			connect.WithClientOptions(opts...),
		),
	}
}

// vtuberCategoryServiceClient implements VtuberCategoryServiceClient.
type vtuberCategoryServiceClient struct {
	getAllVtuberCategories *connect.Client[v1.GetAllVtuberCategoriesRequest, v1.GetVtuberCategoriesResponse]
}

// GetAllVtuberCategories calls api.vtubers.v1.VtuberCategoryService.GetAllVtuberCategories.
func (c *vtuberCategoryServiceClient) GetAllVtuberCategories(ctx context.Context, req *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetVtuberCategoriesResponse], error) {
	return c.getAllVtuberCategories.CallUnary(ctx, req)
}

// VtuberCategoryServiceHandler is an implementation of the api.vtubers.v1.VtuberCategoryService
// service.
type VtuberCategoryServiceHandler interface {
	GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetVtuberCategoriesResponse], error)
}

// NewVtuberCategoryServiceHandler builds an HTTP handler from the service implementation. It
// returns the path on which to mount the handler and the handler itself.
//
// By default, handlers support the Connect, gRPC, and gRPC-Web protocols with the binary Protobuf
// and JSON codecs. They also support gzip compression.
func NewVtuberCategoryServiceHandler(svc VtuberCategoryServiceHandler, opts ...connect.HandlerOption) (string, http.Handler) {
	vtuberCategoryServiceMethods := v1.File_vtubers_v1_vtubercategory_proto.Services().ByName("VtuberCategoryService").Methods()
	vtuberCategoryServiceGetAllVtuberCategoriesHandler := connect.NewUnaryHandler(
		VtuberCategoryServiceGetAllVtuberCategoriesProcedure,
		svc.GetAllVtuberCategories,
		connect.WithSchema(vtuberCategoryServiceMethods.ByName("GetAllVtuberCategories")),
		connect.WithHandlerOptions(opts...),
	)
	return "/api.vtubers.v1.VtuberCategoryService/", http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		switch r.URL.Path {
		case VtuberCategoryServiceGetAllVtuberCategoriesProcedure:
			vtuberCategoryServiceGetAllVtuberCategoriesHandler.ServeHTTP(w, r)
		default:
			http.NotFound(w, r)
		}
	})
}

// UnimplementedVtuberCategoryServiceHandler returns CodeUnimplemented from all methods.
type UnimplementedVtuberCategoryServiceHandler struct{}

func (UnimplementedVtuberCategoryServiceHandler) GetAllVtuberCategories(context.Context, *connect.Request[v1.GetAllVtuberCategoriesRequest]) (*connect.Response[v1.GetVtuberCategoriesResponse], error) {
	return nil, connect.NewError(connect.CodeUnimplemented, errors.New("api.vtubers.v1.VtuberCategoryService.GetAllVtuberCategories is not implemented"))
}
