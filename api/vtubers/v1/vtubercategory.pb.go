// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        (unknown)
// source: vtubers/v1/vtubercategory.proto

package vtubersv1

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	_ "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VtuberCategory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VtuberCategory) Reset() {
	*x = VtuberCategory{}
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VtuberCategory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VtuberCategory) ProtoMessage() {}

func (x *VtuberCategory) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VtuberCategory.ProtoReflect.Descriptor instead.
func (*VtuberCategory) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubercategory_proto_rawDescGZIP(), []int{0}
}

func (x *VtuberCategory) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetAllVtuberCategoriesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetAllVtuberCategoriesRequest) Reset() {
	*x = GetAllVtuberCategoriesRequest{}
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetAllVtuberCategoriesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetAllVtuberCategoriesRequest) ProtoMessage() {}

func (x *GetAllVtuberCategoriesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetAllVtuberCategoriesRequest.ProtoReflect.Descriptor instead.
func (*GetAllVtuberCategoriesRequest) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubercategory_proto_rawDescGZIP(), []int{1}
}

type GetVtuberCategoriesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          []*VtuberCategory      `protobuf:"bytes,1,rep,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVtuberCategoriesResponse) Reset() {
	*x = GetVtuberCategoriesResponse{}
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVtuberCategoriesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVtuberCategoriesResponse) ProtoMessage() {}

func (x *GetVtuberCategoriesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_vtubers_v1_vtubercategory_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVtuberCategoriesResponse.ProtoReflect.Descriptor instead.
func (*GetVtuberCategoriesResponse) Descriptor() ([]byte, []int) {
	return file_vtubers_v1_vtubercategory_proto_rawDescGZIP(), []int{2}
}

func (x *GetVtuberCategoriesResponse) GetData() []*VtuberCategory {
	if x != nil {
		return x.Data
	}
	return nil
}

var File_vtubers_v1_vtubercategory_proto protoreflect.FileDescriptor

var file_vtubers_v1_vtubercategory_proto_rawDesc = string([]byte{
	0x0a, 0x1f, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x74, 0x75,
	0x62, 0x65, 0x72, 0x63, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x0e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72, 0x73, 0x2e, 0x76,
	0x31, 0x1a, 0x14, 0x61, 0x75, 0x74, 0x68, 0x7a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x68,
	0x7a, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64,
	0x2f, 0x76, 0x31, 0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1a, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x61, 0x67,
	0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x76, 0x31, 0x2f, 0x70, 0x72, 0x6f, 0x66, 0x69, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x22, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x6c, 0x5f, 0x6d, 0x65, 0x64, 0x69, 0x61, 0x5f, 0x6c,
	0x69, 0x6e, 0x6b, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x20, 0x0a, 0x0e, 0x56, 0x74,
	0x75, 0x62, 0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x1f, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x56, 0x74, 0x75, 0x62, 0x65, 0x72, 0x43, 0x61, 0x74, 0x65,
	0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x51, 0x0a,
	0x1b, 0x47, 0x65, 0x74, 0x56, 0x74, 0x75, 0x62, 0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x32, 0x0a, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x74, 0x75, 0x62,
	0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x32, 0x8d, 0x01, 0x0a, 0x15, 0x56, 0x74, 0x75, 0x62, 0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67,
	0x6f, 0x72, 0x79, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x74, 0x0a, 0x16, 0x47, 0x65,
	0x74, 0x41, 0x6c, 0x6c, 0x56, 0x74, 0x75, 0x62, 0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f,
	0x72, 0x69, 0x65, 0x73, 0x12, 0x2d, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x74, 0x75, 0x62, 0x65,
	0x72, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x6c, 0x6c, 0x56, 0x74, 0x75, 0x62,
	0x65, 0x72, 0x43, 0x61, 0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x74, 0x75, 0x62, 0x65, 0x72, 0x43, 0x61,
	0x74, 0x65, 0x67, 0x6f, 0x72, 0x69, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x42, 0x34, 0x5a, 0x32, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x6e,
	0x73, 0x70, 0x2d, 0x69, 0x6e, 0x63, 0x2f, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x76, 0x74, 0x75, 0x62, 0x65, 0x72, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x74, 0x75,
	0x62, 0x65, 0x72, 0x73, 0x76, 0x31, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_vtubers_v1_vtubercategory_proto_rawDescOnce sync.Once
	file_vtubers_v1_vtubercategory_proto_rawDescData []byte
)

func file_vtubers_v1_vtubercategory_proto_rawDescGZIP() []byte {
	file_vtubers_v1_vtubercategory_proto_rawDescOnce.Do(func() {
		file_vtubers_v1_vtubercategory_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtubercategory_proto_rawDesc), len(file_vtubers_v1_vtubercategory_proto_rawDesc)))
	})
	return file_vtubers_v1_vtubercategory_proto_rawDescData
}

var file_vtubers_v1_vtubercategory_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_vtubers_v1_vtubercategory_proto_goTypes = []any{
	(*VtuberCategory)(nil),                // 0: api.vtubers.v1.VtuberCategory
	(*GetAllVtuberCategoriesRequest)(nil), // 1: api.vtubers.v1.GetAllVtuberCategoriesRequest
	(*GetVtuberCategoriesResponse)(nil),   // 2: api.vtubers.v1.GetVtuberCategoriesResponse
}
var file_vtubers_v1_vtubercategory_proto_depIdxs = []int32{
	0, // 0: api.vtubers.v1.GetVtuberCategoriesResponse.data:type_name -> api.vtubers.v1.VtuberCategory
	1, // 1: api.vtubers.v1.VtuberCategoryService.GetAllVtuberCategories:input_type -> api.vtubers.v1.GetAllVtuberCategoriesRequest
	2, // 2: api.vtubers.v1.VtuberCategoryService.GetAllVtuberCategories:output_type -> api.vtubers.v1.GetVtuberCategoriesResponse
	2, // [2:3] is the sub-list for method output_type
	1, // [1:2] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_vtubers_v1_vtubercategory_proto_init() }
func file_vtubers_v1_vtubercategory_proto_init() {
	if File_vtubers_v1_vtubercategory_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_vtubers_v1_vtubercategory_proto_rawDesc), len(file_vtubers_v1_vtubercategory_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_vtubers_v1_vtubercategory_proto_goTypes,
		DependencyIndexes: file_vtubers_v1_vtubercategory_proto_depIdxs,
		MessageInfos:      file_vtubers_v1_vtubercategory_proto_msgTypes,
	}.Build()
	File_vtubers_v1_vtubercategory_proto = out.File
	file_vtubers_v1_vtubercategory_proto_goTypes = nil
	file_vtubers_v1_vtubercategory_proto_depIdxs = nil
}
