// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        (unknown)
// source: userdeliveryaddress/v1/userdeliveryaddress.proto

package userdeliveryaddress

import (
	_ "github.com/nsp-inc/vtuber/api/authz/v1"
	v1 "github.com/nsp-inc/vtuber/api/shared/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type AddUserDeliveryAddressRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Recipient             string                 `protobuf:"bytes,1,opt,name=recipient,proto3" json:"recipient,omitempty" validate:"required"`
	PhoneNumber           string                 `protobuf:"bytes,2,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty" validate:"required"`
	PostalCode            string                 `protobuf:"bytes,3,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty" validate:"required"`
	Prefecture            string                 `protobuf:"bytes,4,opt,name=prefecture,proto3" json:"prefecture,omitempty" validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"`
	City                  string                 `protobuf:"bytes,5,opt,name=city,proto3" json:"city,omitempty" validate:"required"`
	AddressLine1          string                 `protobuf:"bytes,6,opt,name=address_line1,json=addressLine1,proto3" json:"address_line1,omitempty" validate:"required"`
	AddressLine2          *string                `protobuf:"bytes,7,opt,name=address_line2,json=addressLine2,proto3,oneof" json:"address_line2,omitempty"`
	PreferredDeliveryTime *string                `protobuf:"bytes,8,opt,name=preferred_delivery_time,json=preferredDeliveryTime,proto3,oneof" json:"preferred_delivery_time,omitempty"`
	PreferredDeliveryDate *timestamppb.Timestamp `protobuf:"bytes,9,opt,name=preferred_delivery_date,json=preferredDeliveryDate,proto3,oneof" json:"preferred_delivery_date,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *AddUserDeliveryAddressRequest) Reset() {
	*x = AddUserDeliveryAddressRequest{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserDeliveryAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserDeliveryAddressRequest) ProtoMessage() {}

func (x *AddUserDeliveryAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserDeliveryAddressRequest.ProtoReflect.Descriptor instead.
func (*AddUserDeliveryAddressRequest) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{0}
}

func (x *AddUserDeliveryAddressRequest) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetPrefecture() string {
	if x != nil {
		return x.Prefecture
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetAddressLine1() string {
	if x != nil {
		return x.AddressLine1
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetAddressLine2() string {
	if x != nil && x.AddressLine2 != nil {
		return *x.AddressLine2
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetPreferredDeliveryTime() string {
	if x != nil && x.PreferredDeliveryTime != nil {
		return *x.PreferredDeliveryTime
	}
	return ""
}

func (x *AddUserDeliveryAddressRequest) GetPreferredDeliveryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PreferredDeliveryDate
	}
	return nil
}

type AddUserDeliveryAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *UserDeliveryAddress   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AddUserDeliveryAddressResponse) Reset() {
	*x = AddUserDeliveryAddressResponse{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AddUserDeliveryAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AddUserDeliveryAddressResponse) ProtoMessage() {}

func (x *AddUserDeliveryAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AddUserDeliveryAddressResponse.ProtoReflect.Descriptor instead.
func (*AddUserDeliveryAddressResponse) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{1}
}

func (x *AddUserDeliveryAddressResponse) GetData() *UserDeliveryAddress {
	if x != nil {
		return x.Data
	}
	return nil
}

type UserDeliveryAddress struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`
	Recipient             string                 `protobuf:"bytes,2,opt,name=recipient,proto3" json:"recipient,omitempty"`
	PhoneNumber           string                 `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty"`
	PostalCode            string                 `protobuf:"bytes,4,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty"`
	Prefecture            string                 `protobuf:"bytes,5,opt,name=prefecture,proto3" json:"prefecture,omitempty"`
	City                  string                 `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty"`
	AddressLine1          string                 `protobuf:"bytes,7,opt,name=address_line1,json=addressLine1,proto3" json:"address_line1,omitempty"`
	AddressLine2          *string                `protobuf:"bytes,8,opt,name=address_line2,json=addressLine2,proto3,oneof" json:"address_line2,omitempty"`
	PreferredDeliveryTime *string                `protobuf:"bytes,9,opt,name=preferred_delivery_time,json=preferredDeliveryTime,proto3,oneof" json:"preferred_delivery_time,omitempty"`
	PreferredDeliveryDate *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=preferred_delivery_date,json=preferredDeliveryDate,proto3,oneof" json:"preferred_delivery_date,omitempty"`
	CreatedAt             *timestamppb.Timestamp `protobuf:"bytes,11,opt,name=created_at,json=createdAt,proto3" json:"created_at,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UserDeliveryAddress) Reset() {
	*x = UserDeliveryAddress{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UserDeliveryAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UserDeliveryAddress) ProtoMessage() {}

func (x *UserDeliveryAddress) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UserDeliveryAddress.ProtoReflect.Descriptor instead.
func (*UserDeliveryAddress) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{2}
}

func (x *UserDeliveryAddress) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UserDeliveryAddress) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *UserDeliveryAddress) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UserDeliveryAddress) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *UserDeliveryAddress) GetPrefecture() string {
	if x != nil {
		return x.Prefecture
	}
	return ""
}

func (x *UserDeliveryAddress) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UserDeliveryAddress) GetAddressLine1() string {
	if x != nil {
		return x.AddressLine1
	}
	return ""
}

func (x *UserDeliveryAddress) GetAddressLine2() string {
	if x != nil && x.AddressLine2 != nil {
		return *x.AddressLine2
	}
	return ""
}

func (x *UserDeliveryAddress) GetPreferredDeliveryTime() string {
	if x != nil && x.PreferredDeliveryTime != nil {
		return *x.PreferredDeliveryTime
	}
	return ""
}

func (x *UserDeliveryAddress) GetPreferredDeliveryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PreferredDeliveryDate
	}
	return nil
}

func (x *UserDeliveryAddress) GetCreatedAt() *timestamppb.Timestamp {
	if x != nil {
		return x.CreatedAt
	}
	return nil
}

type GetCurrentUsersDeliveryAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCurrentUsersDeliveryAddressRequest) Reset() {
	*x = GetCurrentUsersDeliveryAddressRequest{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentUsersDeliveryAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentUsersDeliveryAddressRequest) ProtoMessage() {}

func (x *GetCurrentUsersDeliveryAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentUsersDeliveryAddressRequest.ProtoReflect.Descriptor instead.
func (*GetCurrentUsersDeliveryAddressRequest) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{3}
}

type GetCurrentUsersDeliveryAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *UserDeliveryAddress   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetCurrentUsersDeliveryAddressResponse) Reset() {
	*x = GetCurrentUsersDeliveryAddressResponse{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetCurrentUsersDeliveryAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetCurrentUsersDeliveryAddressResponse) ProtoMessage() {}

func (x *GetCurrentUsersDeliveryAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetCurrentUsersDeliveryAddressResponse.ProtoReflect.Descriptor instead.
func (*GetCurrentUsersDeliveryAddressResponse) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{4}
}

func (x *GetCurrentUsersDeliveryAddressResponse) GetData() *UserDeliveryAddress {
	if x != nil {
		return x.Data
	}
	return nil
}

type GetUserDeliveryAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserDeliveryAddressRequest) Reset() {
	*x = GetUserDeliveryAddressRequest{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserDeliveryAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDeliveryAddressRequest) ProtoMessage() {}

func (x *GetUserDeliveryAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDeliveryAddressRequest.ProtoReflect.Descriptor instead.
func (*GetUserDeliveryAddressRequest) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{5}
}

func (x *GetUserDeliveryAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type GetUserDeliveryAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Data          *UserDeliveryAddress   `protobuf:"bytes,1,opt,name=data,proto3" json:"data,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserDeliveryAddressResponse) Reset() {
	*x = GetUserDeliveryAddressResponse{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserDeliveryAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserDeliveryAddressResponse) ProtoMessage() {}

func (x *GetUserDeliveryAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserDeliveryAddressResponse.ProtoReflect.Descriptor instead.
func (*GetUserDeliveryAddressResponse) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{6}
}

func (x *GetUserDeliveryAddressResponse) GetData() *UserDeliveryAddress {
	if x != nil {
		return x.Data
	}
	return nil
}

type DeleteUserDeliveryAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserDeliveryAddressRequest) Reset() {
	*x = DeleteUserDeliveryAddressRequest{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserDeliveryAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserDeliveryAddressRequest) ProtoMessage() {}

func (x *DeleteUserDeliveryAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserDeliveryAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserDeliveryAddressRequest) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteUserDeliveryAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

type UpdateUserDeliveryAddressRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	Id                    int64                  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty" validate:"required"`
	Recipient             string                 `protobuf:"bytes,2,opt,name=recipient,proto3" json:"recipient,omitempty" validate:"required"`
	PhoneNumber           string                 `protobuf:"bytes,3,opt,name=phone_number,json=phoneNumber,proto3" json:"phone_number,omitempty" validate:"required"`
	PostalCode            string                 `protobuf:"bytes,4,opt,name=postal_code,json=postalCode,proto3" json:"postal_code,omitempty" validate:"required"`
	Prefecture            string                 `protobuf:"bytes,5,opt,name=prefecture,proto3" json:"prefecture,omitempty" validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"`
	City                  string                 `protobuf:"bytes,6,opt,name=city,proto3" json:"city,omitempty" validate:"required"`
	AddressLine1          string                 `protobuf:"bytes,7,opt,name=address_line1,json=addressLine1,proto3" json:"address_line1,omitempty" validate:"required"`
	AddressLine2          *string                `protobuf:"bytes,8,opt,name=address_line2,json=addressLine2,proto3,oneof" json:"address_line2,omitempty"`
	PreferredDeliveryTime *string                `protobuf:"bytes,9,opt,name=preferred_delivery_time,json=preferredDeliveryTime,proto3,oneof" json:"preferred_delivery_time,omitempty"`
	PreferredDeliveryDate *timestamppb.Timestamp `protobuf:"bytes,10,opt,name=preferred_delivery_date,json=preferredDeliveryDate,proto3,oneof" json:"preferred_delivery_date,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *UpdateUserDeliveryAddressRequest) Reset() {
	*x = UpdateUserDeliveryAddressRequest{}
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateUserDeliveryAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateUserDeliveryAddressRequest) ProtoMessage() {}

func (x *UpdateUserDeliveryAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateUserDeliveryAddressRequest.ProtoReflect.Descriptor instead.
func (*UpdateUserDeliveryAddressRequest) Descriptor() ([]byte, []int) {
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateUserDeliveryAddressRequest) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *UpdateUserDeliveryAddressRequest) GetRecipient() string {
	if x != nil {
		return x.Recipient
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetPhoneNumber() string {
	if x != nil {
		return x.PhoneNumber
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetPostalCode() string {
	if x != nil {
		return x.PostalCode
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetPrefecture() string {
	if x != nil {
		return x.Prefecture
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetCity() string {
	if x != nil {
		return x.City
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetAddressLine1() string {
	if x != nil {
		return x.AddressLine1
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetAddressLine2() string {
	if x != nil && x.AddressLine2 != nil {
		return *x.AddressLine2
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetPreferredDeliveryTime() string {
	if x != nil && x.PreferredDeliveryTime != nil {
		return *x.PreferredDeliveryTime
	}
	return ""
}

func (x *UpdateUserDeliveryAddressRequest) GetPreferredDeliveryDate() *timestamppb.Timestamp {
	if x != nil {
		return x.PreferredDeliveryDate
	}
	return nil
}

var File_userdeliveryaddress_v1_userdeliveryaddress_proto protoreflect.FileDescriptor

var file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDesc = string([]byte{
	0x0a, 0x30, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1a, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x1a, 0x14,
	0x61, 0x75, 0x74, 0x68, 0x7a, 0x2f, 0x76, 0x31, 0x2f, 0x61, 0x75, 0x74, 0x68, 0x7a, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2f, 0x76, 0x31,
	0x2f, 0x67, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe4,
	0x03, 0x0a, 0x1d, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x21,
	0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x63, 0x74, 0x75,
	0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x28, 0x0a, 0x0d, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e,
	0x65, 0x32, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88,
	0x01, 0x01, 0x12, 0x57, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48,
	0x02, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x42, 0x1a, 0x0a,
	0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x5f, 0x64, 0x61, 0x74, 0x65, 0x22, 0x65, 0x0a, 0x1e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0xa5, 0x04, 0x0a,
	0x13, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65,
	0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e,
	0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74,
	0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x63,
	0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12,
	0x28, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x88, 0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x15, 0x70, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54,
	0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x57, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72,
	0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x39, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x61, 0x74, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x64, 0x41, 0x74, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x42, 0x1a, 0x0a, 0x18,
	0x5f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f,
	0x64, 0x61, 0x74, 0x65, 0x22, 0x27, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65,
	0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0x6d, 0x0a,
	0x26, 0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x22, 0x2f, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0x65, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x43, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x04,
	0x64, 0x61, 0x74, 0x61, 0x22, 0x32, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x22, 0xf7, 0x03, 0x0a, 0x20, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a,
	0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x72, 0x65, 0x63, 0x69, 0x70, 0x69, 0x65, 0x6e, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x70,
	0x68, 0x6f, 0x6e, 0x65, 0x5f, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x70, 0x68, 0x6f, 0x6e, 0x65, 0x4e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x12, 0x1f,
	0x0a, 0x0b, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x61, 0x6c, 0x43, 0x6f, 0x64, 0x65, 0x12,
	0x1e, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65, 0x63, 0x74, 0x75, 0x72, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x63, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63,
	0x69, 0x74, 0x79, 0x12, 0x23, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c,
	0x69, 0x6e, 0x65, 0x31, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x31, 0x12, 0x28, 0x0a, 0x0d, 0x61, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x0c, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x4c, 0x69, 0x6e, 0x65, 0x32, 0x88,
	0x01, 0x01, 0x12, 0x3b, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x15, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x54, 0x69, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x57, 0x0a, 0x17, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x48, 0x02, 0x52, 0x15,
	0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x44, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x5f, 0x6c, 0x69, 0x6e, 0x65, 0x32, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70,
	0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x42, 0x1a, 0x0a, 0x18, 0x5f, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x5f, 0x64, 0x61,
	0x74, 0x65, 0x32, 0x8f, 0x06, 0x0a, 0x1a, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x12, 0x97, 0x01, 0x0a, 0x16, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x39, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65,
	0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73,
	0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x64, 0x64, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69,
	0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x06, 0x82, 0xb5, 0x18, 0x02, 0x08, 0x01, 0x12, 0xae, 0x01, 0x0a, 0x1d,
	0x47, 0x65, 0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65,
	0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x41, 0x2e,
	0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x75,
	0x72, 0x72, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x42, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x55, 0x73, 0x65, 0x72, 0x73, 0x44, 0x65, 0x6c,
	0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x06, 0x82, 0xb5, 0x18, 0x02, 0x08, 0x01, 0x12, 0x9d, 0x01, 0x0a,
	0x1a, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x42, 0x79, 0x49, 0x64, 0x12, 0x39, 0x2e, 0x61, 0x70,
	0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72,
	0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3a, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65,
	0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x08, 0x82, 0xb5, 0x18, 0x04, 0x08, 0x01, 0x10, 0x01, 0x12, 0x81, 0x01, 0x0a,
	0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76,
	0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3c, 0x2e, 0x61, 0x70, 0x69,
	0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73,
	0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61, 0x70, 0x69, 0x2e, 0x73,
	0x68, 0x61, 0x72, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e, 0x65, 0x72, 0x69, 0x63,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x06, 0x82, 0xb5, 0x18, 0x02, 0x08, 0x01,
	0x12, 0x81, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44,
	0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3c,
	0x2e, 0x61, 0x70, 0x69, 0x2e, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72,
	0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x44, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1e, 0x2e, 0x61,
	0x70, 0x69, 0x2e, 0x73, 0x68, 0x61, 0x72, 0x65, 0x64, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x6e,
	0x65, 0x72, 0x69, 0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x06, 0x82, 0xb5,
	0x18, 0x02, 0x08, 0x01, 0x42, 0x4a, 0x5a, 0x48, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x6e, 0x73, 0x70, 0x2d, 0x69, 0x6e, 0x63, 0x2f, 0x76, 0x74, 0x75, 0x62, 0x65,
	0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x75, 0x73, 0x65, 0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65,
	0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x2f, 0x76, 0x31, 0x3b, 0x75, 0x73, 0x65,
	0x72, 0x64, 0x65, 0x6c, 0x69, 0x76, 0x65, 0x72, 0x79, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescOnce sync.Once
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescData []byte
)

func file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescGZIP() []byte {
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescOnce.Do(func() {
		file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDesc), len(file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDesc)))
	})
	return file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDescData
}

var file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_userdeliveryaddress_v1_userdeliveryaddress_proto_goTypes = []any{
	(*AddUserDeliveryAddressRequest)(nil),          // 0: api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest
	(*AddUserDeliveryAddressResponse)(nil),         // 1: api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse
	(*UserDeliveryAddress)(nil),                    // 2: api.userdeliveryaddress.v1.UserDeliveryAddress
	(*GetCurrentUsersDeliveryAddressRequest)(nil),  // 3: api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressRequest
	(*GetCurrentUsersDeliveryAddressResponse)(nil), // 4: api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse
	(*GetUserDeliveryAddressRequest)(nil),          // 5: api.userdeliveryaddress.v1.GetUserDeliveryAddressRequest
	(*GetUserDeliveryAddressResponse)(nil),         // 6: api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse
	(*DeleteUserDeliveryAddressRequest)(nil),       // 7: api.userdeliveryaddress.v1.DeleteUserDeliveryAddressRequest
	(*UpdateUserDeliveryAddressRequest)(nil),       // 8: api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest
	(*timestamppb.Timestamp)(nil),                  // 9: google.protobuf.Timestamp
	(*v1.GenericResponse)(nil),                     // 10: api.shared.v1.GenericResponse
}
var file_userdeliveryaddress_v1_userdeliveryaddress_proto_depIdxs = []int32{
	9,  // 0: api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest.preferred_delivery_date:type_name -> google.protobuf.Timestamp
	2,  // 1: api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse.data:type_name -> api.userdeliveryaddress.v1.UserDeliveryAddress
	9,  // 2: api.userdeliveryaddress.v1.UserDeliveryAddress.preferred_delivery_date:type_name -> google.protobuf.Timestamp
	9,  // 3: api.userdeliveryaddress.v1.UserDeliveryAddress.created_at:type_name -> google.protobuf.Timestamp
	2,  // 4: api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse.data:type_name -> api.userdeliveryaddress.v1.UserDeliveryAddress
	2,  // 5: api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse.data:type_name -> api.userdeliveryaddress.v1.UserDeliveryAddress
	9,  // 6: api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest.preferred_delivery_date:type_name -> google.protobuf.Timestamp
	0,  // 7: api.userdeliveryaddress.v1.UserDeliveryAddressService.AddUserDeliveryAddress:input_type -> api.userdeliveryaddress.v1.AddUserDeliveryAddressRequest
	3,  // 8: api.userdeliveryaddress.v1.UserDeliveryAddressService.GetCurrentUserDeliveryAddress:input_type -> api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressRequest
	5,  // 9: api.userdeliveryaddress.v1.UserDeliveryAddressService.GetUserDeliveryAddressById:input_type -> api.userdeliveryaddress.v1.GetUserDeliveryAddressRequest
	7,  // 10: api.userdeliveryaddress.v1.UserDeliveryAddressService.DeleteUserDeliveryAddress:input_type -> api.userdeliveryaddress.v1.DeleteUserDeliveryAddressRequest
	8,  // 11: api.userdeliveryaddress.v1.UserDeliveryAddressService.UpdateUserDeliveryAddress:input_type -> api.userdeliveryaddress.v1.UpdateUserDeliveryAddressRequest
	1,  // 12: api.userdeliveryaddress.v1.UserDeliveryAddressService.AddUserDeliveryAddress:output_type -> api.userdeliveryaddress.v1.AddUserDeliveryAddressResponse
	4,  // 13: api.userdeliveryaddress.v1.UserDeliveryAddressService.GetCurrentUserDeliveryAddress:output_type -> api.userdeliveryaddress.v1.GetCurrentUsersDeliveryAddressResponse
	6,  // 14: api.userdeliveryaddress.v1.UserDeliveryAddressService.GetUserDeliveryAddressById:output_type -> api.userdeliveryaddress.v1.GetUserDeliveryAddressResponse
	10, // 15: api.userdeliveryaddress.v1.UserDeliveryAddressService.DeleteUserDeliveryAddress:output_type -> api.shared.v1.GenericResponse
	10, // 16: api.userdeliveryaddress.v1.UserDeliveryAddressService.UpdateUserDeliveryAddress:output_type -> api.shared.v1.GenericResponse
	12, // [12:17] is the sub-list for method output_type
	7,  // [7:12] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_userdeliveryaddress_v1_userdeliveryaddress_proto_init() }
func file_userdeliveryaddress_v1_userdeliveryaddress_proto_init() {
	if File_userdeliveryaddress_v1_userdeliveryaddress_proto != nil {
		return
	}
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[0].OneofWrappers = []any{}
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[2].OneofWrappers = []any{}
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes[8].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDesc), len(file_userdeliveryaddress_v1_userdeliveryaddress_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_userdeliveryaddress_v1_userdeliveryaddress_proto_goTypes,
		DependencyIndexes: file_userdeliveryaddress_v1_userdeliveryaddress_proto_depIdxs,
		MessageInfos:      file_userdeliveryaddress_v1_userdeliveryaddress_proto_msgTypes,
	}.Build()
	File_userdeliveryaddress_v1_userdeliveryaddress_proto = out.File
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_goTypes = nil
	file_userdeliveryaddress_v1_userdeliveryaddress_proto_depIdxs = nil
}
