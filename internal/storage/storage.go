package storagev1

import (
	"bytes"
	"context"
	"encoding/json"
	"errors"
	"mime/multipart"
	"net/http"
	"slices"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/gabriel-vasile/mimetype"
	"github.com/google/uuid"
	filesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/files"
	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/web"
)

type FileUpload struct {
	Data []byte
	Ext  string
	Mime string
}

type StorageService struct {
	storageRepo *filesqueries.Queries
	s3Client    *s3.Client
}

func NewStorageService(storeRepo *filesqueries.Queries) (*StorageService, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO())
	if err != nil {
		return nil, err
	}
	return &StorageService{
		storageRepo: storeRepo,
		s3Client:    s3.NewFromConfig(cfg),
	}, nil
}

func (s *StorageService) ValidateAndUploadFromTemp(ctx context.Context, tempPath *string, fileType FileType, dir string, required bool, prev *string) (string, error) {
	if tempPath == nil || *tempPath == "" {
		if required {
			return "", errors.New(
				web.GetTranslation(ctx, "fileRequired", nil),
			)
		} else {
			if prev != nil {
				return *prev, nil
			}
		}
		return "", nil
	}

	if !strings.HasPrefix(*tempPath, "temp/") {
		if prev != nil {
			return *prev, nil
		}
		return "", errors.New(
			web.GetTranslation(ctx, "invalidFilePath", nil),
		)
	}

	file, err := s.GetUploadByFileName(ctx, *tempPath)
	if err != nil {
		return "", errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "file", nil),
			}),
		)
	}

	isValidMimeType := s.CheckMimeType(file.MimeType, fileType)
	if !isValidMimeType {
		return "", errors.New(web.GetTranslation(ctx, "invalidFileType", nil))
	}
	var path string
	if prev != nil {
		path, err = s.UpdateFromTemp(ctx, *tempPath, *prev, dir)
	} else {
		path, err = s.MoveFromTemp(ctx, *tempPath, dir)
	}
	if err != nil {
		return "", err
	}
	_ = s.storageRepo.DeleteFile(ctx, *tempPath)
	return path, nil
}

func (s *StorageService) GetUploadByFileName(ctx context.Context, fileName string) (filesqueries.File, error) {
	return s.storageRepo.GetFileByPath(ctx, fileName)
}

func (s *StorageService) UpdateFromTemp(ctx context.Context, tempPath string, prevPath string, dir string) (string, error) {
	finalPath, err := s.MoveFromTemp(ctx, tempPath, dir)
	if err != nil {
		return "", err
	}
	err = s.DeleteFile(ctx, prevPath)
	if err != nil {
		return "", err
	}
	return finalPath, nil
}

func (s *StorageService) DeleteFile(ctx context.Context, path string) error {
	// _, err := s.s3Client.DeleteObject(ctx, &s3.DeleteObjectInput{
	// 	Bucket: aws.String(env.GetString("AWS_BUCKET_NAME", "")),
	// 	Key:    aws.String(path),
	// })
	// if err != nil {
	// 	return err
	// }
	return nil
}

func (s *StorageService) CheckMimeType(ext string, fileType FileType) bool {
	val, ok := FileTypes[fileType]
	if ok {
		return slices.Contains(val, ext)
	}
	return false
}

func (s *StorageService) MoveFile(ctx context.Context, path string, newPath string) error {
	_, err := s.s3Client.CopyObject(ctx, &s3.CopyObjectInput{
		Bucket:     aws.String(env.GetString("AWS_BUCKET_NAME", "")),
		CopySource: aws.String("/" + env.GetString("AWS_BUCKET_NAME", "") + "/" + path),
		Key:        aws.String(newPath),
	})
	if err != nil {
		return err
	}
	err = s.DeleteFile(ctx, path)
	if err != nil {
		return err
	}
	return nil
}

func (s *StorageService) MoveFromTemp(ctx context.Context, tempPath string, dir string) (string, error) {
	if !strings.HasPrefix(tempPath, "temp/") {
		return "", errors.New(
			web.GetTranslation(ctx, "invalidFilePath", nil),
		)
	}
	fileName := strings.TrimLeft(tempPath, "temp/")
	finalPath := dir + "/" + fileName
	err := s.MoveFile(ctx, tempPath, finalPath)
	if err != nil {
		return "", err
	}
	return finalPath, nil
}

func (s *StorageService) SaveFile(data []byte, ext, mime string, dir string) (string, error) {
	reader := bytes.NewReader(data)
	newName := uuid.New().String() + ext
	finalPath := dir + "/" + newName
	_, err := s.s3Client.PutObject(context.TODO(), &s3.PutObjectInput{
		Bucket:      aws.String(env.GetString("AWS_BUCKET_NAME", "")),
		Key:         aws.String(finalPath),
		Body:        reader,
		ContentType: aws.String(mime),
	})

	if err != nil {
		return "", err
	}
	return finalPath, nil
}

func (s *StorageService) FileUploadHandler(w http.ResponseWriter, r *http.Request) {
	if r.Method != http.MethodPost {
		http.Error(w, "Invalid request method", http.StatusBadRequest)
		return
	}
	err := r.ParseMultipartForm(15 << 20)
	if err != nil {
		http.Error(w, "File size exceeds the limit", http.StatusBadRequest)
		return
	}

	// Retrieve the file from form data
	file, handler, err := r.FormFile("file")
	if err != nil {
		http.Error(w, "Error retrieving the file", http.StatusBadRequest)
		return
	}
	defer func(file multipart.File) {
		err := file.Close()
		if err != nil {
			http.Error(w, "Error closing the file", http.StatusBadRequest)
			return
		}
	}(file)

	buf := new(bytes.Buffer)

	src, err := handler.Open()

	if err != nil {
		http.Error(w, "Error reading the file", http.StatusBadRequest)
		return
	}

	_, err = buf.ReadFrom(src)

	if err != nil {
		http.Error(w, "Error reading the file", http.StatusBadRequest)
		return
	}

	data := buf.Bytes()

	info := mimetype.Detect(data)
	mimeType := info.String()
	ext := info.Extension()
	path, err := s.SaveFile(data, ext, mimeType, "temp")
	if err != nil {
		http.Error(w, "Error saving the file", http.StatusBadRequest)
		return
	}
	_, err = s.storageRepo.AddFile(context.Background(), filesqueries.AddFileParams{
		FilePath: path,
		Size:     int64(len(data)),
		MimeType: mimeType,
	})
	if err != nil {
		http.Error(w, "Error saving the file to database", http.StatusBadRequest)
		return

	}

	w.Header().Set("Content-Type", "application/json")
	w.WriteHeader(http.StatusOK)
	err = json.NewEncoder(w).Encode(map[string]string{"message": "File uploaded successfully", "path": path})
	if err != nil {
		http.Error(w, "Error encoding the response", http.StatusBadRequest)
		return
	}
}
