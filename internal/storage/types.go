package storagev1

type FileType string

var ImageFileType FileType = "picture"
var VideoFileType FileType = "video"
var AudioFileType FileType = "audio"
var DocumentFileType FileType = "document"

var FileTypes = map[FileType][]string{
	ImageFileType: {"image/png",
		"image/jpeg",
		"image/gif",
		"image/webp",
		"image/bmp", "image/x-bmp", "image/x-ms-bmp",
		"image/x-icon",
		"image/tiff",
		"image/avif",
		"image/heic", "image/heic-sequence",
		"image/heif", "image/heif-sequence",
		"image/jp2",
		"image/jxl",
		"image/svg+xml",
		"image/vnd.adobe.photoshop", "image/x-psd",
		"image/x-xpixmap",
		"image/jxr", "image/vnd.ms-photo",
		"image/vnd.djvu",
		"image/vnd.dwg", "image/x-dwg",
		"image/vnd.radiance",
		"image/x-xcf"},

	VideoFileType: {"video/mp4",
		"video/quicktime",
		"video/x-msvideo", "video/avi", "video/msvideo",
		"video/x-matroska",
		"video/webm",
		"video/x-flv",
		"video/mpeg",
		"video/ogg",
		"video/x-ms-asf", "video/asf", "video/x-ms-wmv",
		"video/3gpp", "video/3gp",
		"video/3gpp2", "video/3g2",
		"video/x-m4v",
		"video/mj2",
		"video/vnd.dvb.file",
		"video/quicktime",
		"application/vnd.rn-realmedia-vbr"},

	AudioFileType: {"audio/mpeg", "audio/x-mpeg", "audio/mp3", // MP3
		"audio/flac",                                                              // FLAC
		"audio/midi", "audio/mid", "audio/sp-midi", "audio/x-mid", "audio/x-midi", // MIDI
		"audio/ape",                 // APE
		"audio/musepack",            // MPC
		"audio/amr", "audio/amr-nb", // AMR
		"audio/wav", "audio/x-wav", "audio/vnd.wave", "audio/wave", // WAV
		"audio/aiff", "audio/x-aiff", // AIFF
		"audio/aac",                                // AAC
		"audio/basic",                              // AU
		"audio/ogg",                                // OGG
		"audio/qcelp",                              // QCP
		"audio/x-unknown",                          // VOC
		"audio/x-m4a",                              // M4A
		"audio/webm",                               // WebM Audio
		"audio/vnd.apple.mpegurl", "audio/mpegurl", // M3U
	},

	DocumentFileType: {"application/pdf", "application/x-pdf", // PDF
		"application/msword", "application/vnd.ms-word", // DOC
		"application/vnd.openxmlformats-officedocument.wordprocessingml.document", // DOCX
		"application/vnd.ms-excel", "application/msexcel", // XLS
		"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", // XLSX
		"application/vnd.ms-powerpoint", "application/mspowerpoint",         // PPT
		"application/vnd.openxmlformats-officedocument.presentationml.presentation", // PPTX
		"application/rtf", "text/rtf", // RTF
		"text/plain",                // TXT
		"text/csv",                  // CSV
		"text/tab-separated-values", // TSV
		"application/vnd.oasis.opendocument.text", "application/x-vnd.oasis.opendocument.text", // ODT
		"application/vnd.oasis.opendocument.text-template", "application/x-vnd.oasis.opendocument.text-template", // OTT
		"application/vnd.oasis.opendocument.spreadsheet", "application/x-vnd.oasis.opendocument.spreadsheet", // ODS
		"application/vnd.oasis.opendocument.spreadsheet-template", "application/x-vnd.oasis.opendocument.spreadsheet-template", // OTS
		"application/vnd.oasis.opendocument.presentation", "application/x-vnd.oasis.opendocument.presentation", // ODP
		"application/vnd.oasis.opendocument.presentation-template", "application/x-vnd.oasis.opendocument.presentation-template", // OTP
		"application/vnd.oasis.opendocument.graphics", "application/x-vnd.oasis.opendocument.graphics", // ODG
		"application/vnd.oasis.opendocument.graphics-template", "application/x-vnd.oasis.opendocument.graphics-template", // OTG
		"application/vnd.oasis.opendocument.formula", "application/x-vnd.oasis.opendocument.formula", // ODF
		"application/vnd.oasis.opendocument.chart", "application/x-vnd.oasis.opendocument.chart", // ODC
		"application/vnd.sun.xml.calc",         // SXC
		"application/epub+zip",                 // EPUB
		"application/x-mobipocket-ebook",       // MOBI
		"application/vnd.fdf",                  // FDF
		"application/vnd.ms-publisher",         // PUB
		"application/vnd.ms-outlook",           // MSG
		"application/vnd.google-earth.kml+xml", // KML
		"application/vnd.apple.mpegurl",        // M3U
		"text/calendar",                        // ICS
	},
}
