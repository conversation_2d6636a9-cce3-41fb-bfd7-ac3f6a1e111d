package content

import (
	"context"
	"errors"
	"strconv"

	postcommentsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/postcomments"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	"github.com/nsp-inc/vtuber/internal/handlers/notifications"

	"connectrpc.com/connect"
	contentv1 "github.com/nsp-inc/vtuber/api/content/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"

	postsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/posts"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type PostCommentService struct {
	repo                *postcommentsqueries.Queries
	postRepo            *postsqueries.Queries
	notificationService *notifications.NotificationService
	vtuberProfileRepo   *vtuberprofilesqueries.Queries
}

func NewPostCommentService(postCommentRepo *postcommentsqueries.Queries, postRepo *postsqueries.Queries, notificationService *notifications.NotificationService, vtuberProfileRepo *vtuberprofilesqueries.Queries) *PostCommentService {
	return &PostCommentService{
		repo:                postCommentRepo,
		postRepo:            postRepo,
		notificationService: notificationService,
		vtuberProfileRepo:   vtuberProfileRepo,
	}
}

func (s PostCommentService) AddPostComment(ctx context.Context, c *connect.Request[contentv1.AddPostCommentRequest]) (*connect.Response[contentv1.AddPostCommentResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	post, err := s.postRepo.GetPostByID(ctx, postsqueries.GetPostByIDParams{
		ID: strconv.FormatInt(c.Msg.PostId, 10),
	})
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}
	var repliedUserId *int64
	if c.Msg.ParentId != nil {
		comment, err := s.repo.GetPostCommentById(ctx, *c.Msg.ParentId)
		if err != nil {
			return nil, errors.New(
				web.GetTranslation(ctx, "fieldNotFound", map[string]string{
					"field": web.GetTranslation(ctx, "comment", nil),
				}),
			)
		}
		repliedUserId = &comment.UserID
	}

	var vtuberId *int64
	if c.Msg.AsVtuber != nil && *c.Msg.AsVtuber {
		vtuberId = sessionUser.VtuberId
	} else {
		vtuberId = nil
	}
	comment, err := s.repo.AddPostComment(ctx, postcommentsqueries.AddPostCommentParams{
		PostID:   c.Msg.PostId,
		ParentID: c.Msg.ParentId,
		UserID:   sessionUser.ID,
		Content:  c.Msg.Content,
		VtuberID: vtuberId,
	})
	if err != nil {
		return nil, err
	}

	userId, err := s.vtuberProfileRepo.GetVtuberUserId(ctx, post.VtuberProfileID)
	if err == nil {
		s.notificationService.CreateCommentNotification(ctx, post.ID, userId, post.VtuberID, comment.ID, repliedUserId, c.Msg.ParentId, "post")
	}

	var vtuber *sharedv1.Profile
	if c.Msg.AsVtuber != nil && *c.Msg.AsVtuber {
		vtuber = &sharedv1.Profile{
			Id:    *comment.VtuberID,
			Name:  *comment.Vtuberdisplayname,
			Image: comment.Vtuberimage,
		}
	} else {
		vtuber = nil
	}
	return connect.NewResponse(
		&contentv1.AddPostCommentResponse{
			Data: &contentv1.PostComment{
				Id:        comment.ID,
				PostId:    comment.PostID,
				ParentId:  comment.ParentID,
				Content:   comment.Content,
				CreatedAt: timestamppb.New(comment.CreatedAt),
				User: &sharedv1.Profile{
					Id:    comment.UserID,
					Name:  comment.Username,
					Image: comment.Userimage,
				},
				Vtuber: vtuber,
			},
		},
	), nil

}
func (s PostCommentService) GetAllPostComments(ctx context.Context, c *connect.Request[contentv1.GetAllPostCommentsRequest]) (*connect.Response[contentv1.GetAllPostCommentsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "created_at", "updated_at"})
	comments, err := s.repo.GetAllPostComments(ctx, postcommentsqueries.GetAllPostCommentsParams{
		PostID: c.Msg.PostId,
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}

	var postComments []*contentv1.PostComment
	for _, comment := range comments {
		var vtuber *sharedv1.Profile
		if comment.CommentVtuberID != nil {
			vtuber = &sharedv1.Profile{
				Id:    *comment.VtuberID,
				Name:  *comment.VtuberDisplayName,
				Image: comment.VtuberImage,
			}
		} else {
			vtuber = nil
		}

		postComments = append(postComments, &contentv1.PostComment{
			Id:        comment.ID,
			PostId:    comment.PostID,
			ParentId:  comment.ParentID,
			Content:   comment.Content,
			CreatedAt: timestamppb.New(comment.CreatedAt),
			HasReply:  comment.HasReplies,
			User: &sharedv1.Profile{
				Id:    comment.UserIDFromUsers,
				Name:  comment.FullName,
				Image: comment.Image,
			},
			Vtuber: vtuber,
		})
	}
	return connect.NewResponse(
		&contentv1.GetAllPostCommentsResponse{
			Data:              postComments,
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(comments)),
		},
	), nil
}
func (s PostCommentService) GetAllReplesOfComment(ctx context.Context, c *connect.Request[contentv1.GetPostRepliesOfCommentRequest]) (*connect.Response[contentv1.GetAllPostCommentsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "created_at", "updated_at"})

	replies, err := s.repo.GetAllRepliesOfComment(ctx, postcommentsqueries.GetAllRepliesOfCommentParams{
		ParentID: &c.Msg.Id,
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}
	var postComments []*contentv1.PostComment
	for _, comment := range replies {
		var vtuber *sharedv1.Profile
		if comment.CommentVtuberID != nil {
			vtuber = &sharedv1.Profile{
				Id:    *comment.VtuberID,
				Name:  *comment.VtuberDisplayName,
				Image: comment.VtuberImage,
			}
		} else {
			vtuber = nil
		}

		postComments = append(postComments, &contentv1.PostComment{
			Id:        comment.ID,
			PostId:    comment.PostID,
			ParentId:  comment.ParentID,
			Content:   comment.Content,
			CreatedAt: timestamppb.New(comment.CreatedAt),
			HasReply:  comment.HasReplies,
			User: &sharedv1.Profile{
				Id:    comment.UserID,
				Name:  comment.FullName,
				Image: comment.Image,
			},
			Vtuber: vtuber,
		})
	}
	return connect.NewResponse(
		&contentv1.GetAllPostCommentsResponse{
			Data:              postComments,
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(replies)),
		},
	), nil
}

func (s PostCommentService) GetPostCommentById(ctx context.Context, c *connect.Request[contentv1.GetPostCommentByIdRequest]) (*connect.Response[contentv1.GetPostCommentByIdResponse], error) {
	comment, err := s.repo.GetPostCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}
	return connect.NewResponse(&contentv1.GetPostCommentByIdResponse{
		Data: &contentv1.PostComment{
			Id:        comment.ID,
			PostId:    comment.PostID,
			Content:   comment.Content,
			ParentId:  comment.ParentID,
			CreatedAt: timestamppb.New(comment.CreatedAt),
		},
	}), nil

}
func (s PostCommentService) DeletePostCommentById(ctx context.Context, c *connect.Request[contentv1.DeletePostCommentByIdRequest]) (*connect.Response[contentv1.DeletePostCommentByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	comment, err := s.repo.GetPostCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}

	post, err := s.postRepo.GetPost(ctx, strconv.FormatInt(comment.PostID, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}

	if (sessionUser.VtuberId != nil && *sessionUser.VtuberId == post.VtuberProfileID) || (sessionUser.ID == comment.UserID) {
		err = s.repo.DeletePostCommentById(ctx, c.Msg.Id)
		if err != nil {
			return nil, err
		}
	} else {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}
	return connect.NewResponse(
		&contentv1.DeletePostCommentByIdResponse{
			Message: web.GetTranslation(ctx, "postCommentDeleted", nil),
			Success: true,
		},
	), nil

}
func (s PostCommentService) UpdatePostCommentById(ctx context.Context, c *connect.Request[contentv1.UpdatePostCommentByIdRequest]) (*connect.Response[contentv1.UpdatePostCommentByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	comment, err := s.repo.GetPostCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}
	if sessionUser.ID != comment.UserID {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedToPerformAction", nil),
		)
	}
	err = s.repo.UpdatePostCommentById(ctx, postcommentsqueries.UpdatePostCommentByIdParams{
		Content: c.Msg.Content,
		ID:      c.Msg.Id,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(
		&contentv1.UpdatePostCommentByIdResponse{
			Message: web.GetTranslation(ctx, "postCommentUpdated", nil),
			Success: true,
		},
	), nil

}
