package content

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"
	postlikesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/postlikes"
	postsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/posts"

	contentv1 "github.com/nsp-inc/vtuber/api/content/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type PostLikeService struct {
	repo     *postlikesqueries.Queries
	postRepo *postsqueries.Queries
}

func NewPostLikeService(postlikeRepo *postlikesqueries.Queries, postsRepo *postsqueries.Queries) *PostLikeService {
	return &PostLikeService{
		repo:     postlikeRepo,
		postRepo: postsRepo,
	}
}

func (l PostLikeService) AddPostLike(ctx context.Context, c *connect.Request[contentv1.AddPostLikeRequest]) (*connect.Response[contentv1.AddPostLikeResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	_, err := l.postRepo.GetPost(ctx, strconv.FormatInt(c.Msg.PostId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "post", nil),
			}),
		)
	}
	_, err = l.repo.GetPostLikeByUserIdAndPostId(ctx, postlikesqueries.GetPostLikeByUserIdAndPostIdParams{
		PostID: c.Msg.PostId,
		UserID: sessionUser.ID,
	})

	if err == nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "alreadyLikedPost", nil),
		)
	}
	_, err = l.repo.AddPostLike(ctx, postlikesqueries.AddPostLikeParams{
		PostID: c.Msg.PostId,
		UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&contentv1.AddPostLikeResponse{
		Message: web.GetTranslation(ctx, "likeAdded", nil),
		Success: true,
	}), nil

}
func (l PostLikeService) GetPostLikeCount(ctx context.Context, c *connect.Request[contentv1.GetPostLikeCountRequest]) (*connect.Response[contentv1.GetPostLikeCountResponse], error) {
	count, err := l.repo.GetPostLikeCount(ctx, c.Msg.PostId)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&contentv1.GetPostLikeCountResponse{
			Count: count,
		},
	), nil
}
func (l PostLikeService) DeletePostLikeById(ctx context.Context, c *connect.Request[contentv1.DeletePostLikeByIdRequest]) (*connect.Response[contentv1.DeletePostLikeByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	_, err := l.repo.GetPostLikeByUserIdAndPostId(ctx, postlikesqueries.GetPostLikeByUserIdAndPostIdParams{
		PostID: c.Msg.PostId,
		UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "like", nil),
			}),
		)
	}
	err = l.repo.DeletePostLikeByPostId(ctx, postlikesqueries.DeletePostLikeByPostIdParams{
		PostID: c.Msg.PostId,
		UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&contentv1.DeletePostLikeByIdResponse{
			Message: web.GetTranslation(ctx, "likeRemoved", nil),
			Success: true,
		},
	), nil
}

func (l PostLikeService) GetPostLikesOfUser(ctx context.Context, c *connect.Request[contentv1.GetAllPostLikeByUserRequest]) (*connect.Response[contentv1.GetAllPostLikeByUserResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"user_id", "created_at", "updated_at"})
	sessionUser := web.GetUserFromContext(ctx)
	likes, err := l.repo.GetPostLikesOfUser(ctx, postlikesqueries.GetPostLikesOfUserParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
		UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&contentv1.GetAllPostLikeByUserResponse{
			Data: helpers.Map(likes, func(like postlikesqueries.GetPostLikesOfUserRow) *contentv1.PostLike {
				return &contentv1.PostLike{
					UserId:    like.UserID,
					PostId:    like.PostID,
					CreatedAt: timestamppb.New(like.CreatedAt),
				}
			}),
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(likes)),
		},
	), nil
}
