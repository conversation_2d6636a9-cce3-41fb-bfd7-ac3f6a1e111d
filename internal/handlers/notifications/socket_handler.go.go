package notifications

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"net/http"

	"github.com/go-redis/redis/v8"
	"github.com/gorilla/websocket"
	"github.com/nsp-inc/vtuber/packages/interceptors"
)

type WebSocketService struct {
	redisClient *redis.Client
}

func NewWebSocketService(redisClient *redis.Client) *WebSocketService {
	return &WebSocketService{
		redisClient: redisClient,
	}
}

var upgraded = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true
	},
}

func (ws *WebSocketService) WebSocketHandler(w http.ResponseWriter, r *http.Request) {
	auth := r.URL.Query().Get("authorization")
	isVtuber := r.URL.Query().Get("isVtuber")
	currentUser, err := interceptors.GetCurrentUserFromBearer(auth)

	if currentUser == nil {
		log.Printf("Connection cannot be established")
		return
	}
	if err != nil {
		log.Printf("Connection cannot be established")
		return
	}
	if isVtuber == "true" && !currentUser.IsVtuber {
		log.Printf("Connection cannot be established")
		return
	}

	if isVtuber == "true" && currentUser.VtuberId == nil {
		log.Printf("Connection cannot be established")
		return
	}

	conn, err := upgraded.Upgrade(w, r, nil)
	if err != nil {
		slog.Error("Failed to upgrade WebSocket connection", "error", err)
		http.Error(w, "Failed to upgrade WebSocket connection", http.StatusInternalServerError)
		return
	}

	defer func(conn *websocket.Conn) {
		err := conn.Close()
		if err != nil {
			slog.Error("Failed to close connection", "error", err)
			http.Error(w, "Failed to close connection:", http.StatusInternalServerError)
			return
		}
	}(conn)
	slog.Info("WebSocket client connected")

	notificationChannel := fmt.Sprintf("user-notification-%d", currentUser.ID)
	if isVtuber == "true" {
		notificationChannel = fmt.Sprintf("creator-notification-%d", *currentUser.VtuberId)
	}
	subscriber := ws.redisClient.Subscribe(context.Background(), notificationChannel)

	for {

		msg, err := subscriber.ReceiveMessage(context.Background())
		if err != nil {
			panic(err)
		}
		if msg != nil {
			err := conn.WriteMessage(websocket.TextMessage, []byte(msg.Payload))
			if err != nil {
				return
			}
		}

	}

}
