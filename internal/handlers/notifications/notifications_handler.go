package notifications

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strconv"
	"strings"

	"connectrpc.com/connect"
	"github.com/go-redis/redis/v8"
	notificationsv1 "github.com/nsp-inc/vtuber/api/notifications/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	notificationsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/notifications"
	notificationtemplatesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/notificationtemplates"
	usersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type NotificationService struct {
	repo                     *notificationsqueries.Queries
	notificationTemplateRepo *notificationtemplatesqueries.Queries
	vtuberProfileRepo        *vtuberprofilesqueries.Queries
	userRepo                 *usersqueries.Queries
	redisClient              *redis.Client
}

func (n NotificationService) MarkNotificationAsUnRead(ctx context.Context, c *connect.Request[notificationsv1.MarkNotificationAsUnReadRequest]) (*connect.Response[notificationsv1.MarkNotificationAsUnReadResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	notification, err := n.repo.GetNotification(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{"field": web.GetTranslation(ctx, "notification", nil)}))
	}
	if notification.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	if !notification.IsRead {
		return connect.NewResponse(
			&notificationsv1.MarkNotificationAsUnReadResponse{
				Message: web.GetTranslation(ctx, "notificationUnread", nil),
				Success: true,
			},
		), nil
	}

	err = n.repo.UnreadNotification(ctx, notification.ID)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.MarkNotificationAsUnReadResponse{
			Message: web.GetTranslation(ctx, "notificationUnread", nil),
			Success: true,
		},
	), nil
}

func NewNotificationService(repo *notificationsqueries.Queries, notificationTemplateRepo *notificationtemplatesqueries.Queries, vtuberProfileRepo *vtuberprofilesqueries.Queries, userRepo *usersqueries.Queries, redisClient *redis.Client) *NotificationService {
	return &NotificationService{
		repo:                     repo,
		notificationTemplateRepo: notificationTemplateRepo,
		vtuberProfileRepo:        vtuberProfileRepo,
		userRepo:                 userRepo,
		redisClient:              redisClient,
	}

}

func (n NotificationService) GetUserNotifications(ctx context.Context, c *connect.Request[notificationsv1.GetNotificationsRequest]) (*connect.Response[notificationsv1.GetUserNotificationsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{})
	notifications, err := n.repo.ListNotifications(ctx, notificationsqueries.ListNotificationsParams{
		UserID: &sessionUser.ID,
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.GetUserNotificationsResponse{
			Data: helpers.Map(notifications, func(notification notificationsqueries.ListNotificationsRow) *notificationsv1.Notification {
				return &notificationsv1.Notification{
					Id:            notification.ID,
					Title:         notification.Title,
					TitleJp:       notification.TitleJp,
					Description:   notification.Description,
					DescriptionJp: notification.DescriptionJp,
					Severity:      string(notification.Severity),
					DeepLink:      notification.Deeplink,
					Json:          notification.Json,
					CreatedAt:     timestamppb.New(notification.CreatedAt),
					IsRead:        notification.IsRead,
				}
			}),
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(notifications)),
		},
	), nil
}

func (n NotificationService) GetCreatorNotifications(ctx context.Context, c *connect.Request[notificationsv1.GetNotificationsRequest]) (*connect.Response[notificationsv1.GetUserNotificationsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{})
	notifications, err := n.repo.ListNotifications(ctx, notificationsqueries.ListNotificationsParams{
		VtuberID: sessionUser.VtuberId,
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.GetUserNotificationsResponse{
			Data: helpers.Map(notifications, func(notification notificationsqueries.ListNotificationsRow) *notificationsv1.Notification {
				return &notificationsv1.Notification{
					Id:            notification.ID,
					Title:         notification.Title,
					TitleJp:       notification.TitleJp,
					Description:   notification.Description,
					DescriptionJp: notification.DescriptionJp,
					Severity:      string(notification.Severity),
					DeepLink:      notification.Deeplink,
					Json:          notification.Json,
					CreatedAt:     timestamppb.New(notification.CreatedAt),
					IsRead:        notification.IsRead,
				}
			}),
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(notifications)),
		},
	), nil
}

func (n NotificationService) MarkNotificationAsRead(ctx context.Context, c *connect.Request[notificationsv1.MarkNotificationAsReadRequest]) (*connect.Response[notificationsv1.MarkNotificationAsReadResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	notification, err := n.repo.GetNotification(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{"field": web.GetTranslation(ctx, "notification", nil)}))
	}
	if notification.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	if notification.IsRead {
		return connect.NewResponse(
			&notificationsv1.MarkNotificationAsReadResponse{
				Message: web.GetTranslation(ctx, "notificationRead", nil),
				Success: true,
			},
		), nil
	}

	err = n.repo.ReadNotification(ctx, notification.ID)

	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.MarkNotificationAsReadResponse{
			Message: web.GetTranslation(ctx, "notificationRead", nil),
			Success: true,
		},
	), nil

}

func (n NotificationService) CreateCommentNotification(ctx context.Context, resourceId int64, userId int64, creatorId int64, commentId int64, repliedUserId *int64, parentId *int64, resourceType string) {
	var userName = web.GetUserFromContext(ctx).Name
	if repliedUserId != nil {
		if web.GetUserFromContext(ctx).ID != *repliedUserId {
			template, err := n.fixTheCommentTemplate(ctx, helpers.COMMENT_REPLY_KEY, resourceId, *parentId, resourceType, userName)
			if err == nil {
				var creatorId, err = n.vtuberProfileRepo.GetVtuberId(ctx, *repliedUserId)
				if err == nil {
					n.createNotification(ctx, *repliedUserId, &creatorId, template)
				}
				n.createNotification(ctx, *repliedUserId, nil, template)
			}
		}
	}
	if web.GetUserFromContext(ctx).ID == userId {
		return
	}
	template, err := n.fixTheCommentTemplate(ctx, helpers.COMMENT_KEY, resourceId, commentId, resourceType, userName)
	if err != nil {
		return
	}
	n.createNotification(ctx, userId, &creatorId, template)
}

func (n NotificationService) fixTheCommentTemplate(ctx context.Context, key string, resourceId int64, commentId int64, resource string, name string) (map[string]notificationtemplatesqueries.NotificationTemplate, error) {
	templates, err := n.notificationTemplateRepo.GetNotificationTemplatesByKey(ctx, key)
	if err != nil {
		return nil, err
	}
	if len(templates) != 2 {
		return nil, nil
	}

	result := make(map[string]notificationtemplatesqueries.NotificationTemplate)

	for _, t := range templates {
		t.Description = strings.ReplaceAll(t.Description, "#name", name)
		t.Description = strings.ReplaceAll(t.Description, "#resource", resource)
		if t.Deeplink != nil {
			*t.Deeplink = strings.ReplaceAll(*t.Deeplink, "#resource", resource)
			*t.Deeplink = strings.ReplaceAll(*t.Deeplink, "#id", strconv.FormatInt(resourceId, 10))
			*t.Deeplink = strings.ReplaceAll(*t.Deeplink, "#commentId", strconv.FormatInt(commentId, 10))
		}

		// Map by language
		switch t.Language {
		case notificationtemplatesqueries.LanguageEnUs:
			result["en"] = t
		case notificationtemplatesqueries.LanguageJaJp:
			result["jp"] = t
		}
	}

	return result, nil
}

func (n NotificationService) createNotification(ctx context.Context, userId int64, vtuberId *int64, templates map[string]notificationtemplatesqueries.NotificationTemplate) {
	if templates == nil {
		return
	}

	enTemplate, enExists := templates["en"]
	jpTemplate, jpExists := templates["jp"]
	if !enExists && !jpExists {
		return
	}
	notification, err := n.repo.AddNotification(ctx, notificationsqueries.AddNotificationParams{
		Title:         enTemplate.Title,
		TitleJp:       jpTemplate.Title,
		Description:   enTemplate.Description,
		DescriptionJp: jpTemplate.Description,
		Json:          enTemplate.Json,
		Deeplink:      enTemplate.Deeplink,
		Severity:      notificationsqueries.NotificationSeverity(enTemplate.Severity),
		UserID:        userId,
		VtuberID:      vtuberId,
	})
	if err != nil {
		return
	}

	data, err := json.Marshal(notification)
	if err == nil {
		n.redisClient.Publish(ctx, fmt.Sprintf("user-notification-%d", userId), data)
		if vtuberId != nil {
			n.redisClient.Publish(ctx, fmt.Sprintf("creator-notification-%d", *vtuberId), data)
		}
	}
}

func (n NotificationService) DeleteNotificationById(ctx context.Context, c *connect.Request[notificationsv1.DeleteNotificationByIdRequest]) (*connect.Response[notificationsv1.DeleteNotificationByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	notification, err := n.repo.GetNotification(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{"field": web.GetTranslation(ctx, "notification", nil)}))
	}
	if notification.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	err = n.repo.DeleteNotification(ctx, c.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.DeleteNotificationByIdResponse{
			Message: web.GetTranslation(ctx, "notificationDeleted", nil),
			Success: true,
		},
	), nil
}

func (n NotificationService) GetNotificationCount(ctx context.Context, c *connect.Request[notificationsv1.NotificationCountRequest]) (*connect.Response[notificationsv1.NotificationCountResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	count, err := n.repo.GetNotificationCount(ctx, sessionUser.ID)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&notificationsv1.NotificationCountResponse{
			Count: count,
		},
	), nil
}
