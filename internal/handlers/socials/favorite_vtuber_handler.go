package socials

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	socialsv1 "github.com/nsp-inc/vtuber/api/socials/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	favoritevtubersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritevtubers"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type FavoriteVtuberService struct {
	repo *favoritevtubersqueries.Queries
}

func NewFavoriteVtuberService(repo *favoritevtubersqueries.Queries) *FavoriteVtuberService {
	return &FavoriteVtuberService{
		repo: repo,
	}
}

func (s *FavoriteVtuberService) AddFavoriteVtuber(ctx context.Context, c *connect.Request[socialsv1.AddFavoriteVtuberRequest]) (*connect.Response[socialsv1.AddFavoriteVtuberResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	_, err := s.repo.GetFavoriteVtuberByUserIdAndVtuberId(ctx, favoritevtubersqueries.GetFavoriteVtuberByUserIdAndVtuberIdParams{
		UserID:   sessionUser.ID,
		VtuberID: c.Msg.VtuberId,
	})
	if err == nil {
		return nil, errors.New(
			"you already favorited this vtuber",
		)
	}
	favoriteVtuber, err := s.repo.AddFavoriteVtuber(ctx, favoritevtubersqueries.AddFavoriteVtuberParams{
		VtuberID: c.Msg.VtuberId,
		UserID:   sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&socialsv1.AddFavoriteVtuberResponse{
		Data: &socialsv1.FavoriteVtuber{
			Id:        favoriteVtuber.ID,
			VtuberId:  favoriteVtuber.VtuberID,
			UserId:    favoriteVtuber.UserID,
			CreatedAt: timestamppb.New(favoriteVtuber.CreatedAt),
			UpdatedAt: timestamppb.New(favoriteVtuber.UpdatedAt),
			Username:  favoriteVtuber.Username,
		},
	}), nil
}

func (s *FavoriteVtuberService) GetAllFavoriteVtuber(ctx context.Context, c *connect.Request[socialsv1.GetAllFavoriteVtuberRequest]) (*connect.Response[socialsv1.GetAllFavoriteVtuberResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationRequest := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"created_at"})
	favoriteVtuber, err := s.repo.GetFavoriteVtuber(ctx, favoritevtubersqueries.GetFavoriteVtuberParams{
		UserID: sessionUser.ID,
		Limit:  paginationRequest.PageSize,
		Offset: paginationRequest.Offset,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&socialsv1.GetAllFavoriteVtuberResponse{
		Data: helpers.Map(favoriteVtuber, func(favoriteVtuber favoritevtubersqueries.GetFavoriteVtuberRow) *socialsv1.FavoriteVtuberWithDetails {
			return &socialsv1.FavoriteVtuberWithDetails{
				Id:        favoriteVtuber.ID,
				VtuberId:  favoriteVtuber.VtuberID,
				UserId:    favoriteVtuber.UserID,
				CreatedAt: timestamppb.New(favoriteVtuber.CreatedAt),
				UpdatedAt: timestamppb.New(favoriteVtuber.UpdatedAt),
				Username:  favoriteVtuber.Username,
				Vtuber: &vtubersv1.VtuberProfile{
					Id:          favoriteVtuber.VtuberID,
					DisplayName: favoriteVtuber.VtuberName,
					Image:       helpers.GetCdnLinkPointer(favoriteVtuber.VtuberImage),
				},
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationRequest, helpers.GetFirstElement(favoriteVtuber)),
	}), nil
}

func (s *FavoriteVtuberService) DeleteFavoriteVtuber(ctx context.Context, c *connect.Request[socialsv1.DeleteFavoriteVtuberRequest]) (*connect.Response[sharedv1.GenericResponse], error) {

	sessionUser := web.GetUserFromContext(ctx)

	favorite, err := s.repo.GetFavoriteVtuberByUserIdAndVtuberId(ctx, favoritevtubersqueries.GetFavoriteVtuberByUserIdAndVtuberIdParams{
		UserID:   sessionUser.ID,
		VtuberID: c.Msg.VtuberId,
	})

	if err != nil {
		return nil, err
	}

	if favorite.UserID != sessionUser.ID {
		return nil, errors.New("Unauthorized")
	}

	err = s.repo.DeleteFavoriteVtuber(ctx, favoritevtubersqueries.DeleteFavoriteVtuberParams{VtuberID: c.Msg.VtuberId, UserID: web.GetUserFromContext(ctx).ID})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: "Vtuber removed from favorites",
		Success: true,
	}), nil
}
