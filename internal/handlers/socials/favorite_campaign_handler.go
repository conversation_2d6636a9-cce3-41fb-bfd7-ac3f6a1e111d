package socials

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	socialsv1 "github.com/nsp-inc/vtuber/api/socials/v1"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	favoritecampaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritecampaigns"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type FavoriteCampaignService struct {
	repo         *favoritecampaignsqueries.Queries
	campaignrepo *campaignsqueries.Queries
}

func NewFavoriteCampaignService(repo *favoritecampaignsqueries.Queries, campaignrepo *campaignsqueries.Queries) *FavoriteCampaignService {
	return &FavoriteCampaignService{
		repo:         repo,
		campaignrepo: campaignrepo,
	}
}

func (s *FavoriteCampaignService) AddFavoriteCampaign(ctx context.Context, c *connect.Request[socialsv1.AddFavoriteCampaignRequest]) (*connect.Response[socialsv1.AddFavoriteCampaignResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	_, err := s.repo.GetFavoriteCampaignByUserIdAndCampaignId(ctx, favoritecampaignsqueries.GetFavoriteCampaignByUserIdAndCampaignIdParams{
		UserID:     sessionUser.ID,
		CampaignID: c.Msg.CampaignId,
	})
	if err == nil {
		return nil, errors.New(
			"you already favorited this campaign",
		)
	}
	favoriteCampaign, err := s.repo.AddFavoriteCampaign(ctx, favoritecampaignsqueries.AddFavoriteCampaignParams{
		UserID:     sessionUser.ID,
		CampaignID: c.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&socialsv1.AddFavoriteCampaignResponse{
		Data: &socialsv1.FavoriteCampaign{
			Id:         favoriteCampaign.ID,
			UserId:     favoriteCampaign.UserID,
			CreatedAt:  timestamppb.New(favoriteCampaign.CreatedAt),
			UpdatedAt:  timestamppb.New(favoriteCampaign.UpdatedAt),
			CampaignId: favoriteCampaign.CampaignID,
		},
	}), nil
}

func (s *FavoriteCampaignService) GetAllFavoriteCampaign(ctx context.Context, c *connect.Request[socialsv1.GetAllFavoriteCampaignRequest]) (*connect.Response[socialsv1.GetAllFavoriteCampaignResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationRequest := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"created_at"})
	favoriteCampaign, err := s.repo.GetFavoriteCampaign(ctx, favoritecampaignsqueries.GetFavoriteCampaignParams{
		UserID: sessionUser.ID,
		Limit:  paginationRequest.PageSize,
		Offset: paginationRequest.Offset,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&socialsv1.GetAllFavoriteCampaignResponse{
		Data: helpers.Map(favoriteCampaign, func(favoriteCampaign favoritecampaignsqueries.GetFavoriteCampaignRow) *socialsv1.FavoriteCampaignWithDetails {
			return &socialsv1.FavoriteCampaignWithDetails{
				Id:               favoriteCampaign.ID,
				CampaignId:       favoriteCampaign.CampaignID,
				UserId:           favoriteCampaign.UserID,
				CreatedAt:        timestamppb.New(favoriteCampaign.CreatedAt),
				Name:             favoriteCampaign.Name,
				ShortDescription: favoriteCampaign.ShortDescription,
				Image:            *helpers.GetCdnLinkPointer(&favoriteCampaign.Thumbnail),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationRequest, helpers.GetFirstElement(favoriteCampaign)),
	}), nil
}

func (s *FavoriteCampaignService) DeleteFavoriteCampaign(ctx context.Context, c *connect.Request[socialsv1.DeleteFavoriteCampaignRequest]) (*connect.Response[sharedv1.GenericResponse], error) {

	sessionUser := web.GetUserFromContext(ctx)

	favorite, err := s.repo.GetFavoriteCampaignByUserIdAndCampaignId(ctx, favoritecampaignsqueries.GetFavoriteCampaignByUserIdAndCampaignIdParams{
		UserID:     sessionUser.ID,
		CampaignID: c.Msg.CampaignId,
	})

	if err != nil {
		return nil, err
	}

	if favorite.UserID != sessionUser.ID {
		return nil, errors.New("Unauthorized")
	}

	err = s.repo.DeleteFavoriteCampaign(ctx, favoritecampaignsqueries.DeleteFavoriteCampaignParams{
		CampaignID: c.Msg.CampaignId, UserID: sessionUser.ID,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: "Campaign removed from favorites",
		Success: true,
	}), nil
}
