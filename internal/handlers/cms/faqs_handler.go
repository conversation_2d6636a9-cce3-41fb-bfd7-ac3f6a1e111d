package cms

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	cmsv1 "github.com/nsp-inc/vtuber/api/cms/v1"
	faqsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/faqs"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type FAQService struct {
	repo *faqsqueries.Queries
}

func (F FAQService) AddFaq(ctx context.Context, c *connect.Request[cmsv1.AddFaqRequest]) (*connect.Response[cmsv1.AddFaqResponse], error) {
	isActive := true
	if c.Msg.IsActive != nil {
		isActive = *c.Msg.IsActive
	}

	if !helpers.CheckValidTag(c.Msg.Tag) {
		return nil, errors.New(web.GetTranslation(ctx, "invalidTag", nil))
	}

	newFaq, err := F.repo.CreateFaq(ctx, faqsqueries.CreateFaqParams{
		Question: c.Msg.Question,
		Response: c.Msg.Response,
		Index:    c.Msg.Index,
		Active:   isActive,
		Language: c.Msg.Language,
		Tag:      c.Msg.Tag,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.AddFaqResponse{
		Data: &cmsv1.Faq{
			Id:        newFaq.ID,
			Question:  newFaq.Question,
			Response:  newFaq.Response,
			Index:     newFaq.Index,
			IsActive:  newFaq.Active,
			CreatedAt: timestamppb.New(newFaq.CreatedAt),
			Language:  newFaq.Language,
			Tag:       newFaq.Tag,
		},
	}), nil
}

func (F FAQService) GetAllFaqs(ctx context.Context, c *connect.Request[cmsv1.GetAllFaqsRequest]) (*connect.Response[cmsv1.GetAllFaqsResponse], error) {
	faqs, err := F.repo.GetAllFaqs(ctx, faqsqueries.GetAllFaqsParams{
		Language: c.Msg.Language,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.GetAllFaqsResponse{
		Data: helpers.Map(faqs, func(faq faqsqueries.Faq) *cmsv1.Faq {
			return &cmsv1.Faq{
				Id:        faq.ID,
				Question:  faq.Question,
				Response:  faq.Response,
				Index:     faq.Index,
				IsActive:  faq.Active,
				CreatedAt: timestamppb.New(faq.CreatedAt),
				Language:  faq.Language,
				Tag:       faq.Tag,
			}
		}),
	}), nil
}

func (F FAQService) GetFaq(ctx context.Context, c *connect.Request[cmsv1.GetFaqRequest]) (*connect.Response[cmsv1.GetFaqResponse], error) {
	faq, err := F.repo.GetFaqById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "FAQ", nil),
		}))
	}
	return connect.NewResponse(&cmsv1.GetFaqResponse{
		Data: &cmsv1.Faq{
			Id:        faq.ID,
			Question:  faq.Question,
			Response:  faq.Response,
			Index:     faq.Index,
			IsActive:  faq.Active,
			CreatedAt: timestamppb.New(faq.CreatedAt),
			Language:  faq.Language,
			Tag:       faq.Tag,
		},
	},
	), nil
}

func (F FAQService) UpdateFaq(ctx context.Context, c *connect.Request[cmsv1.UpdateFaqRequest]) (*connect.Response[cmsv1.UpdateFaqResponse], error) {
	_, err := F.repo.GetFaqById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "FAQ", nil),
		}))
	}
	if !helpers.CheckValidTag(c.Msg.Tag) {
		return nil, errors.New(web.GetTranslation(ctx, "invalidTag", nil))
	}
	err = F.repo.UpdateFaqById(ctx, faqsqueries.UpdateFaqByIdParams{
		Question: c.Msg.Question,
		Response: c.Msg.Response,
		Index:    c.Msg.Index,
		ID:       c.Msg.Id,
		Language: c.Msg.Language,
		Tag:      c.Msg.Tag,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.UpdateFaqResponse{
		Message: web.GetTranslation(ctx, "FAQUpdated", nil),
		Success: true,
	},
	), nil
}

func (F FAQService) DeleteFaq(ctx context.Context, c *connect.Request[cmsv1.DeleteFaqRequest]) (*connect.Response[cmsv1.DeleteFaqResponse], error) {
	faq, err := F.repo.GetFaqById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "FAQ", nil),
		}))
	}

	err = F.repo.DeleteFaqById(ctx, faq.ID)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.DeleteFaqResponse{
		Message: web.GetTranslation(ctx, "FAQDeleted", nil),
		Success: true,
	},
	), nil

}

func (F FAQService) ToogleFaqStatus(ctx context.Context, c *connect.Request[cmsv1.ToogleFaqStatusRequest]) (*connect.Response[cmsv1.ToogleFaqStatusResponse], error) {
	faq, err := F.repo.GetFaqById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "FAQ", nil),
		}))
	}

	err = F.repo.ToggleFaqActive(ctx, faqsqueries.ToggleFaqActiveParams{
		ID:     faq.ID,
		Active: !faq.Active,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.ToogleFaqStatusResponse{
		Message: web.GetTranslation(ctx, "FAQStatusUpdated", nil),
		Success: true,
	},
	), nil
}

func (F FAQService) GetAllActiveFaqs(ctx context.Context, c *connect.Request[cmsv1.GetAllActiveFaqsRequest]) (*connect.Response[cmsv1.GetAllFaqsResponse], error) {
	isActive := true
	faqs, err := F.repo.GetAllFaqs(ctx, faqsqueries.GetAllFaqsParams{

		IsActive: &isActive,
		Language: c.Msg.Language,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.GetAllFaqsResponse{
		Data: helpers.Map(faqs, func(faq faqsqueries.Faq) *cmsv1.Faq {
			return &cmsv1.Faq{
				Id:        faq.ID,
				Question:  faq.Question,
				Response:  faq.Response,
				Index:     faq.Index,
				IsActive:  faq.Active,
				CreatedAt: timestamppb.New(faq.CreatedAt),
				Language:  faq.Language,
				Tag:       faq.Tag,
			}
		}),
	}), nil
}

func NewFAQService(repo *faqsqueries.Queries) *FAQService {
	return &FAQService{
		repo: repo,
	}
}
