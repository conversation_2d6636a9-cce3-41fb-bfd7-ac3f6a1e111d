package cms

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	cmsv1 "github.com/nsp-inc/vtuber/api/cms/v1"
	announcementsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/announcements"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type AnnouncementService struct {
	repo           *announcementsqueries.Queries
	storageService *storagev1.StorageService
}

func (a AnnouncementService) AddAnnouncement(ctx context.Context, req *connect.Request[cmsv1.AddAnnouncementRequest]) (*connect.Response[cmsv1.AnnouncementResponse], error) {
	_, err := a.repo.GetAnnouncement(ctx)
	if err == nil {
		return nil, connect.NewError(connect.CodeAlreadyExists, errors.New("announcement already exists"))
	}

	newFileName, err := a.storageService.ValidateAndUploadFromTemp(ctx, &req.Msg.Image, storagev1.ImageFileType, "announcement", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	announcement, err := a.repo.AddAnnouncement(ctx, announcementsqueries.AddAnnouncementParams{
		Image:       newFileName,
		Description: req.Msg.Description,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.AnnouncementResponse{
		Data: &cmsv1.Announcement{
			Id:        announcement.ID,
			Image:     helpers.GetCdnUrl(announcement.Image),
			Content:   announcement.Description,
			Active:    announcement.Active,
			CreatedAt: timestamppb.New(announcement.CreatedAt),
		},
	}), nil

}

func (a AnnouncementService) UpdateAnnouncement(ctx context.Context, req *connect.Request[cmsv1.UpdateAnnouncementRequest]) (*connect.Response[cmsv1.AnnouncementResponse], error) {
	id, err := a.repo.GetAnnouncementById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}

	newUpdatedImage, err := a.storageService.ValidateAndUploadFromTemp(ctx, &req.Msg.Image, storagev1.ImageFileType, "campaigns", false, &id.Image)
	if err != nil {
		return nil, validation.NewFieldError("thumbnail", err)
	}

	announcement, err := a.repo.UpdateAnnouncement(ctx, announcementsqueries.UpdateAnnouncementParams{
		ID:          id.ID,
		Image:       newUpdatedImage,
		Description: req.Msg.Content,
	})

	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&cmsv1.AnnouncementResponse{
		Data: &cmsv1.Announcement{
			Id:        announcement.ID,
			Image:     helpers.GetCdnUrl(announcement.Image),
			Content:   announcement.Description,
			Active:    announcement.Active,
			CreatedAt: timestamppb.New(announcement.CreatedAt),
		},
	}), nil
}

func (a AnnouncementService) ToggleAnnouncement(ctx context.Context, req *connect.Request[cmsv1.ToggleAnnouncementRequest]) (*connect.Response[cmsv1.AnnouncementResponse], error) {
	id, err := a.repo.GetAnnouncementById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}

	announcement, err := a.repo.ToggleAnnouncement(ctx, id.ID)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&cmsv1.AnnouncementResponse{
		Data: &cmsv1.Announcement{
			Id:        announcement.ID,
			Image:     helpers.GetCdnUrl(announcement.Image),
			Content:   announcement.Description,
			Active:    announcement.Active,
			CreatedAt: timestamppb.New(announcement.CreatedAt),
		},
	}), nil

}

func (a AnnouncementService) GetAnnouncement(ctx context.Context, c *connect.Request[cmsv1.GetAnnouncementRequest]) (*connect.Response[cmsv1.AnnouncementResponse], error) {
	announcement, err := a.repo.GetAnnouncement(ctx)
	if err != nil {
		return connect.NewResponse(&cmsv1.AnnouncementResponse{
			Data: nil,
		}), nil
	}

	return connect.NewResponse(&cmsv1.AnnouncementResponse{
		Data: &cmsv1.Announcement{
			Id:        announcement.ID,
			Image:     helpers.GetCdnUrl(announcement.Image),
			Content:   announcement.Description,
			Active:    announcement.Active,
			CreatedAt: timestamppb.New(announcement.CreatedAt),
		},
	}), nil
}

func NewAnnouncementService(repo *announcementsqueries.Queries, storageService *storagev1.StorageService) *AnnouncementService {
	return &AnnouncementService{
		repo:           repo,
		storageService: storageService,
	}
}
