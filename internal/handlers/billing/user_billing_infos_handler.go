package billing

import (
	"connectrpc.com/connect"
	"context"
	"errors"
	billingv1 "github.com/nsp-inc/vtuber/api/billing/v1"

	userbillinginfosqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userbillinginfos"
	"github.com/nsp-inc/vtuber/packages/gmo"
	"github.com/nsp-inc/vtuber/packages/hasher"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
)

type UserBillingInfoHandlerService struct {
	repo *userbillinginfosqueries.Queries
}

func NewUserBillingInfoHandlerService(billingRepo *userbillinginfosqueries.Queries) *UserBillingInfoHandlerService {
	return &UserBillingInfoHandlerService{
		repo: billingRepo,
	}
}

func (b UserBillingInfoHandlerService) AddBillingInfo(ctx context.Context, req *connect.Request[billingv1.AddBillingInfoRequest]) (*connect.Response[billingv1.AddBillingInfoResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	billingInfo, err := b.repo.CountBillingInfo(ctx, sessionUser.ID)
	if err != nil {
		return nil, err
	}
	if billingInfo >= 4 {
		return nil, errors.New("you cannot add more that 4 billing information")
	}
	memberId, err := gmo.AddCard(ctx, gmo.AddCardRequest{
		CardNumber:       req.Msg.CardNo,
		CardExpiryDate:   req.Msg.CardExpiry,
		CardSecurityCode: req.Msg.CardCvc,
	})

	if err != nil {
		return nil, err
	}

	infoByUserId, err := b.repo.GetBillingInfoByUserId(ctx, sessionUser.ID)
	if err != nil {
		return nil, err
	}

	for _, info := range infoByUserId {
		if hasher.CompareHash(info.CardNo, req.Msg.CardNo) {
			return nil, errors.New("this card is already used")
		}
	}
	hashedCardNo, err := hasher.Hash(req.Msg.CardNo)
	if err != nil {
		return nil, err
	}

	info, err := b.repo.AddBillingInfo(ctx, userbillinginfosqueries.AddBillingInfoParams{
		UserID:      sessionUser.ID,
		GmoMemberID: *memberId,
		FullName:    req.Msg.FullName,
		Address1:    req.Msg.Address_1,
		Address2:    req.Msg.Address_2,
		City:        req.Msg.City,
		State:       &req.Msg.State,
		Country:     req.Msg.Country,
		PostalCode:  req.Msg.PostalCode,
		CompanyName: req.Msg.CompanyName,
		VatNumber:   req.Msg.VatNumber,
		CardNo:      hashedCardNo,
	})
	if err != nil {
		return nil, err
	}

	card, err := gmo.SearchCard(ctx, *memberId)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&billingv1.AddBillingInfoResponse{
		Data: &billingv1.BillingInfo{
			Id:          info.ID,
			FullName:    info.FullName,
			Address_1:   info.Address1,
			Address_2:   info.Address2,
			City:        info.City,
			State:       info.State,
			Country:     info.Country,
			PostalCode:  info.PostalCode,
			CompanyName: info.CompanyName,
			VatNumber:   info.VatNumber,
			CardNo:      card.CardNumber,
			CardExpiry:  card.CardExpiryDate,
		},
	}), nil
}

func (b UserBillingInfoHandlerService) GetBillingInfo(ctx context.Context, req *connect.Request[billingv1.GetBillingInfoRequest]) (*connect.Response[billingv1.GetBillingInfoResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	userBillingInfos, err := b.repo.GetBillingInfoByUserId(ctx, sessionUser.ID)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&billingv1.GetBillingInfoResponse{
		Data: helpers.MapWithOutError(userBillingInfos, func(info userbillinginfosqueries.UserBillingInfo) (*billingv1.BillingInfo, error) {
			card, err := gmo.SearchCard(ctx, info.GmoMemberID)
			if err != nil {
				return nil, err
			}
			return &billingv1.BillingInfo{
				Id:          info.ID,
				FullName:    info.FullName,
				Address_1:   info.Address1,
				Address_2:   info.Address2,
				City:        info.City,
				State:       info.State,
				Country:     info.Country,
				PostalCode:  info.PostalCode,
				CompanyName: info.CompanyName,
				VatNumber:   info.VatNumber,
				CardNo:      card.CardNumber,
				CardExpiry:  card.CardExpiryDate,
			}, nil
		}),
	}), nil

}

func (b UserBillingInfoHandlerService) UpdateBillingInfo(ctx context.Context, req *connect.Request[billingv1.UpdateBillingInfoRequest]) (*connect.Response[billingv1.UpdateBillingInfoResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	billingInfo, err := b.repo.GetBillingInfoById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	if billingInfo.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	_, err = b.repo.UpdateBillingInfo(ctx, userbillinginfosqueries.UpdateBillingInfoParams{
		ID:          req.Msg.Id,
		FullName:    req.Msg.FullName,
		Address1:    req.Msg.Address_1,
		Address2:    req.Msg.Address_2,
		City:        req.Msg.City,
		State:       &req.Msg.State,
		VatNumber:   req.Msg.VatNumber,
		Country:     req.Msg.Country,
		PostalCode:  req.Msg.PostalCode,
		CompanyName: req.Msg.CompanyName,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&billingv1.UpdateBillingInfoResponse{
		Message: "Billing info updated successfully",
		Success: true,
	}), nil
}

func (b UserBillingInfoHandlerService) DeleteBillingInfo(ctx context.Context, req *connect.Request[billingv1.DeleteBillingInfoRequest]) (*connect.Response[billingv1.DeleteBillingInfoResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	billingInfo, err := b.repo.GetBillingInfoById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	if billingInfo.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}

	err = b.repo.DeleteBillingInfo(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}

	err = gmo.DeleteCard(ctx, billingInfo.GmoMemberID)
	if err != nil {
		return nil, err
	}

	_, err = gmo.GmoAPICall(gmo.GmoApiParams{
		MemberID: billingInfo.GmoMemberID,
	}, gmo.DeleteMemberEndPoint)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&billingv1.DeleteBillingInfoResponse{
		Message: "Billing info deleted successfully",
		Success: true,
	}), nil

}
