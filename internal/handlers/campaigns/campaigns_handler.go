package campaigns

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"

	campaignsv1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	campaignbannersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignbanners"
	campaigncategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigncategories"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	campaignvariantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariants"
	campaignvariantsubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariantsubscriptions"
	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	favoritecampaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/favoritecampaigns"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type CampaignService struct {
	repo                            *campaignsqueries.Queries
	categoryRepo                    *categoriesqueries.Queries
	storageService                  *storagev1.StorageService
	campaignBannerRepo              *campaignbannersqueries.Queries
	campaignVariantRepo             *campaignvariantsqueries.Queries
	favoritecampaignsqueries        *favoritecampaignsqueries.Queries
	campaignVariantSubscriptionRepo *campaignvariantsubscriptionsqueries.Queries
	campaignCategoriesRepo          *campaigncategoriesqueries.Queries
}

func NewCampaignService(
	repo *campaignsqueries.Queries,
	categoryRepo *categoriesqueries.Queries,
	storageService *storagev1.StorageService,
	campaignBannerRepo *campaignbannersqueries.Queries,
	campaignVariantRepo *campaignvariantsqueries.Queries,
	favoritecampaignsqueries *favoritecampaignsqueries.Queries,
	campaignVariantSubscriptionRepo *campaignvariantsubscriptionsqueries.Queries,
	campaignCategoriesRepo *campaigncategoriesqueries.Queries,
) *CampaignService {
	return &CampaignService{
		repo:                            repo,
		categoryRepo:                    categoryRepo,
		storageService:                  storageService,
		campaignBannerRepo:              campaignBannerRepo,
		campaignVariantRepo:             campaignVariantRepo,
		favoritecampaignsqueries:        favoritecampaignsqueries,
		campaignVariantSubscriptionRepo: campaignVariantSubscriptionRepo,
		campaignCategoriesRepo:          campaignCategoriesRepo,
	}
}

func (c CampaignService) GetSubscribers(ctx context.Context, req *connect.Request[campaignsv1.SubscriberRequest]) (*connect.Response[campaignsv1.SubscriberResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	subs, err := c.repo.GetCampaignSubscribers(ctx, campaignsqueries.GetCampaignSubscribersParams{
		Limit:      paginationInfo.PageSize,
		Offset:     paginationInfo.Offset,
		Order:      paginationInfo.OrderDirection,
		Sort:       paginationInfo.OrderBy,
		CampaignID: req.Msg.CampaignId,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.SubscriberResponse{
		Data: helpers.Map(subs, func(sub campaignsqueries.GetCampaignSubscribersRow) *campaignsv1.Subscription {
			return &campaignsv1.Subscription{
				Id:                   sub.UserID,
				Name:                 sub.FullName,
				Image:                *helpers.GetCdnLinkPointer(sub.Image),
				Comment:              sub.Comment,
				CreatedAt:            timestamppb.New(sub.CreatedAt),
				CampaignId:           sub.CampaignID,
				Amount:               sub.Price,
				CampaignVariantId:    sub.CampaignVariantID,
				CampaignVariantTitle: sub.CampaignVariantTitle,
				CampaignVariantImage: sub.CampaignVariantImage,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(subs)),
	},
	), nil

}

func (c CampaignService) AddCampaign(ctx context.Context, req *connect.Request[campaignsv1.AddCampaignRequest]) (*connect.Response[campaignsv1.AddCampaignResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	if len(req.Msg.Categories) > 0 {
		categories, err := c.categoryRepo.GetCategoriesByIds(ctx, req.Msg.Categories)
		if err != nil {

			return nil, err
		}
		if len(categories) != len(req.Msg.Categories) {
			return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}))
		}
	}

	newFileName, err := c.storageService.ValidateAndUploadFromTemp(ctx, &req.Msg.Thumbnail, storagev1.ImageFileType, "campaigns", true, nil)

	if err != nil {
		return nil, validation.NewFieldError("thumbnail", err)
	}

	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	campRepo := c.repo.WithTx(tx)
	catRepo := c.campaignCategoriesRepo.WithTx(tx)

	slug := helpers.Slugify(req.Msg.Name)

	_, err = c.repo.GetCampaignBySlug(ctx, slug)
	if err == nil {
		slug = helpers.SlugifyWithTimestamp(slug)
	}
	campaign, err := campRepo.CreateCampaign(ctx, campaignsqueries.CreateCampaignParams{
		VtuberID:           *sessionUser.VtuberId,
		Description:        helpers.SanitizeHtml(req.Msg.Description),
		ShortDescription:   req.Msg.ShortDescription,
		TotalBudget:        req.Msg.TotalBudget,
		Name:               req.Msg.Name,
		Thumbnail:          newFileName,
		StartDate:          req.Msg.StartDate.AsTime(),
		EndDate:            req.Msg.EndDate.AsTime(),
		PromotionalMessage: req.Msg.PromotionalMessage,
		SocialMediaLinks:   req.Msg.SocialMediaLinks.ToBytes(),
		Slug:               slug,
	})
	if err != nil {
		return nil, err
	}

	_, err = catRepo.AddCampaignCategories(ctx, helpers.Map(req.Msg.Categories, func(categoryId int64) campaigncategoriesqueries.AddCampaignCategoriesParams {
		return campaigncategoriesqueries.AddCampaignCategoriesParams{
			CampaignID: campaign.ID,
			CategoryID: categoryId,
		}
	}))
	if err != nil {
		return nil, err
	}

	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.AddCampaignResponse{
		Data: &campaignsv1.Campaign{
			Id:                 campaign.ID,
			VtuberId:           campaign.VtuberID,
			ShortDescription:   campaign.ShortDescription,
			Name:               campaign.Name,
			StartDate:          timestamppb.New(campaign.StartDate),
			EndDate:            timestamppb.New(campaign.EndDate),
			TotalBudget:        campaign.TotalBudget,
			Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
			CreatedAt:          timestamppb.New(campaign.CreatedAt),
			PromotionalMessage: campaign.PromotionalMessage,
			SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
			Slug:               campaign.Slug,
			Categories:         req.Msg.Categories,
		},
	}), nil
}

func (c CampaignService) GetAllCampaigns(ctx context.Context, req *connect.Request[campaignsv1.GetAllCampaignsRequest]) (*connect.Response[campaignsv1.GetAllCampaignsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"name", "budget", "created_at", "updated_at"})
	campaigns, err := c.repo.GetAllCampaigns(ctx, campaignsqueries.GetAllCampaignsParams{
		Limit:      paginationInfo.PageSize,
		Offset:     paginationInfo.Offset,
		Order:      paginationInfo.OrderDirection,
		Sort:       paginationInfo.OrderBy,
		CategoryID: req.Msg.CategoryId,
	})

	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetAllCampaignsResponse{
		Data: helpers.Map(campaigns, func(campaign campaignsqueries.GetAllCampaignsRow) *campaignsv1.Campaign {
			return &campaignsv1.Campaign{
				Id:                 campaign.ID,
				VtuberId:           campaign.VtuberID,
				ShortDescription:   campaign.ShortDescription,
				Name:               campaign.Name,
				StartDate:          timestamppb.New(campaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
				TotalBudget:        campaign.TotalBudget,
				EndDate:            timestamppb.New(campaign.EndDate),
				CreatedAt:          timestamppb.New(campaign.CreatedAt),
				PromotionalMessage: campaign.PromotionalMessage,
				TotalRaised:        campaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
				Slug:               campaign.Slug,
				CreatedBy:          &campaign.DisplayName,
				Categories:         campaign.CategoryIds,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(campaigns)),
	},
	), nil
}
func (c CampaignService) GetCampaignById(ctx context.Context, req *connect.Request[campaignsv1.GetCampaignByIdRequest]) (*connect.Response[campaignsv1.GetCampaignByIdResponse], error) {
	campaign, err := c.repo.GetCampaignById(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	sessionUser := web.GetUserFromContext(ctx)
	var userId *int64
	hasLiked := false
	if sessionUser != nil {
		userId = &sessionUser.ID
		count, _ := c.repo.UserLikedCampaign(ctx, campaignsqueries.UserLikedCampaignParams{
			UserID:     sessionUser.ID,
			CampaignID: campaign.ID,
		})
		hasLiked = count > 0
	}

	banners, err := c.campaignBannerRepo.GetAllCampaignBanners(ctx, campaign.ID)
	if err != nil {
		return nil, err
	}

	variants, err := c.campaignVariantRepo.GetAllCampaignVariantsByCampaignId(ctx, campaignvariantsqueries.GetAllCampaignVariantsByCampaignIdParams{
		UserID:     userId,
		CampaignID: campaign.ID,
	})
	if err != nil {
		return nil, err
	}

	likeCount, err := c.favoritecampaignsqueries.CountFavoriteCampaign(ctx, campaign.ID)

	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetCampaignByIdResponse{
		Data: &campaignsv1.GetCampaignById{
			Id: campaign.ID,
			Vtuber: &vtubersv1.VtuberProfile{
				Id:          campaign.VtuberID,
				DisplayName: campaign.VtuberName,
				UserId:      campaign.VtuberUserID,
				Furigana:    campaign.VtuberFurigana,
				Image:       helpers.GetCdnLinkPointer(campaign.VtuberImage),
				BannerImage: helpers.GetCdnLinkPointer(campaign.VtuberBannerImage),
				Description: campaign.VtuberDescription,
				CreatedAt:   timestamppb.New(campaign.VtuberCreatedAt),
			},
			Description:        campaign.Description,
			Name:               campaign.Name,
			StartDate:          timestamppb.New(campaign.StartDate),
			EndDate:            timestamppb.New(campaign.EndDate),
			Categories:         campaign.CategoryIds,
			Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
			TotalBudget:        campaign.TotalBudget,
			CreatedAt:          timestamppb.New(campaign.CreatedAt),
			HasLiked:           hasLiked,
			ShortDescription:   campaign.ShortDescription,
			LikeCount:          likeCount,
			PromotionalMessage: campaign.PromotionalMessage,
			TotalRaised:        campaign.TotalRaised,
			Slug:               campaign.Slug,
			SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
			Banners: helpers.Map(banners, func(banner campaignbannersqueries.CampaignBanner) *campaignsv1.CampaignBanner {
				return &campaignsv1.CampaignBanner{
					Id:         banner.ID,
					Image:      helpers.GetCdnUrl(banner.Image),
					Index:      banner.Index,
					CampaignId: banner.CampaignID,
					CreatedAt:  timestamppb.New(banner.CreatedAt),
				}
			}),
			Variants: helpers.Map(variants, func(variant campaignvariantsqueries.GetAllCampaignVariantsByCampaignIdRow) *campaignsv1.CampaignVariantById {
				return &campaignsv1.CampaignVariantById{
					Id:            variant.ID,
					CampaignId:    variant.CampaignID,
					Description:   variant.Description,
					Price:         variant.Price,
					MaxSub:        variant.MaxSub,
					Image:         helpers.GetCdnUrl(variant.Image),
					Title:         variant.Title,
					CreatedAt:     timestamppb.New(variant.CreatedAt),
					SubCount:      variant.SubCount,
					HasSubscribed: variant.HasSubscribed,
					DisplayOrder:  variant.DisplayOrder,
				}
			}),
		},
	}), nil
}

func (c CampaignService) DeleteCampaignById(ctx context.Context, req *connect.Request[campaignsv1.DeleteCampaignByIdRequest]) (*connect.Response[campaignsv1.DeleteCampaignByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	camp, err := c.repo.GetCampaignById(ctx, strconv.FormatInt(req.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}
	if camp.VtuberID != *sessionUser.VtuberId {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedCampaignDelete", nil),
		)
	}
	err = c.repo.DeleteCampaign(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.DeleteCampaignByIdResponse{
		Message: web.GetTranslation(ctx, "campaignDeleted", nil),
		Success: true,
	}), nil
}

func (c CampaignService) UpdateCampaignById(ctx context.Context, req *connect.Request[campaignsv1.UpdateCampaignByIdRequest]) (*connect.Response[campaignsv1.UpdateCampaignByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	camp, err := c.repo.GetCampaignById(ctx, strconv.FormatInt(req.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "campaign", nil),
		}))
	}

	if camp.VtuberID != *sessionUser.VtuberId {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedCampaignUpdate", nil))
	}
	if len(req.Msg.Categories) > 0 {
		categories, err := c.categoryRepo.GetCategoriesByIds(ctx, req.Msg.Categories)
		if err != nil {

			return nil, err
		}
		if len(categories) != len(req.Msg.Categories) {
			return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}))
		}
	}

	newUpdatedImage, err := c.storageService.ValidateAndUploadFromTemp(ctx, &req.Msg.Thumbnail, storagev1.ImageFileType, "campaigns", false, &camp.Thumbnail)

	if err != nil {
		return nil, validation.NewFieldError("thumbnail", err)
	}

	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	campRepo := c.repo.WithTx(tx)
	catRepo := c.campaignCategoriesRepo.WithTx(tx)

	err = campRepo.UpdateCampaign(ctx, campaignsqueries.UpdateCampaignParams{
		ID:                 req.Msg.Id,
		Description:        req.Msg.Description,
		TotalBudget:        req.Msg.TotalBudget,
		Name:               req.Msg.Name,
		ShortDescription:   req.Msg.ShortDescription,
		Thumbnail:          newUpdatedImage,
		StartDate:          req.Msg.StartDate.AsTime(),
		EndDate:            req.Msg.EndDate.AsTime(),
		PromotionalMessage: req.Msg.PromotionalMessage,
		SocialMediaLinks:   req.Msg.SocialMediaLinks.ToBytes(),
	})
	if err != nil {
		return nil, err
	}

	err = catRepo.DeleteCampaignCategories(ctx, camp.ID)
	if err != nil {
		return nil, err
	}

	_, err = catRepo.AddCampaignCategories(ctx, helpers.Map(req.Msg.Categories, func(categoryId int64) campaigncategoriesqueries.AddCampaignCategoriesParams {
		return campaigncategoriesqueries.AddCampaignCategoriesParams{
			CampaignID: camp.ID,
			CategoryID: categoryId,
		}
	}))
	if err != nil {
		return nil, err
	}

	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.UpdateCampaignByIdResponse{
		Message: web.GetTranslation(ctx, "campaignUpdated", nil),
		Success: true,
	}), nil
}

func (c *CampaignService) GetAllCampaignsByVtuberId(ctx context.Context, req *connect.Request[campaignsv1.GetAllCampaignsByVtuberRequest]) (*connect.Response[campaignsv1.GetAllCampaignsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"name", "budget", "created_at", "updated_at"})
	campaigns, err := c.repo.GetCampaignsByVtuber(ctx, campaignsqueries.GetCampaignsByVtuberParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: req.Msg.VtuberId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetAllCampaignsResponse{
		Data: helpers.Map(campaigns, func(campaign campaignsqueries.GetCampaignsByVtuberRow) *campaignsv1.Campaign {
			return &campaignsv1.Campaign{
				Id:                 campaign.ID,
				VtuberId:           campaign.VtuberID,
				ShortDescription:   campaign.ShortDescription,
				Name:               campaign.Name,
				StartDate:          timestamppb.New(campaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
				TotalBudget:        campaign.TotalBudget,
				EndDate:            timestamppb.New(campaign.EndDate),
				CreatedAt:          timestamppb.New(campaign.CreatedAt),
				PromotionalMessage: campaign.PromotionalMessage,
				TotalRaised:        campaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
				Slug:               campaign.Slug,
				Categories:         campaign.CategoryIds,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(campaigns)),
	},
	), nil

}

func (c *CampaignService) GetMyCampaigns(ctx context.Context, req *connect.Request[campaignsv1.GetMyCampaignsRequest]) (*connect.Response[campaignsv1.GetAllCampaignsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"name", "budget", "created_at", "updated_at"})
	campaigns, err := c.repo.GetCampaignsByVtuber(ctx, campaignsqueries.GetCampaignsByVtuberParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: *sessionUser.VtuberId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&campaignsv1.GetAllCampaignsResponse{
		Data: helpers.Map(campaigns, func(campaign campaignsqueries.GetCampaignsByVtuberRow) *campaignsv1.Campaign {
			return &campaignsv1.Campaign{
				Id:                 campaign.ID,
				VtuberId:           campaign.VtuberID,
				ShortDescription:   campaign.ShortDescription,
				Name:               campaign.Name,
				StartDate:          timestamppb.New(campaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
				TotalBudget:        campaign.TotalBudget,
				EndDate:            timestamppb.New(campaign.EndDate),
				CreatedAt:          timestamppb.New(campaign.CreatedAt),
				PromotionalMessage: campaign.PromotionalMessage,
				TotalRaised:        campaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
				Slug:               campaign.Slug,
				Categories:         campaign.CategoryIds,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(campaigns)),
	},
	), nil
}

func (c *CampaignService) GetCampaignSubscriberComments(ctx context.Context, req *connect.Request[campaignsv1.GetCampaignSubscriptionCommentsRequest]) (*connect.Response[campaignsv1.GetCampaignSubscriptionCommentsReponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	campaignSubscriberComments, err := c.repo.GetCampaignSubscriberComments(ctx, campaignsqueries.GetCampaignSubscriberCommentsParams{
		Offset:     paginationInfo.Offset,
		Limit:      paginationInfo.PageSize,
		CampaignID: req.Msg.CampaignId,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.GetCampaignSubscriptionCommentsReponse{
		Data: helpers.Map(campaignSubscriberComments, func(campaignComment campaignsqueries.GetCampaignSubscriberCommentsRow) *campaignsv1.CampaignSubscriptionComments {
			return &campaignsv1.CampaignSubscriptionComments{
				Id: campaignComment.ID,
				User: &sharedv1.Profile{
					Id:    campaignComment.UserID,
					Name:  campaignComment.FullName,
					Image: helpers.GetCdnLinkPointer(campaignComment.Image),
				},
				CreatedAt: timestamppb.New(campaignComment.CreatedAt),
				Comment:   *campaignComment.Comment,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(campaignSubscriberComments)),
	},
	), nil
}

func (c *CampaignService) GetMySupportedCampaigns(ctx context.Context, req *connect.Request[campaignsv1.GetSupportedCampaignRequest]) (*connect.Response[campaignsv1.GetAllCampaignsResponse], error) {

	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	supportedCampaigns, err := c.repo.GetMySupportedCampaigns(ctx, campaignsqueries.GetMySupportedCampaignsParams{
		UserID: web.GetUserFromContext(ctx).ID,
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.GetAllCampaignsResponse{
		Data: helpers.Map(supportedCampaigns, func(supportedCampaign campaignsqueries.GetMySupportedCampaignsRow) *campaignsv1.Campaign {
			return &campaignsv1.Campaign{
				Id:                 supportedCampaign.CampaignID,
				VtuberId:           supportedCampaign.VtuberID,
				ShortDescription:   supportedCampaign.ShortDescription,
				Name:               supportedCampaign.Name,
				StartDate:          timestamppb.New(supportedCampaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(supportedCampaign.Thumbnail),
				TotalBudget:        supportedCampaign.TotalBudget,
				EndDate:            timestamppb.New(supportedCampaign.EndDate),
				CreatedAt:          timestamppb.New(supportedCampaign.CreatedAt),
				PromotionalMessage: supportedCampaign.PromotionalMessage,
				TotalRaised:        supportedCampaign.TotalRaised,
				Slug:               supportedCampaign.Slug,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(supportedCampaign.SocialMediaLinks),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(supportedCampaigns)),
	},
	), nil
}

func (c *CampaignService) GetPopularCampaign(ctx context.Context, _ *connect.Request[campaignsv1.PopularCampaignRequest]) (*connect.Response[campaignsv1.Campaign], error) {
	popularCampaign, err := c.favoritecampaignsqueries.PopularCampaign(ctx)
	if err != nil {

		campaign, err := c.campaignVariantSubscriptionRepo.PopularCampaign(ctx)
		if err != nil {
			campaign, err := c.repo.GetRandomCampaign(ctx)
			if err != nil {
				return nil, err
			}
			return connect.NewResponse(&campaignsv1.Campaign{
				Id:                 campaign.ID,
				VtuberId:           campaign.VtuberID,
				ShortDescription:   campaign.ShortDescription,
				Name:               campaign.Name,
				StartDate:          timestamppb.New(campaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
				TotalBudget:        campaign.TotalBudget,
				EndDate:            timestamppb.New(campaign.EndDate),
				CreatedAt:          timestamppb.New(campaign.CreatedAt),
				PromotionalMessage: campaign.PromotionalMessage,
				TotalRaised:        campaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
				Categories:         campaign.CategoryIds,
				Slug:               campaign.Slug,
			}), nil
		} else {
			campaign, err := c.repo.GetCampaignById(ctx, strconv.FormatInt(campaign.CampaignID, 10))
			if err != nil {
				return nil, err
			}
			return connect.NewResponse(&campaignsv1.Campaign{
				Id:                 campaign.ID,
				VtuberId:           campaign.VtuberID,
				ShortDescription:   campaign.ShortDescription,
				Name:               campaign.Name,
				StartDate:          timestamppb.New(campaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
				TotalBudget:        campaign.TotalBudget,
				EndDate:            timestamppb.New(campaign.EndDate),
				CreatedAt:          timestamppb.New(campaign.CreatedAt),
				PromotionalMessage: campaign.PromotionalMessage,
				TotalRaised:        campaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
				Categories:         campaign.CategoryIds,
				Slug:               campaign.Slug,
			}), nil
		}
	} else {
		campaign, err := c.repo.GetCampaignById(ctx, strconv.FormatInt(popularCampaign.CampaignID, 10))
		if err != nil {
			return nil, err
		}
		return connect.NewResponse(&campaignsv1.Campaign{
			Id:                 campaign.ID,
			VtuberId:           campaign.VtuberID,
			ShortDescription:   campaign.ShortDescription,
			Name:               campaign.Name,
			StartDate:          timestamppb.New(campaign.StartDate),
			Thumbnail:          helpers.GetCdnUrl(campaign.Thumbnail),
			TotalBudget:        campaign.TotalBudget,
			EndDate:            timestamppb.New(campaign.EndDate),
			CreatedAt:          timestamppb.New(campaign.CreatedAt),
			PromotionalMessage: campaign.PromotionalMessage,
			TotalRaised:        campaign.TotalRaised,
			SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(campaign.SocialMediaLinks),
			Categories:         campaign.CategoryIds,
			Slug:               campaign.Slug,
		}), nil

	}

}

func (c *CampaignService) GetRelatedCampaign(p0 context.Context, p1 *connect.Request[campaignsv1.RelatedCampaignRequest]) (*connect.Response[campaignsv1.RelatedCampaignResponse], error) {

	relatedCampaigns, err := c.repo.GetRelatedCampaign(p0, p1.Msg.CategoryId)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.RelatedCampaignResponse{
		Data: helpers.Map(relatedCampaigns, func(relatedCampaign campaignsqueries.GetRelatedCampaignRow) *campaignsv1.Campaign {
			return &campaignsv1.Campaign{
				Id:                 relatedCampaign.ID,
				VtuberId:           relatedCampaign.VtuberID,
				ShortDescription:   relatedCampaign.ShortDescription,
				Name:               relatedCampaign.Name,
				StartDate:          timestamppb.New(relatedCampaign.StartDate),
				Thumbnail:          helpers.GetCdnUrl(relatedCampaign.Thumbnail),
				TotalBudget:        relatedCampaign.TotalBudget,
				EndDate:            timestamppb.New(relatedCampaign.EndDate),
				CreatedAt:          timestamppb.New(relatedCampaign.CreatedAt),
				PromotionalMessage: relatedCampaign.PromotionalMessage,
				TotalRaised:        relatedCampaign.TotalRaised,
				SocialMediaLinks:   sharedv1.SocialMediaLinksFromBytes(relatedCampaign.SocialMediaLinks),
				Slug:               relatedCampaign.Slug,
				Categories:         relatedCampaign.CategoryIds,
			}
		}),
	}), nil
}

func (c *CampaignService) GetMyCampaignNames(ctx context.Context, req *connect.Request[campaignsv1.GetMyCampaignsNameRequest]) (*connect.Response[campaignsv1.GetMyCampaignNamesResponse], error) {
	session := web.GetUserFromContext(ctx)
	campaigns, err := c.repo.GetMyCampaignNames(ctx, *session.VtuberId)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.GetMyCampaignNamesResponse{
		Data: helpers.Map(campaigns, func(campaign campaignsqueries.GetMyCampaignNamesRow) *campaignsv1.CampaignNames {
			return &campaignsv1.CampaignNames{
				Name: campaign.Name,
				Id:   campaign.ID,
			}
		}),
	}), nil
}
