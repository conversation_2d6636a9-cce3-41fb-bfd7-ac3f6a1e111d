package campaigns

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"

	campaignsv1 "github.com/nsp-inc/vtuber/api/campaigns/v1"
	campaignbannersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignbanners"
	campaignsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaigns"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type CampaignBannerService struct {
	repo           *campaignbannersqueries.Queries
	campaignRepo   *campaignsqueries.Queries
	storageService *storagev1.StorageService
}

func NewCampaignBannerService(campaignBannerRepo *campaignbannersqueries.Queries, campaignRepo *campaignsqueries.Queries, storageService *storagev1.StorageService) *CampaignBannerService {
	return &CampaignBannerService{
		repo:           campaignBannerRepo,
		campaignRepo:   campaignRepo,
		storageService: storageService,
	}
}

func (c CampaignBannerService) GetBannerByCampaignId(ctx context.Context, c2 *connect.Request[campaignsv1.GetBannerByCampaignIdRequest]) (*connect.Response[campaignsv1.GetBannerByCampaignIdResponse], error) {
	_, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(c2.Msg.CampaignId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}
	banners, err := c.repo.GetAllCampaignBanners(ctx, c2.Msg.CampaignId)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.GetBannerByCampaignIdResponse{
		Data: helpers.Map(banners, func(banner campaignbannersqueries.CampaignBanner) *campaignsv1.CampaignBanner {
			return &campaignsv1.CampaignBanner{
				Id:         banner.ID,
				Image:      banner.Image,
				Index:      banner.Index,
				CampaignId: banner.CampaignID,
				CreatedAt:  timestamppb.New(banner.CreatedAt),
			}
		}),
	}), nil
}

func (c CampaignBannerService) AddCampaignBanner(ctx context.Context, c2 *connect.Request[campaignsv1.AddCampaignBannerRequest]) (*connect.Response[campaignsv1.AddCampaignBannerResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	campaign, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(c2.Msg.CampaignId, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "campaign", nil),
		}))
	}

	if sessionUser.VtuberId != nil && (campaign.VtuberID != *sessionUser.VtuberId) {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedBannerAdd", nil))
	}

	newFileName, err := c.storageService.ValidateAndUploadFromTemp(ctx, &c2.Msg.Image, storagev1.ImageFileType, "campaigns", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}
	banner, err := c.repo.AddCampaignBanner(ctx, campaignbannersqueries.AddCampaignBannerParams{
		CampaignID: c2.Msg.CampaignId,
		Image:      newFileName,
		Index:      c2.Msg.Index,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.AddCampaignBannerResponse{
		Data: &campaignsv1.CampaignBanner{
			Id:         banner.ID,
			Image:      banner.Image,
			Index:      banner.Index,
			CampaignId: banner.CampaignID,
			CreatedAt:  timestamppb.New(banner.CreatedAt),
		},
	}), nil
}

func (c CampaignBannerService) GetCampaignBannerById(ctx context.Context, c2 *connect.Request[campaignsv1.GetCampaignBannerByIdRequest]) (*connect.Response[campaignsv1.GetCampaignBannerByIdResponse], error) {
	banner, err := c.repo.GetCampaignBannerById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "banner", nil),
		}))
	}

	return connect.NewResponse(&campaignsv1.GetCampaignBannerByIdResponse{
		Data: &campaignsv1.CampaignBanner{
			Id:         banner.ID,
			Image:      banner.Image,
			Index:      banner.Index,
			CampaignId: banner.CampaignID,
			CreatedAt:  timestamppb.New(banner.CreatedAt),
		},
	}), nil

}

func (c CampaignBannerService) DeleteCampaignBannerById(ctx context.Context, c2 *connect.Request[campaignsv1.DeleteCampaignBannerByIdRequest]) (*connect.Response[campaignsv1.DeleteCampaignBannerResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	banner, err := c.repo.GetCampaignBannerById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "banner", nil),
		}))
	}

	campaign, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(banner.CampaignID, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "campaign", nil),
		}))
	}

	if sessionUser.VtuberId != nil && (campaign.VtuberID != *sessionUser.VtuberId) {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedBannerDelete", nil))
	}

	err = c.repo.DeleteCampaignBannerById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.DeleteCampaignBannerResponse{
		Success: true,
		Message: web.GetTranslation(ctx, "bannerDeleted", nil),
	}), nil
}

func (c CampaignBannerService) UpdateCampaignBannerById(ctx context.Context, c2 *connect.Request[campaignsv1.UpdateCampaignBannerByIdRequest]) (*connect.Response[campaignsv1.UpdateCampaignBannerResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	banner, err := c.repo.GetCampaignBannerById(ctx, c2.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "banner", nil),
			}),
		)
	}

	campaign, err := c.campaignRepo.GetCampaignById(ctx, strconv.FormatInt(banner.CampaignID, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "campaign", nil),
			}),
		)
	}

	if sessionUser.VtuberId != nil && (campaign.VtuberID != *sessionUser.VtuberId) {
		return nil, errors.New(
			web.GetTranslation(ctx, "unauthorizedBannerUpdate", nil),
		)
	}

	newPath, err := c.storageService.ValidateAndUploadFromTemp(ctx, &c2.Msg.Image, storagev1.ImageFileType, "campaigns", false, &banner.Image)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	err = c.repo.UpdateCampaignBannerById(ctx, campaignbannersqueries.UpdateCampaignBannerByIdParams{
		ID:    c2.Msg.Id,
		Image: newPath,
		Index: c2.Msg.Index,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&campaignsv1.UpdateCampaignBannerResponse{
		Success: true,
		Message: web.GetTranslation(ctx, "bannerUpdated", nil),
	}), nil
}
