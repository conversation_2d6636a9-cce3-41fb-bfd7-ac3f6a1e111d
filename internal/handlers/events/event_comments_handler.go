package events

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"
	eventsv1 "github.com/nsp-inc/vtuber/api/events/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	eventcommentsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventcomments"
	eventsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/events"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	"github.com/nsp-inc/vtuber/internal/handlers/notifications"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type EventCommentService struct {
	repo                *eventcommentsqueries.Queries
	eventRepo           *eventsqueries.Queries
	notificationService *notifications.NotificationService
	vtuberProfileRepo   *vtuberprofilesqueries.Queries
}

func NewEventCommentService(eventCommentRepo *eventcommentsqueries.Queries, eventRepo *eventsqueries.Queries, notificationService *notifications.NotificationService, vtuberProfileRepo *vtuberprofilesqueries.Queries) *EventCommentService {
	return &EventCommentService{
		repo:                eventCommentRepo,
		eventRepo:           eventRepo,
		notificationService: notificationService,
		vtuberProfileRepo:   vtuberProfileRepo,
	}
}

func (e EventCommentService) AddEventComment(ctx context.Context, c *connect.Request[eventsv1.AddEventCommentRequest]) (*connect.Response[eventsv1.AddEventCommentResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	event, err := e.eventRepo.GetOneEvent(ctx, strconv.FormatInt(c.Msg.EventId, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}
	var repliedUserId *int64
	if c.Msg.ParentId != nil {
		comment, err := e.repo.GetEventCommentById(ctx, *c.Msg.ParentId)
		if err != nil {
			return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}))
		}
		repliedUserId = &comment.UserID
	}

	var vtuberId *int64
	if c.Msg.AsVtuber != nil && *c.Msg.AsVtuber {
		vtuberId = sessionUser.VtuberId
	} else {
		vtuberId = nil
	}
	comment, err := e.repo.AddEventComment(ctx, eventcommentsqueries.AddEventCommentParams{
		EventID:  c.Msg.EventId,
		ParentID: c.Msg.ParentId,
		UserID:   sessionUser.ID,
		Content:  c.Msg.Content,
		VtuberID: vtuberId,
	})

	if err != nil {
		return nil, err
	}

	creatorId, err := e.vtuberProfileRepo.GetVtuberId(ctx, event.UserID)
	if err == nil {
		e.notificationService.CreateCommentNotification(ctx, event.ID, event.UserID, creatorId, comment.ID, repliedUserId, c.Msg.ParentId, "event")
	}

	var vtuber *sharedv1.Profile
	if c.Msg.AsVtuber != nil && *c.Msg.AsVtuber {
		vtuber = &sharedv1.Profile{
			Id:    *comment.VtuberID,
			Name:  *comment.Vtuberdisplayname,
			Image: comment.Vtuberimage,
		}
	} else {
		vtuber = nil
	}

	return connect.NewResponse(
		&eventsv1.AddEventCommentResponse{
			Data: &eventsv1.EventComment{
				Id:        comment.ID,
				EventId:   comment.EventID,
				Content:   comment.Content,
				CreatedAt: timestamppb.New(comment.CreatedAt),
				User: &sharedv1.Profile{
					Id:    comment.UserID,
					Name:  comment.Username,
					Image: comment.Userimage,
				},
				Vtuber: vtuber,
			},
		},
	), nil
}

func (e EventCommentService) GetAllEventComments(ctx context.Context, c *connect.Request[eventsv1.GetAllEventCommentsRequest]) (*connect.Response[eventsv1.GetAllEventCommentsResponse], error) {
	_, err := e.eventRepo.GetOneEvent(ctx, strconv.FormatInt(c.Msg.EventId, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}

	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "created_at", "updated_at"})
	comments, err := e.repo.GetAllEventComments(ctx, eventcommentsqueries.GetAllEventCommentsParams{
		EventID: c.Msg.EventId,
		Limit:   paginationInfo.PageSize,
		Offset:  paginationInfo.Offset,
		Order:   paginationInfo.OrderDirection,
		Sort:    paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}

	var eventComments []*eventsv1.EventComment
	for _, comment := range comments {
		var vtuber *sharedv1.Profile
		if comment.CommentVtuberID != nil {
			vtuber = &sharedv1.Profile{
				Id:    *comment.VtuberID,
				Name:  *comment.VtuberDisplayName,
				Image: comment.VtuberImage,
			}
		} else {
			vtuber = nil
		}

		eventComments = append(eventComments, &eventsv1.EventComment{
			Id:        comment.ID,
			EventId:   comment.EventID,
			ParentId:  comment.ParentID,
			Content:   comment.Content,
			CreatedAt: timestamppb.New(comment.CreatedAt),
			HasReply:  comment.HasReplies,
			User: &sharedv1.Profile{
				Id:    comment.UserID,
				Name:  comment.FullName,
				Image: comment.Image,
			},
			Vtuber: vtuber,
		})
	}

	return connect.NewResponse(
		&eventsv1.GetAllEventCommentsResponse{
			Data:              eventComments,
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(comments)),
		},
	), nil
}

func (e EventCommentService) GetAllRepliesOfEventComment(ctx context.Context, c *connect.Request[eventsv1.GetEventCommentByIdRequest]) (*connect.Response[eventsv1.GetAllEventCommentsResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"id", "created_at", "updated_at"})
	comments, err := e.repo.GetAllRepliesOfEventComment(ctx, eventcommentsqueries.GetAllRepliesOfEventCommentParams{
		ParentID: &c.Msg.Id,
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}

	var eventComments []*eventsv1.EventComment
	for _, comment := range comments {
		var vtuber *sharedv1.Profile
		if comment.CommentVtuberID != nil {
			vtuber = &sharedv1.Profile{
				Id:    *comment.VtuberID,
				Name:  *comment.VtuberDisplayName,
				Image: comment.VtuberImage,
			}
		} else {
			vtuber = nil
		}
		eventComments = append(eventComments, &eventsv1.EventComment{
			Id:        comment.ID,
			EventId:   comment.EventID,
			Content:   comment.Content,
			ParentId:  comment.ParentID,
			CreatedAt: timestamppb.New(comment.CreatedAt),
			User: &sharedv1.Profile{
				Id:    comment.UserID,
				Name:  comment.FullName,
				Image: comment.Image,
			},
			Vtuber: vtuber,
		})
	}

	return connect.NewResponse(
		&eventsv1.GetAllEventCommentsResponse{
			Data:              eventComments,
			PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(comments)),
		},
	), nil

}

func (e EventCommentService) GetEventCommentById(ctx context.Context, c *connect.Request[eventsv1.GetEventCommentByIdRequest]) (*connect.Response[eventsv1.GetEventCommentByIdResponse], error) {
	comment, err := e.repo.GetEventCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}
	return connect.NewResponse(&eventsv1.GetEventCommentByIdResponse{
		Data: &eventsv1.EventComment{
			Id:        comment.ID,
			EventId:   comment.EventID,
			Content:   comment.Content,
			ParentId:  comment.ParentID,
			CreatedAt: timestamppb.New(comment.CreatedAt),
		},
	}), nil
}

func (e EventCommentService) DeleteEventCommentById(ctx context.Context, c *connect.Request[eventsv1.DeleteEventCommentByIdRequest]) (*connect.Response[eventsv1.DeleteEventCommentByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	comment, err := e.repo.GetEventCommentById(ctx, c.Msg.Id)

	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}
	event, err := e.eventRepo.GetOneEvent(ctx, strconv.FormatInt(comment.EventID, 10))
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "event", nil),
		}))
	}
	if comment.UserID != sessionUser.ID && event.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}

	err = e.repo.DeleteEventCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.DeleteEventCommentByIdResponse{

		Message: web.GetTranslation(ctx, "commentDeleted", nil),
		Success: true,
	}), nil
}

func (e EventCommentService) UpdateEventCommentById(ctx context.Context, c *connect.Request[eventsv1.UpdateEventCommentByIdRequest]) (*connect.Response[eventsv1.UpdateEventCommentByIdResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	comment, err := e.repo.GetEventCommentById(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "comment", nil),
			}),
		)
	}

	if comment.UserID != sessionUser.ID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedToPerformAction", nil))
	}
	err = e.repo.UpdateEventCommentById(ctx, eventcommentsqueries.UpdateEventCommentByIdParams{
		ID:      c.Msg.Id,
		Content: c.Msg.Content,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.UpdateEventCommentByIdResponse{

		Message: web.GetTranslation(ctx, "commentUpdated", nil),
		Success: true,
	},
	), nil
}
