package events

import (
	"context"
	"encoding/json"
	"errors"

	"connectrpc.com/connect"
	eventsv1 "github.com/nsp-inc/vtuber/api/events/v1"
	eventparticipantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventparticipants"
	eventparticipantvotesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventparticipantvotes"
	userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"

	// userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"
	"github.com/nsp-inc/vtuber/packages/web"
)

type EventVoteService struct {
	repo                 *eventparticipantvotesqueries.Queries
	eventParticipantRepo *eventparticipantsqueries.Queries
	userPointRepo        *userpointsqueries.Queries
}

type UserPointRemarks struct {
	EventParticipantID int64 `json:"event_participant_id"`
}

func (e EventVoteService) AddVote(ctx context.Context, req *connect.Request[eventsv1.AddVoteRequest]) (*connect.Response[eventsv1.AddVoteResponse], error) {
	tx, err := web.StartTransaction(ctx)
	if err != nil {
		return nil, err
	}
	defer tx.Rollback(ctx)
	eventRepo := e.repo.WithTx(tx)
	userPointRepo := e.userPointRepo.WithTx(tx)

	participate, err := e.eventParticipantRepo.GetEventParticipantByID(ctx, req.Msg.EventParticipationId)
	if err != nil {
		return nil, errors.New("event participant not found")
	}
	if participate.Status != "accepted" {
		return nil, errors.New("cannot vote for this participant because it is not accepted")
	}

	var user = web.GetUserFromContext(ctx)
	var userId = user.ID
	if req.Msg.FromDailyPoint {
		_, err = e.repo.GetTodayVote(ctx, eventparticipantvotesqueries.GetTodayVoteParams{
			EventID: participate.EventID,
			UserID:  &userId,
		})
		if err == nil {
			return nil, errors.New("you have already used your daily point for this event")
		}
		_, err := eventRepo.AddVote(ctx, eventparticipantvotesqueries.AddVoteParams{
			EventParticipantID: req.Msg.EventParticipationId,
			UserID:             &userId,
			Type:               "daily_point",
			EventID:            participate.EventID,
			Count:              1,
		})
		if err != nil {
			return nil, err
		}

	} else {
		if req.Msg.Points == 0 {
			return nil, errors.New("points must be greater than 0")
		}
		var points, err = e.userPointRepo.GetUserPoints(ctx, userId)
		if err != nil {
			return nil, err
		}
		if points <= 0 {
			return nil, errors.New("you do not have enough points to vote")
		}
		_, err = eventRepo.AddVote(ctx, eventparticipantvotesqueries.AddVoteParams{
			EventParticipantID: req.Msg.EventParticipationId,
			UserID:             &userId,
			Type:               "user_point",
			EventID:            participate.EventID,
			Count:              req.Msg.Points,
		})
		if err != nil {
			return nil, err
		}
		//make a remarks json string that the points are used for voting for this event participant
		remarks := UserPointRemarks{
			EventParticipantID: participate.ID,
		}
		remarksJson, err := json.Marshal(remarks)
		if err != nil {
			return nil, err
		}

		_, err = userPointRepo.AddUserPoints(ctx, userpointsqueries.AddUserPointsParams{
			UserID:  userId,
			Points:  float64(-req.Msg.Points),
			Remarks: remarksJson,
		})
		if err != nil {
			return nil, err
		}
	}

	err = tx.Commit(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&eventsv1.AddVoteResponse{
		Success: true,
		Message: "Vote added successfully",
	}), nil

}

func (e EventVoteService) GetDailyPointAvailable(ctx context.Context, req *connect.Request[eventsv1.GetDailyPointAvailableRequest]) (*connect.Response[eventsv1.GetDailyPointAvailableResponse], error) {
	_, err := e.repo.GetTodayVote(ctx, eventparticipantvotesqueries.GetTodayVoteParams{
		EventID: req.Msg.EventId,
		UserID:  &web.GetUserFromContext(ctx).ID,
	})
	var isAvailable = false
	if err != nil {
		isAvailable = true
	}
	return connect.NewResponse(&eventsv1.GetDailyPointAvailableResponse{
		Available: isAvailable,
	}), nil

}

func (e EventVoteService) GivePlatformPoint(ctx context.Context, req *connect.Request[eventsv1.GivePlatformPointRequest]) (*connect.Response[eventsv1.GivePlatformPointResponse], error) {
	participate, err := e.eventParticipantRepo.GetEventParticipantByID(ctx, req.Msg.EventParticipationId)
	if err != nil {
		return nil, err
	}
	if participate.Status != "accepted" {
		return nil, errors.New("please accept the event participant first to give platform point")
	}

	platfromVote, err := e.repo.GetPlatformVote(ctx, participate.ID)
	if err == nil {

		err = e.repo.UpdatePlatformVote(ctx, eventparticipantvotesqueries.UpdatePlatformVoteParams{
			ID:      platfromVote.ID,
			Count:   int32(req.Msg.Points),
			Remarks: &req.Msg.Remarks,
			UserID:  &web.GetUserFromContext(ctx).ID,
		})
		if err != nil {
			return nil, err
		}
	} else {
		_, err := e.repo.AddVote(ctx, eventparticipantvotesqueries.AddVoteParams{
			EventParticipantID: participate.ID,
			UserID:             &web.GetUserFromContext(ctx).ID,
			Type:               "platform_point",
			EventID:            participate.EventID,
			Count:              int32(req.Msg.Points),
			Remarks:            &req.Msg.Remarks,
		})
		if err != nil {
			return nil, err
		}
	}

	return connect.NewResponse(&eventsv1.GivePlatformPointResponse{
		Success: true,
		Message: "Platform point added successfully",
	}), nil
}

func (e *EventVoteService) GetPlatfromPointOfEventParticipation(ctx context.Context, req *connect.Request[eventsv1.GetPlatfromPoint]) (*connect.Response[eventsv1.GivePlatformPointRequest], error) {
	platfromPoint, err := e.repo.GetPlatformVote(ctx, req.Msg.EventParticipationId)
	if err != nil {
		return connect.NewResponse(&eventsv1.GivePlatformPointRequest{}), nil
	}
	return connect.NewResponse(&eventsv1.GivePlatformPointRequest{
		EventParticipationId: platfromPoint.EventParticipantID,
		Remarks:              *platfromPoint.Remarks,
		Points:               int64(platfromPoint.Count),
	}), nil

}

func NewEventVoteService(repo *eventparticipantvotesqueries.Queries, eventParticipantRepo *eventparticipantsqueries.Queries, userPointRepo *userpointsqueries.Queries) *EventVoteService {
	return &EventVoteService{
		repo:                 repo,
		eventParticipantRepo: eventParticipantRepo,
		userPointRepo:        userPointRepo,
	}
}
