package events

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"
	eventsv1 "github.com/nsp-inc/vtuber/api/events/v1"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	eventparticipantsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventparticipants"
	eventsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/events"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type EventParticipantService struct {
	repo      *eventparticipantsqueries.Queries
	eventRepo *eventsqueries.Queries
}

func NewEventParticipantService(repo *eventparticipantsqueries.Queries, eventRepo *eventsqueries.Queries) *EventParticipantService {
	return &EventParticipantService{
		repo:      repo,
		eventRepo: eventRepo,
	}
}

func (e EventParticipantService) ChangeStatus(ctx context.Context, req *connect.Request[eventsv1.ChangeStatusRequest]) (*connect.Response[eventsv1.ChangeStatusResponse], error) {
	if req.Msg.Status == "rejected" && (req.Msg.Reason == nil) {
		return nil, errors.New("reason is required when status is rejected")
	}
	_, err := e.repo.GetEventParticipantByID(ctx, req.Msg.EventParticipationId)
	if err != nil {
		return nil, err
	}
	var remarks *string
	var status = eventparticipantsqueries.EventParticipantStatusAccepted
	if req.Msg.Status != "approved" {
		status = eventparticipantsqueries.EventParticipantStatusRejected
		remarks = req.Msg.Reason
	}
	err = e.repo.ChangeStatus(ctx, eventparticipantsqueries.ChangeStatusParams{
		ID:      req.Msg.EventParticipationId,
		Status:  status,
		Remarks: remarks,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.ChangeStatusResponse{
		Success: true,
		Message: "event participation status changed successfully",
	}), nil

}

func (e EventParticipantService) GetAllEventParticipation(ctx context.Context, req *connect.Request[eventsv1.GetAllEventParticipationRequest]) (*connect.Response[eventsv1.GetAllEventParticipationResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})

	participants, err := e.repo.GetAllEventParticipants(ctx, eventparticipantsqueries.GetAllEventParticipantsParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
	})
	if err != nil {
		return nil, err
	}

	var participations []*eventsv1.EventParticipation
	for _, event := range participants {
		var user, vtuber *sharedv1.Profile

		if event.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *event.Userid,
				Name:  *event.Username,
				Image: event.UserImage,
			}
		}

		if event.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *event.Vtuberid,
				Name:  *event.Vtubername,
				Image: event.Vtuberimage,
			}
		}

		participations = append(participations, &eventsv1.EventParticipation{
			Id:        event.ID,
			EventId:   event.EventID,
			VtuberId:  event.VtuberID,
			VoteCount: int64(event.VoteCount),
			Status:    string(event.Status),
			Remarks:   event.Remarks,
			Vtuber: &sharedv1.Profile{
				Id:       event.EventVtuberID,
				Name:     event.EventVtuberName,
				Furigana: &event.EventVtuberFurigana,
				Image:    helpers.GetCdnLinkPointer(event.EventVtuberImage),
			},
			Event: &eventsv1.Event{
				Id:               event.ID,
				User:             user,
				Categories:       event.CategoryIds,
				Vtuber:           vtuber,
				Description:      event.Description,
				ShortDescription: event.ShortDescription,
				StartDate:        timestamppb.New(event.StartDate),
				EndDate:          timestamppb.New(event.EndDate),
				Image:            helpers.GetCdnUrl(event.Image),
				Rules:            event.Rules,
				Title:            event.Title,
				Status:           string(event.EventStatus),
				HasParticipated:  true,
				CreatedAt:        timestamppb.New(event.EventCreatedAt),
				Slug:             event.Slug,
			},
		})

	}

	return connect.NewResponse(&eventsv1.GetAllEventParticipationResponse{
		Data:              participations,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(participants)),
	}), nil

}

func (e EventParticipantService) AddEventParticipation(ctx context.Context, req *connect.Request[eventsv1.AddEventParticipationRequest]) (*connect.Response[eventsv1.AddEventParticipationResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	event, err := e.eventRepo.GetOneEvent(ctx, strconv.FormatInt(req.Msg.EventId, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "event", nil),
			}),
		)
	}

	if event.Status != "approved" {
		return nil, errors.New("event is not approved yet")
	}

	_, err = e.repo.GetEventParticipantByEventIDAndVtuberID(ctx, eventparticipantsqueries.GetEventParticipantByEventIDAndVtuberIDParams{
		VtuberID: *sessionUser.VtuberId,
		EventID:  req.Msg.EventId,
	})
	if err == nil {
		return nil, errors.New("you already participated in this event")
	}

	_, err = e.repo.ParticipantInEvent(ctx, eventparticipantsqueries.ParticipantInEventParams{
		EventID:  req.Msg.EventId,
		VtuberID: *sessionUser.VtuberId,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&eventsv1.AddEventParticipationResponse{
		Success: true,
		Message: "event participation added successfully",
	}), nil
}

func (e EventParticipantService) GetEventParticipantsByEventId(ctx context.Context, req *connect.Request[eventsv1.GetEventParticipantsByEventIdRequest]) (*connect.Response[eventsv1.GetEventParticipantsByEventIdResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})

	participants, err := e.repo.GetEventParticipantByEventID(ctx, eventparticipantsqueries.GetEventParticipantByEventIDParams{
		Limit:   paginationInfo.PageSize,
		Offset:  paginationInfo.Offset,
		Order:   paginationInfo.OrderDirection,
		Sort:    paginationInfo.OrderBy,
		EventID: req.Msg.EventId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.GetEventParticipantsByEventIdResponse{
		Data: helpers.Map(participants, func(participant eventparticipantsqueries.GetEventParticipantByEventIDRow) *eventsv1.EventParticipation {
			return &eventsv1.EventParticipation{
				Id:        participant.ID,
				EventId:   participant.EventID,
				VtuberId:  participant.VtuberID,
				VoteCount: int64(participant.VoteCount),
				Remarks:   participant.Remarks,
				Status:    string(participant.Status),
				Vtuber: &sharedv1.Profile{
					Id:               participant.EventVtuberID,
					Name:             participant.VtuberName,
					Furigana:         &participant.VtuberFurigana,
					Image:            helpers.GetCdnLinkPointer(participant.VtuberImage),
					Introduction:     participant.VtuberIntroduction,
					SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(participant.SocialMediaLinks),
				},
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(participants)),
	}), nil

}

func (e EventParticipantService) GetMyEventParticipation(ctx context.Context, req *connect.Request[eventsv1.GetMyEventParticipationRequest]) (*connect.Response[eventsv1.GetMyEventParticipationResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	participants, err := e.repo.GetEventParticipantByVtuberID(ctx, eventparticipantsqueries.GetEventParticipantByVtuberIDParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: *web.GetUserFromContext(ctx).VtuberId,
	})

	if err != nil {
		return nil, err
	}

	var participations []*eventsv1.EventParticipation
	for _, participant := range participants {
		var user, vtuber *sharedv1.Profile

		if participant.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *participant.Userid,
				Name:  *participant.Username,
				Image: participant.UserImage,
			}
		}

		if participant.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *participant.Vtuberid,
				Name:  *participant.Vtubername,
				Image: participant.Vtuberimage,
			}
		}

		participations = append(participations, &eventsv1.EventParticipation{
			Id:        participant.ID,
			EventId:   participant.EventID,
			VtuberId:  participant.VtuberID,
			VoteCount: int64(participant.VoteCount),
			Status:    string(participant.Status),
			Remarks:   participant.Remarks,
			Event: &eventsv1.Event{
				Id:               participant.EventID,
				Categories:       participant.CategoryIds,
				User:             user,
				Vtuber:           vtuber,
				Description:      participant.Description,
				ShortDescription: participant.ShortDescription,
				StartDate:        timestamppb.New(participant.StartDate),
				EndDate:          timestamppb.New(participant.EndDate),
				Image:            helpers.GetCdnUrl(participant.Image),
				Rules:            participant.Rules,
				Title:            participant.Title,
				Status:           string(participant.EventStatus),
				HasParticipated:  true,
				CreatedAt:        timestamppb.New(participant.EventCreatedAt),
				Slug:             participant.Slug,
			},
		})

	}

	return connect.NewResponse(&eventsv1.GetMyEventParticipationResponse{
		Data:              participations,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(participants)),
	}), nil

}

func (e *EventParticipantService) GetCreatorEventParticipation(ctx context.Context, req *connect.Request[eventsv1.GetCreatorEventParticipationRequest]) (*connect.Response[eventsv1.GetCreatorEventParticipationResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	events, err := e.repo.GetCreatorEventParticipation(ctx, *sessionUser.VtuberId)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&eventsv1.GetCreatorEventParticipationResponse{
		Events: events,
	}), nil
}

func (e *EventParticipantService) GetAllEventParticipantsByEventId(ctx context.Context, req *connect.Request[eventsv1.GetEventParticipantsByEventIdRequest]) (*connect.Response[eventsv1.GetEventParticipantsByEventIdResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	participants, err := e.repo.GetAllEventParticipantByEventID(ctx, eventparticipantsqueries.GetAllEventParticipantByEventIDParams{
		Limit:   paginationInfo.PageSize,
		Offset:  paginationInfo.Offset,
		Order:   paginationInfo.OrderDirection,
		Sort:    paginationInfo.OrderBy,
		EventID: req.Msg.EventId,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&eventsv1.GetEventParticipantsByEventIdResponse{
		Data: helpers.Map(participants, func(participant eventparticipantsqueries.GetAllEventParticipantByEventIDRow) *eventsv1.EventParticipation {
			return &eventsv1.EventParticipation{
				Id:        participant.ID,
				EventId:   participant.EventID,
				Remarks:   participant.Remarks,
				VtuberId:  participant.VtuberID,
				VoteCount: int64(participant.VoteCount),
				Status:    string(participant.Status),
				Vtuber: &sharedv1.Profile{
					Id:           participant.EventVtuberID,
					Name:         participant.VtuberName,
					Furigana:     &participant.VtuberFurigana,
					Image:        helpers.GetCdnLinkPointer(participant.VtuberImage),
					Introduction: participant.VtuberIntroduction,
				},
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(participants)),
	}), nil

}

func (e EventParticipantService) GetAllEventParticipationOfVtuber(ctx context.Context, req *connect.Request[eventsv1.GetEventParticipationOfVtuberRequest]) (*connect.Response[eventsv1.GetAllEventParticipationResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	participants, err := e.repo.GetEventParticipantByVtuberID(ctx, eventparticipantsqueries.GetEventParticipantByVtuberIDParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: req.Msg.VtuberId,
	})

	if err != nil {
		return nil, err
	}

	var participations []*eventsv1.EventParticipation
	for _, participant := range participants {
		var user, vtuber *sharedv1.Profile

		if participant.Userid != nil {
			user = &sharedv1.Profile{
				Id:    *participant.Userid,
				Name:  *participant.Username,
				Image: participant.UserImage,
			}
		}

		if participant.Vtuberid != nil {
			vtuber = &sharedv1.Profile{
				Id:    *participant.Vtuberid,
				Name:  *participant.Vtubername,
				Image: participant.Vtuberimage,
			}
		}

		participations = append(participations, &eventsv1.EventParticipation{
			Id:        participant.ID,
			EventId:   participant.EventID,
			VtuberId:  participant.VtuberID,
			VoteCount: int64(participant.VoteCount),
			Status:    string(participant.Status),
			Remarks:   participant.Remarks,
			Event: &eventsv1.Event{
				Id: participant.EventID,
				// CategoryId:       participant.CategoryID,
				User:             user,
				Vtuber:           vtuber,
				Description:      participant.Description,
				ShortDescription: participant.ShortDescription,
				StartDate:        timestamppb.New(participant.StartDate),
				EndDate:          timestamppb.New(participant.EndDate),
				Image:            helpers.GetCdnUrl(participant.Image),
				Rules:            participant.Rules,
				Title:            participant.Title,
				Status:           string(participant.EventStatus),
				HasParticipated:  true,
				CreatedAt:        timestamppb.New(participant.EventCreatedAt),
				Slug:             participant.Slug,
			},
		})

	}

	return connect.NewResponse(&eventsv1.GetAllEventParticipationResponse{
		Data:              participations,
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(participants)),
	}), nil

}

func (e EventParticipantService) GetTopTenEventParticipantsVtubers(ctx context.Context, req *connect.Request[eventsv1.GetTopTenEventParticipantsVtuberRequest]) (*connect.Response[eventsv1.GetTopTenEventParticipantsVtuberResponse], error) {
	participants, err := e.repo.GetTopTenEventParticipantVtubers(ctx, req.Msg.EventId)

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&eventsv1.GetTopTenEventParticipantsVtuberResponse{
		Data: helpers.Map(participants, func(participant eventparticipantsqueries.GetTopTenEventParticipantVtubersRow) *eventsv1.EventParticipantVtuber {
			return &eventsv1.EventParticipantVtuber{
				Name:      participant.DisplayName,
				Image:     helpers.GetCdnLinkPointer(participant.Image),
				VoteCount: int32(participant.TotalVotes),
				Id:        participant.ID,
			}
		}),
	}), nil

}
