package users

import (
	"context"
	"errors"

	campaignvariantsubscriptionsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/campaignvariantsubscriptions"
	userpointsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userpoints"
	"github.com/nsp-inc/vtuber/packages/web"

	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/validation"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	usersv1 "github.com/nsp-inc/vtuber/api/users/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	usersv1repo "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type UserService struct {
	repo                            *usersv1repo.Queries
	storageService                  *storagev1.StorageService
	userPointRepo                   *userpointsqueries.Queries
	campaignVariantSubscriptionRepo *campaignvariantsubscriptionsqueries.Queries
}

func NewUserService(userRepo *usersv1repo.Queries, storageService *storagev1.StorageService, userPointRepo *userpointsqueries.Queries, campaignVariantSubscriptionRepo *campaignvariantsubscriptionsqueries.Queries) *UserService {
	return &UserService{
		repo:                            userRepo,
		storageService:                  storageService,
		userPointRepo:                   userPointRepo,
		campaignVariantSubscriptionRepo: campaignVariantSubscriptionRepo,
	}
}

func (u UserService) GetAllUsers(ctx context.Context, c *connect.Request[usersv1.GetAllUsersRequest]) (*connect.Response[usersv1.GetAllUsersResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"full_name", "email", "created_at", "updated_at"})
	users, err := u.repo.ListUsers(ctx, usersv1repo.ListUsersParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
		Email:  c.Msg.Email,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.GetAllUsersResponse{
		Data: helpers.Map(users, func(user usersv1repo.ListUsersRow) *usersv1.User {
			return &usersv1.User{
				Id:            user.ID,
				FullName:      user.FullName,
				Email:         user.Email,
				Image:         helpers.GetCdnLinkPointer(user.Image),
				Role:          string(user.Role),
				IsBanned:      user.IsBanned,
				CreatedAt:     timestamppb.New(user.CreatedAt),
				IsVtuber:      user.Role == usersv1repo.UserRoleVtuber,
				EmailVerified: user.EmailVerified,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(users)),
	}), nil

}

func (u UserService) GetUserById(ctx context.Context, c *connect.Request[usersv1.GetUserByIdRequest]) (*connect.Response[usersv1.GetUserByIdResponse], error) {
	user, err := u.repo.GetUser(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "user", nil),
			}),
		)
	}

	var vtuber *vtubersv1.VtuberProfile
	if user.VtuberID != nil {
		vtuber = &vtubersv1.VtuberProfile{
			Id: *user.VtuberID,
		}
	} else {
		vtuber = nil
	}

	return connect.NewResponse(&usersv1.GetUserByIdResponse{
		Data: &usersv1.User{
			Id:            user.ID,
			FullName:      user.FullName,
			Email:         user.Email,
			Image:         helpers.GetCdnLinkPointer(user.Image),
			Role:          string(user.Role),
			IsBanned:      user.IsBanned,
			IsVtuber:      user.Role == usersv1repo.UserRoleVtuber,
			EmailVerified: user.EmailVerified,
			CreatedAt:     timestamppb.New(user.CreatedAt),
			Vtuber:        vtuber,
		},
	}), nil

}

func (u UserService) DeleteUserById(ctx context.Context, c *connect.Request[usersv1.DeleteUserByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	_, err := u.repo.GetUserByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "user", nil),
			}),
		)
	}
	err = u.repo.DeleteUser(ctx, c.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "userDeleted", nil),
		Success: true,
	}), nil
}

func (u UserService) UpdateUserById(ctx context.Context, c *connect.Request[usersv1.UpdateUserByIdRequest]) (*connect.Response[usersv1.UpdateUserResponse], error) {
	user, err := u.repo.GetUserByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "user", nil),
			}),
		)
	}

	newImage, err := u.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, "users", false, user.Image)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}
	user, err = u.repo.UpdateUser(ctx, usersv1repo.UpdateUserParams{
		ID:       c.Msg.Id,
		FullName: c.Msg.FullName,
		Image:    helpers.GetPointerString(newImage),
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.UpdateUserResponse{
		Data: &usersv1.User{
			Id:            user.ID,
			FullName:      user.FullName,
			Email:         user.Email,
			Image:         helpers.GetCdnLinkPointer(user.Image),
			Role:          string(user.Role),
			IsBanned:      user.IsBanned,
			CreatedAt:     timestamppb.New(user.CreatedAt),
			IsVtuber:      user.Role == usersv1repo.UserRoleVtuber,
			EmailVerified: user.EmailVerified,
		},
	}), nil
}

func (u UserService) BanUser(ctx context.Context, c *connect.Request[usersv1.BanUserRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	_, err := u.repo.GetUserByID(ctx, c.Msg.Id)
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "user", nil),
			}),
		)
	}
	err = u.repo.BanUser(ctx, c.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "userBanned", nil),
		Success: true,
	}), nil
}

func (u *UserService) GetAllDeletedUsers(ctx context.Context, c *connect.Request[usersv1.GetAllDeletedUsersRequest]) (*connect.Response[usersv1.GetAllDeletedUsersResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"full_name", "email", "created_at", "updated_at"})
	users, err := u.repo.ListDeletedUsers(ctx, usersv1repo.ListDeletedUsersParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
		Email:  c.Msg.Email,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.GetAllDeletedUsersResponse{
		Data: helpers.Map(users, func(user usersv1repo.ListDeletedUsersRow) *usersv1.User {
			return &usersv1.User{
				Id:            user.ID,
				FullName:      user.FullName,
				Email:         user.Email,
				Image:         helpers.GetCdnLinkPointer(user.Image),
				Role:          string(user.Role),
				IsBanned:      user.IsBanned,
				IsVtuber:      user.Role == usersv1repo.UserRoleVtuber,
				EmailVerified: user.EmailVerified,
				CreatedAt:     timestamppb.New(user.CreatedAt),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(users)),
	}), nil
}

func (u UserService) GetAllBannedUsers(ctx context.Context, c *connect.Request[usersv1.GetAllUsersRequest]) (*connect.Response[usersv1.GetAllUsersResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(c.Msg.Pagination, []string{"full_name", "email", "created_at", "updated_at"})
	users, err := u.repo.ListOfBannedUsers(ctx, usersv1repo.ListOfBannedUsersParams{
		Limit:  paginationInfo.PageSize,
		Offset: paginationInfo.Offset,
		Order:  paginationInfo.OrderDirection,
		Sort:   paginationInfo.OrderBy,
		Email:  c.Msg.Email,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.GetAllUsersResponse{
		Data: helpers.Map(users, func(user usersv1repo.ListOfBannedUsersRow) *usersv1.User {
			return &usersv1.User{
				Id:            user.ID,
				FullName:      user.FullName,
				Email:         user.Email,
				Image:         helpers.GetCdnLinkPointer(user.Image),
				Role:          string(user.Role),
				IsBanned:      user.IsBanned,
				CreatedAt:     timestamppb.New(user.CreatedAt),
				IsVtuber:      user.Role == usersv1repo.UserRoleVtuber,
				EmailVerified: user.EmailVerified,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(users)),
	}), nil
}

func (u UserService) GetUserPoint(ctx context.Context, req *connect.Request[usersv1.GetUserPointRequest]) (*connect.Response[usersv1.GetUserResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	point, err := u.userPointRepo.GetUserPoints(ctx, sessionUser.ID)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.GetUserResponse{
		Point: float32(point),
	}), nil

}

func (u UserService) GetUserCampaignInvestment(ctx context.Context, req *connect.Request[usersv1.GetUserCampaignInvestmentRequest]) (*connect.Response[usersv1.GetUserCampaignInvestmentResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(req.Msg.Pagination, []string{"created_at"})
	investments, err := u.campaignVariantSubscriptionRepo.GetUserCampaignVariantSubscriptions(ctx,
		campaignvariantsubscriptionsqueries.GetUserCampaignVariantSubscriptionsParams{
			UserID: req.Msg.UserId,
			Limit:  paginationInfo.PageSize,
			Offset: paginationInfo.Offset,
			Order:  paginationInfo.OrderDirection,
			Sort:   paginationInfo.OrderBy,
		})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&usersv1.GetUserCampaignInvestmentResponse{
		Data: helpers.Map(investments, func(investment campaignvariantsubscriptionsqueries.GetUserCampaignVariantSubscriptionsRow) *usersv1.CampaignInvestment {
			return &usersv1.CampaignInvestment{
				UserId:                     investment.UserID,
				CampaignId:                 investment.CampaignID,
				Amount:                     investment.Price,
				CreatedAt:                  timestamppb.New(investment.CreatedAt),
				CampaignVariantId:          investment.CampaignVariantID,
				CampaignVariantTitle:       investment.CampaignVariantTitle,
				CampaignVariantImage:       investment.CampaignVariantImage,
				CampaignVariantDescription: investment.CampaignVariantDescription,
				CampaignName:               investment.CampaignName,
				CampaignThumbnail:          investment.CampaignThumbnail,
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(investments)),
	}), nil

}
