package users

import (
	"context"
	"errors"
	"fmt"
	"time"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	usersv1 "github.com/nsp-inc/vtuber/api/users/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	accountsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/accounts"
	sessionqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/session"
	usersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	verificationcodesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/verificationcodes"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	"github.com/nsp-inc/vtuber/internal/domain/users"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/hasher"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/jwt"
	"github.com/nsp-inc/vtuber/packages/mail"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type AuthHandlerService struct {
	vtuberRepo       *vtuberprofilesqueries.Queries
	storageService   *storagev1.StorageService
	userRepo         *usersqueries.Queries
	accountsRepo     *accountsqueries.Queries
	sessionRepo      *sessionqueries.Queries
	verificationRepo *verificationcodesqueries.Queries
}

func NewAuthHandlerService(
	vtuberRepo *vtuberprofilesqueries.Queries,
	storageService *storagev1.StorageService,
	userRepo *usersqueries.Queries,
	accountsRepo *accountsqueries.Queries,
	sessionRepo *sessionqueries.Queries,
	verificationRepo *verificationcodesqueries.Queries,
) *AuthHandlerService {
	return &AuthHandlerService{
		userRepo:         userRepo,
		vtuberRepo:       vtuberRepo,
		storageService:   storageService,
		accountsRepo:     accountsRepo,
		sessionRepo:      sessionRepo,
		verificationRepo: verificationRepo,
	}
}

func (a *AuthHandlerService) SignInWithEmail(ctx context.Context, req *connect.Request[usersv1.SignInWithEmailRequest]) (*connect.Response[usersv1.SignInWithEmailResponse], error) {
	user, err := a.userRepo.GetUserByEmail(ctx, &req.Msg.Email)

	if err != nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("email not found: %w", err))
	}
	data, err := a.accountsRepo.GetAccountByEmail(ctx, &req.Msg.Email)

	if err != nil {
		return nil, errors.New("you have not set up password login for this account. Reset your password or use social login")
	}

	if data.User.IsBanned {
		return nil, connect.NewError(connect.CodePermissionDenied, errors.New("user is banned"))
	}

	isCorrect := hasher.CompareHash(*data.Account.Password, req.Msg.Password)

	if !isCorrect {
		return nil, connect.NewError(connect.CodeInvalidArgument, errors.New("password is incorrect"))
	}

	var vtuberId *int64

	vtuberProfile, err := a.vtuberRepo.GetVtuberProfileByUserID(ctx, data.User.ID)

	if err == nil {
		vtuberId = &vtuberProfile.ID
	}

	tokens, err := jwt.CreateJwt(user, vtuberId)

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	ua := req.Header().Get("User-Agent")
	ip := req.Header().Get("X-Forwarded-For")

	err = a.sessionRepo.CreateSession(ctx, sessionqueries.CreateSessionParams{
		UserID:    user.ID,
		Uuid:      tokens.UUID.String(),
		UserAgent: &ua,
		IpAddress: &ip,
		ExpiresAt: tokens.ExpiresAt,
	})
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(&usersv1.SignInWithEmailResponse{
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
	}), nil

}

func (a *AuthHandlerService) RefreshToken(ctx context.Context, req *connect.Request[usersv1.RefreshTokenRequest]) (*connect.Response[usersv1.RefreshTokenResponse], error) {
	claims, err := jwt.VerifyRefreshToken(req.Msg.RefreshToken)
	if err != nil {
		return nil, connect.NewError(connect.CodePermissionDenied, err)
	}

	session, err := a.sessionRepo.GetSession(ctx, sessionqueries.GetSessionParams{
		Uuid:   claims.UUID.String(),
		UserID: claims.ID,
	})

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	if time.Now().After(session.ExpiresAt) {
		return nil, connect.NewError(connect.CodePermissionDenied, errors.New("session expired"))
	}

	user, err := a.userRepo.GetUserById(ctx, session.UserID)

	if err != nil {
		return nil, err
	}

	var vtuberId *int64

	vtuberProfile, err := a.vtuberRepo.GetVtuberProfileByUserID(ctx, user.ID)

	if err == nil {
		vtuberId = &vtuberProfile.ID
	}

	tokens, err := jwt.CreateJwt(user, vtuberId)
	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	ip := req.Header().Get("X-Forwarded-For")
	ua := req.Header().Get("User-Agent")

	err = a.sessionRepo.UpdateSession(ctx, sessionqueries.UpdateSessionParams{
		IpAddress: &ip,
		UserAgent: &ua,
		NewUuid:   tokens.UUID.String(),
		OldUuid:   claims.UUID.String(),
		ExpiresAt: tokens.ExpiresAt,
	})

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	return connect.NewResponse(&usersv1.RefreshTokenResponse{
		AccessToken:  tokens.AccessToken,
		RefreshToken: tokens.RefreshToken,
	}), nil

}

func (a *AuthHandlerService) SignOut(ctx context.Context, req *connect.Request[usersv1.SignOutRequest]) (*connect.Response[usersv1.SignOutResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	err := a.sessionRepo.DeleteSession(ctx, sessionqueries.DeleteSessionParams{
		UserID: sessionUser.ID,
		Uuid:   sessionUser.UUID.String(),
	})

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}
	return connect.NewResponse(&usersv1.SignOutResponse{
		Success: true,
		Message: "Sign out successfully",
	}), nil

}

func (a *AuthHandlerService) GetSession(ctx context.Context, req *connect.Request[usersv1.GetSessionRequest]) (*connect.Response[usersv1.GetSessionResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	user, err := a.userRepo.GetUserById(ctx, sessionUser.ID)

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	var dob *timestamppb.Timestamp

	if user.DateOfBirth != nil {
		dob = timestamppb.New(*user.DateOfBirth)
	}

	var vtuber *vtubersv1.VtuberProfile

	vtuberProfile, err := a.vtuberRepo.GetVtuberProfileByUserID(ctx, user.ID)
	if err == nil {
		vtuber = &vtubersv1.VtuberProfile{
			Id:               vtuberProfile.ID,
			UserId:           vtuberProfile.UserID,
			DisplayName:      vtuberProfile.DisplayName,
			Furigana:         vtuberProfile.Furigana,
			Image:            helpers.GetCdnLinkPointer(vtuberProfile.Image),
			BannerImage:      helpers.GetCdnLinkPointer(vtuberProfile.BannerImage),
			SocialMediaLinks: sharedv1.SocialMediaLinksFromBytes(vtuberProfile.SocialMediaLinks),
			Description:      vtuberProfile.Description,
			Categories:       vtuberProfile.CategoryIds,
			IsPlanActive:     vtuberProfile.IsPlanActive,
			Username:         vtuberProfile.Username,
		}
	}

	return connect.NewResponse(&usersv1.GetSessionResponse{
		User: &usersv1.User{
			Id:            user.ID,
			Email:         user.Email,
			FullName:      user.FullName,
			Image:         helpers.GetCdnLinkPointer(user.Image),
			Dob:           dob,
			EmailVerified: user.EmailVerified,
			Role:          string(user.Role),
		},
		Vtuber: vtuber,
	}), nil

}

func (a *AuthHandlerService) SendSignupEmailVerificationCode(ctx context.Context, req *connect.Request[usersv1.SendSignupEmailVerificationCodeRequest]) (*connect.Response[usersv1.SendSignupEmailVerificationCodeResponse], error) {
	_, err := a.userRepo.GetUserByEmail(context.Background(), &req.Msg.Email)
	if err != nil && helpers.IsNoRowsError(err) {
		otp, err := a.CreateVerificationCode(nil, &req.Msg.Email, verificationcodesqueries.VerificationCodeTypeSignupEmailVerification)
		if err != nil {
			return nil, err
		}
		// Send the OTP to the user's email

		_, err = jwt.CreateVerificationCodeJwt(jwt.SignupEmailVerification, req.Msg.Email, nil)
		if err != nil {
			return nil, err
		}
		// TODO: ADD TO QUEUE
		mail.SendRegistrationEmail(ctx, req.Msg.Email, otp)
		return connect.NewResponse(&usersv1.SendSignupEmailVerificationCodeResponse{
			Success: true,
		}), nil
	} else {
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("email already exists")
	}
}

func (a *AuthHandlerService) VerifySignupEmail(ctx context.Context, req *connect.Request[usersv1.VerifySignupEmailRequest]) (*connect.Response[usersv1.VerifySignupEmailResponse], error) {
	err := a.VerifyVerificationCode(req.Msg.VerificationCode, nil, &req.Msg.Email, verificationcodesqueries.VerificationCodeTypeSignupEmailVerification)

	if err != nil {
		return nil, err
	}

	signupToken, err := jwt.CreateVerificationCodeJwt(jwt.SignupEmailVerification, req.Msg.Email, nil)

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&usersv1.VerifySignupEmailResponse{
		Token: signupToken,
	}), nil
}

func (a *AuthHandlerService) SignupWithEmail(ctx context.Context, req *connect.Request[usersv1.SignupWithEmailRequest]) (*connect.Response[usersv1.SignupWithEmailResponse], error) {
	token := req.Msg.Token
	claims, err := jwt.VerifyVerificationToken(token)

	if err != nil {
		return nil, err
	}

	if claims.Type != jwt.SignupEmailVerification {
		return nil, fmt.Errorf("invalid verification type")
	}

	_, err = a.userRepo.GetUserByEmail(context.Background(), &claims.Email)

	if err != nil && helpers.IsNoRowsError(err) {
		tx, err := web.StartTransaction(ctx)
		if err != nil {
			panic("Failed to begin transaction: " + err.Error())
		}

		defer tx.Rollback(context.Background())

		accountqueries := a.accountsRepo.WithTx(tx)
		dob := req.Msg.DateOfBirth.AsTime()

		user, err := a.userRepo.CreateUser(context.Background(), usersqueries.CreateUserParams{
			FullName:      req.Msg.FullName,
			Email:         &claims.Email,
			DateOfBirth:   &dob,
			EmailVerified: true,
			Role:          usersqueries.UserRoleUser,
		})

		if err != nil {

			return nil, err
		}

		hashedPassword, err := hasher.Hash(req.Msg.Password)

		if err != nil {
			return nil, err
		}

		lastThreePasswords := users.LastThreePasswords{}

		lastThreePasswords.AddPassword(hashedPassword)

		_, err = accountqueries.CreateAccount(context.Background(), accountsqueries.CreateAccountParams{
			ProviderID:        "email",
			UserID:            user.ID,
			Password:          &hashedPassword,
			LastThreePassword: lastThreePasswords.ToByte(),
		})

		if err != nil {
			return nil, err
		}

		err = tx.Commit(context.Background())

		if err != nil {
			return nil, err
		}

		mail.SendMemberRegisterMail(ctx, user.Email, user.FullName)

		return connect.NewResponse(&usersv1.SignupWithEmailResponse{
			Success: true,
			Message: "Account created successfully",
		}), nil
	} else {
		if err != nil {
			return nil, err
		}
		return nil, fmt.Errorf("email already exists")
	}
}

func (a *AuthHandlerService) SendForgotPasswordEmail(ctx context.Context, req *connect.Request[usersv1.SendForgotPasswordEmailRequest]) (*connect.Response[usersv1.SendForgotPasswordEmailResponse], error) {
	user, err := a.userRepo.GetUserByEmail(context.Background(), &req.Msg.Email)

	if err == nil {
		otp, err := a.CreateVerificationCode(&user.ID, &req.Msg.Email, verificationcodesqueries.VerificationCodeTypePasswordReset)

		if err != nil {
			return nil, err
		}

		// TODO: ADD TO QUEUE
		mail.ForgotPasswordEmail(ctx, req.Msg.Email, otp)
		return connect.NewResponse(&usersv1.SendForgotPasswordEmailResponse{
			Success: true,
			Message: "Email sent successfully",
		}), nil
	} else {
		if helpers.IsNoRowsError(err) {
			return nil, fmt.Errorf("email doesn't exists")
		}
		return nil, err
	}
}

func (a *AuthHandlerService) ResetPassword(ctx context.Context, req *connect.Request[usersv1.ResetPasswordRequest]) (*connect.Response[usersv1.ResetPasswordResponse], error) {
	user, err := a.userRepo.GetUserByEmail(context.Background(), &req.Msg.Email)
	if err != nil {
		return nil, err
	}
	err = a.VerifyVerificationCode(req.Msg.VerificationCode, &user.ID, &req.Msg.Email, verificationcodesqueries.VerificationCodeTypePasswordReset)
	if err != nil {
		return nil, err
	}

	hashedPassword, err := hasher.Hash(req.Msg.NewPassword)

	if err != nil {
		return nil, err
	}

	userAccount, err := a.accountsRepo.GetAccountByEmail(context.Background(), &req.Msg.Email)
	if err != nil {
		if helpers.IsNoRowsError(err) {
			lastThreePasswords := users.LastThreePasswords{}
			lastThreePasswords.AddPassword(hashedPassword)
			_, err := a.accountsRepo.CreateAccount(context.Background(), accountsqueries.CreateAccountParams{
				ProviderID:        "email",
				UserID:            user.ID,
				Password:          &hashedPassword,
				LastThreePassword: lastThreePasswords.ToByte(),
			})
			if err != nil {
				return nil, err
			}

			return connect.NewResponse(&usersv1.ResetPasswordResponse{
				Success: true,
				Message: "Password reset successfully",
			}), nil
		} else {
			return nil, err
		}
	}

	if userAccount.Account.ID == 0 {
		return nil, fmt.Errorf("account not found")
	}

	lastThreePasswords := users.LastThreePasswordsFromByte(userAccount.Account.LastThreePassword)

	if lastThreePasswords.CheckIsOnLastThree(req.Msg.NewPassword) {
		// TODO: Need to think of a way to not invalidate the token in this case. Maybe pass transaction to the function VerifyVerificationCode
		return nil, fmt.Errorf("password is already used in the last three passwords")
	}

	lastThreePasswords.AddPassword(hashedPassword)

	err = a.accountsRepo.UpdateAccountPassword(context.Background(), accountsqueries.UpdateAccountPasswordParams{
		ID:                userAccount.Account.ID,
		Password:          &hashedPassword,
		LastThreePassword: lastThreePasswords.ToByte(),
		UserID:            user.ID,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&usersv1.ResetPasswordResponse{
		Success: true,
		Message: "Password reset successfully",
	}), nil

}

func (a *AuthHandlerService) ChangePassword(ctx context.Context, req *connect.Request[usersv1.ChangePasswordRequest]) (*connect.Response[usersv1.ChangePasswordResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	user, err := a.userRepo.GetUserById(ctx, sessionUser.ID)

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	userAccount, err := a.accountsRepo.GetAccountByEmail(context.Background(), user.Email)

	if err != nil {
		return nil, err
	}

	if !hasher.CompareHash(*userAccount.Account.Password, req.Msg.OldPassword) {
		return nil, validation.NewFieldError("oldPassword", fmt.Errorf("old password don't match"))

	}

	lastThreePasswords := users.LastThreePasswordsFromByte(userAccount.Account.LastThreePassword)

	if lastThreePasswords.CheckIsOnLastThree(req.Msg.NewPassword) {
		return nil, fmt.Errorf("password is already used in the last three passwords")
	}

	hashedPassword, err := hasher.Hash(req.Msg.NewPassword)

	if err != nil {
		return nil, err
	}

	lastThreePasswords.AddPassword(hashedPassword)

	err = a.accountsRepo.UpdateAccountPassword(context.Background(), accountsqueries.UpdateAccountPasswordParams{
		ID:                userAccount.Account.ID,
		Password:          &hashedPassword,
		LastThreePassword: lastThreePasswords.ToByte(),
		UserID:            user.ID,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&usersv1.ChangePasswordResponse{
		Success: true,
		Message: "Password changed successfully",
	}), nil
}

func (a *AuthHandlerService) ChangeEmailVerification(ctx context.Context, req *connect.Request[usersv1.ChangeEmailVerificationRequest]) (*connect.Response[usersv1.ChangeEmailVerificationResponse], error) {
	currentUser := web.GetUserFromContext(ctx)

	user, err := a.userRepo.GetUserByEmail(context.Background(), currentUser.Email)

	if err != nil {
		return nil, err
	}

	otp, err := a.CreateVerificationCode(&user.ID, user.Email, verificationcodesqueries.VerificationCodeTypeChangeEmailRequest)

	if err != nil {
		return nil, err
	}

	_, err = jwt.CreateVerificationCodeJwt(jwt.EmailChangeRequestVerification, *user.Email, nil)

	if err != nil {
		return nil, err
	}

	// TODO: ADD TO QUEUE
	mail.SendChangeEmailSend(ctx, *user.Email, otp)

	return connect.NewResponse(&usersv1.ChangeEmailVerificationResponse{
		Success: true,
		Message: "verification code has been sent to your email",
	}), nil

}

func (a *AuthHandlerService) VerifyChangeEmail(ctx context.Context, req *connect.Request[usersv1.VerifyChangeEmailRequest]) (*connect.Response[usersv1.VerifyChangeEmailResponse], error) {
	user := web.GetUserFromContext(ctx)
	err := a.VerifyVerificationCode(req.Msg.VerificationCode, &user.ID, user.Email, verificationcodesqueries.VerificationCodeTypeChangeEmailRequest)
	if err != nil {
		return nil, err
	}

	token, err := jwt.CreateVerificationCodeJwt(jwt.EmailChangeRequestVerification, *user.Email, nil)

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&usersv1.VerifyChangeEmailResponse{
		Token:   token,
		Success: true,
	}), nil

}

func (a *AuthHandlerService) SendVerifyNewEmail(ctx context.Context, req *connect.Request[usersv1.SendVerifyNewEmailRequest]) (*connect.Response[usersv1.SendVerifyNewEmailResponse], error) {
	token, err := jwt.VerifyVerificationToken(req.Msg.Token)
	if err != nil {
		return nil, err
	}

	if token.Type != jwt.EmailChangeRequestVerification {
		return nil, fmt.Errorf("invalid verification type")
	}

	user, err := a.userRepo.GetUserByEmail(context.Background(), &token.Email)
	if err != nil {
		return nil, err
	}
	otp, err := a.CreateVerificationCode(&user.ID, &req.Msg.NewEmail, verificationcodesqueries.VerificationCodeTypeChangeEmail)
	if err != nil {
		return nil, err
	}

	verifyJwt, err := jwt.CreateVerificationCodeJwt(jwt.EmailChangeVerification, req.Msg.NewEmail, &token.Email)

	if err != nil {
		return nil, err
	}
	// TODO: ADD TO QUEUE
	go mail.SendMail(mail.Mail{
		To:      []string{req.Msg.NewEmail},
		Subject: string(verificationcodesqueries.VerificationCodeTypeChangeEmail),
		Body:    []byte("Your email change verification code is: " + otp + "\n" + verifyJwt),
	})

	return connect.NewResponse(&usersv1.SendVerifyNewEmailResponse{
		Success: true,
		Message: "verification code sent to new email",
	}), nil

}

func (a *AuthHandlerService) VerifyNewEmail(ctx context.Context, req *connect.Request[usersv1.VerifyNewEmailRequest]) (*connect.Response[usersv1.VerifyNewEmailResponse], error) {
	if req.Msg.Token != nil {
		claims, err := jwt.VerifyVerificationToken(*req.Msg.Token)

		if err != nil {
			return nil, err
		}

		if claims.Type != jwt.EmailChangeVerification {
			return nil, fmt.Errorf("invalid verification type")
		}

		if claims.OldEmail == nil {
			return nil, fmt.Errorf("old email is required")
		}

		user, err := a.userRepo.GetUserByEmail(context.Background(), claims.OldEmail)

		if err != nil {
			return nil, err
		}

		doesEmailExist, err := a.userRepo.GetUserByEmail(context.Background(), &claims.Email)

		if err == nil && doesEmailExist.ID != 0 {
			return nil, fmt.Errorf("email already exists")
		}

		_, err = a.userRepo.UpdateUserEmail(context.Background(), usersqueries.UpdateUserEmailParams{
			ID:            user.ID,
			Email:         &claims.Email,
			EmailVerified: true,
		})

		if err != nil {
			return nil, err
		}

		return connect.NewResponse(&usersv1.VerifyNewEmailResponse{
			Success: true,
			Message: "Email changed successfully",
		}), nil
	}

	if req.Msg.VerifyNewEmailWithCode != nil {
		user := web.GetUserFromContext(ctx)
		if user == nil {
			return nil, fmt.Errorf("user not found")
		}

		err := a.VerifyVerificationCode(req.Msg.VerifyNewEmailWithCode.VerificationCode, &user.ID, &req.Msg.VerifyNewEmailWithCode.NewEmail, verificationcodesqueries.VerificationCodeTypeChangeEmail)

		if err != nil {
			return nil, err
		}
		_, err = a.userRepo.UpdateUserEmail(context.Background(), usersqueries.UpdateUserEmailParams{
			ID:            user.ID,
			Email:         &req.Msg.VerifyNewEmailWithCode.NewEmail,
			EmailVerified: true,
		})

		if err != nil {
			return nil, err
		}

		return connect.NewResponse(&usersv1.VerifyNewEmailResponse{
			Success: true,
			Message: "Email changed successfully",
		}), nil

	}

	return nil, errors.New("invalid request")
}

func (a *AuthHandlerService) UpdateUserDetails(ctx context.Context, req *connect.Request[usersv1.UpdateUserDetailsRequest]) (*connect.Response[usersv1.UpdateUserDetailsResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	user, err := a.userRepo.GetUserById(ctx, sessionUser.ID)

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	dob := req.Msg.DateOfBirth.AsTime()

	_, err = a.userRepo.UpdateUserDetails(context.Background(), usersqueries.UpdateUserDetailsParams{
		ID:          user.ID,
		FullName:    req.Msg.FullName,
		DateOfBirth: &dob,
	})

	if err != nil {
		return nil, connect.NewError(connect.CodeInternal, err)
	}

	return connect.NewResponse(&usersv1.UpdateUserDetailsResponse{
		Success: true,
		Message: "profile udpated successfully",
	}), nil
}

func (a *AuthHandlerService) UpdateUserImage(ctx context.Context, req *connect.Request[usersv1.UpdateUserImageRequest]) (*connect.Response[usersv1.UpdateUserImageResponse], error) {
	newFileName, err := a.storageService.ValidateAndUploadFromTemp(ctx, &req.Msg.Image, storagev1.ImageFileType, "user-image", true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	image, err := a.userRepo.UpdateUserImage(ctx, &newFileName)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&usersv1.UpdateUserImageResponse{
		Success: true,
		Message: *helpers.GetCdnLinkPointer(image),
	}), nil
}

func (a *AuthHandlerService) ListSessions(ctx context.Context, req *connect.Request[usersv1.ListSessionsRequest]) (*connect.Response[usersv1.ListSessionsResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) RevokeSessions(ctx context.Context, req *connect.Request[usersv1.RevokeSessionsRequest]) (*connect.Response[usersv1.RevokeSessionsResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) RevokeOtherSessions(ctx context.Context, req *connect.Request[usersv1.RevokeOtherSessionsRequest]) (*connect.Response[usersv1.RevokeOtherSessionsResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) LinkSocial(ctx context.Context, req *connect.Request[usersv1.LinkSocialRequest]) (*connect.Response[usersv1.LinkSocialResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) ListAccounts(ctx context.Context, req *connect.Request[usersv1.ListAccountsRequest]) (*connect.Response[usersv1.ListAccountsResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) UnlinkAccount(ctx context.Context, req *connect.Request[usersv1.UnlinkAccountRequest]) (*connect.Response[usersv1.UnlinkAccountResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) SignInWithSocial(ctx context.Context, req *connect.Request[usersv1.SignInWithSocialRequest]) (*connect.Response[usersv1.SignInWithSocialResponse], error) {
	panic("TODO: Implement")
}

func (a *AuthHandlerService) CreateVerificationCode(userId *int64, email *string, verificationType verificationcodesqueries.VerificationCodeType) (string, error) {
	otp, hash, err := hasher.GenerateOTPHash(6)

	if err != nil {
		return "", fmt.Errorf("failed to generate OTP: %w", err)
	}

	var previousVerificationCode verificationcodesqueries.VerificationCode
	if verificationType == verificationcodesqueries.VerificationCodeTypeChangeEmail {

		previousVerificationCode, err = a.verificationRepo.GetEmailChangeVerificationCode(context.Background(), verificationcodesqueries.GetEmailChangeVerificationCodeParams{
			Type:   verificationType,
			UserID: userId,
		})
	} else {
		previousVerificationCode, err = a.verificationRepo.GetVerificationCode(context.Background(), verificationcodesqueries.GetVerificationCodeParams{
			Type:   verificationType,
			Email:  email,
			UserID: userId,
		})
	}

	if previousVerificationCode.ID != 0 {
		sentTime := time.Since(previousVerificationCode.UpdatedAt)

		if sentTime < 120*time.Second {
			return "", fmt.Errorf("verification code already sent, please wait for 2 minutes before requesting a new one")
		}

		err = a.verificationRepo.UpdateVerificationCode(context.Background(), verificationcodesqueries.UpdateVerificationCodeParams{
			ID:        previousVerificationCode.ID,
			Code:      hash,
			ExpiresAt: time.Now().Add(15 * time.Minute),
		})

		if err != nil {
			return "", fmt.Errorf("failed to update verification code: %w", err)
		}

	}

	if err != nil {

		if helpers.IsNoRowsError(err) {
			_, err = a.verificationRepo.CreateVerificationCode(context.Background(), verificationcodesqueries.CreateVerificationCodeParams{
				UserID:    userId,
				Code:      hash,
				Email:     email,
				Type:      verificationType,
				ExpiresAt: time.Now().Add(15 * time.Minute),
			})
			if err != nil {
				return "", err
			}
		} else {
			return "", err
		}

	}

	return otp, nil
}

func (a *AuthHandlerService) VerifyVerificationCode(code string, userId *int64, email *string, verificationType verificationcodesqueries.VerificationCodeType) error {

	verificationCode, err := a.verificationRepo.GetVerificationCode(context.Background(), verificationcodesqueries.GetVerificationCodeParams{
		Type:   verificationType,
		Email:  email,
		UserID: userId,
	})

	if err != nil {
		return fmt.Errorf("failed to get verification code: %w", err)
	}

	if verificationCode.ID == 0 {
		return fmt.Errorf("verification code not found")
	}

	if time.Now().After(verificationCode.ExpiresAt) {
		return fmt.Errorf("verification code expired")
	}

	if !hasher.CompareHash(verificationCode.Code, code) {
		return fmt.Errorf("invalid verification code")
	}

	err = a.verificationRepo.DeleteVerificationCode(context.Background(), verificationCode.ID)
	if err != nil {
		return fmt.Errorf("failed to delete verification code: %w", err)
	}
	return nil

}
