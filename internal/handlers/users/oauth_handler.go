package users

import (
	"context"
	"github.com/nsp-inc/vtuber/packages/mail"
	"net/http"

	"github.com/markbates/goth"
	"github.com/markbates/goth/gothic"
	"github.com/markbates/goth/providers/google"
	accountsqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/accounts"
	usersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/users"
	vtuberprofilesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberprofiles"
	"github.com/nsp-inc/vtuber/packages/env"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/jwt"
	"github.com/nsp-inc/vtuber/packages/oauth/twitterv2"
)

type ProviderIndex struct {
	Providers    []string          `json:"providers"`
	ProvidersMap map[string]string `json:"providers_map"`
}

type OauthHandler struct {
	userRepo          *usersqueries.Queries
	accountsRepo      *accountsqueries.Queries
	vtuberProfileRepo *vtuberprofilesqueries.Queries
}

func NewOauthHandler(userRepo *usersqueries.Queries, accountsRepo *accountsqueries.Queries, vtuberProfileRepo *vtuberprofilesqueries.Queries) *OauthHandler {
	return &OauthHandler{
		userRepo:          userRepo,
		accountsRepo:      accountsRepo,
		vtuberProfileRepo: vtuberProfileRepo,
	}
}

func init() {
	goth.UseProviders(
		google.New(env.GetString("GOOGLE_CLIENT_KEY", ""), env.GetString("GOOGLE_CLIENT_SECRET", ""), env.GetString("GOOGLE_REDIRECT_URL", ""), "email", "profile"),
		twitterv2.New(env.GetString("TWITTER_CLIENT_KEY", ""), env.GetString("TWITTER_CLIENT_SECRET", ""), env.GetString("TWITTER_REDIRECT_URL", "")),
		// facebook.New(os.Getenv("FACEBOOK_KEY"), os.Getenv("FACEBOOK_SECRET"), "http://localhost:3000/auth/facebook/callback"),
	)
}

func (h *OauthHandler) HandleOauthRoutes(mux *http.ServeMux) {
	mux.HandleFunc("/oauth/v1/{provider}/signin", func(w http.ResponseWriter, r *http.Request) {
		gothic.BeginAuthHandler(w, r)
	})

	mux.HandleFunc("/oauth/v1/{provider}/callback", func(w http.ResponseWriter, r *http.Request) {

		oauthuser, err := gothic.CompleteUserAuth(w, r)
		if err != nil {
			http.Error(w, "Authentication failed", http.StatusInternalServerError)
			return
		}

		provider := oauthuser.Provider
		accessToken := oauthuser.AccessToken
		refreshToken := oauthuser.RefreshToken
		idToken := oauthuser.IDToken
		if provider == "twitterv2" {
			provider = "twitter"
			refreshToken = oauthuser.AccessTokenSecret
		}

		var image *string
		if oauthuser.AvatarURL != "" {
			image = &oauthuser.AvatarURL
		}

		existingAccount, err := h.accountsRepo.GetAccountByProviderAndAccountID(context.Background(), accountsqueries.GetAccountByProviderAndAccountIDParams{
			ProviderID: provider,
			AccountID:  oauthuser.UserID,
		})
		if err != nil {
			if helpers.IsNoRowsError(err) {
				var user *usersqueries.User
				var vtuberId *int64

				if oauthuser.Email != "" {
					// Check if the user already exists by email
					existingUser, err := h.userRepo.GetUserByEmail(context.Background(), &oauthuser.Email)
					if err != nil {
						if !helpers.IsNoRowsError(err) {
							http.Error(w, "Could not fetch user", http.StatusInternalServerError)
							return
						}
					} else {
						// User already exists, we can create the account
						user = &existingUser

						vtuber, _ := h.vtuberProfileRepo.GetVtuberProfileByUserID(context.Background(), existingUser.ID)
						if vtuber.ID != 0 {
							vtuberId = &vtuber.ID
						}
					}
				}

				if user == nil {
					newUser, err := h.userRepo.CreateUser(context.Background(), usersqueries.CreateUserParams{
						Email:         &oauthuser.Email,
						EmailVerified: true,
						FullName:      oauthuser.Name,
						Image:         image,
						Role:          usersqueries.UserRoleUser,
					})
					if err != nil {
						http.Error(w, "Could not create user", http.StatusInternalServerError)
						return
					}

					user = &newUser

					mail.SendMemberRegisterMail(context.Background(), user.Email, user.FullName)
				}

				if user == nil {
					http.Error(w, "Could not create user", http.StatusInternalServerError)
					return
				}

				// Successfully created the user, we can create the account
				_, err = h.accountsRepo.CreateAccount(context.Background(), accountsqueries.CreateAccountParams{
					AccountID:          oauthuser.UserID,
					ProviderID:         provider,
					UserID:             user.ID,
					AccessToken:        &accessToken,
					RefreshToken:       &refreshToken,
					IDToken:            &idToken,
					AccessTokenExpires: &oauthuser.ExpiresAt,
				})

				if err != nil {
					http.Error(w, "Could not create account", http.StatusInternalServerError)
					return
				}

				// Successfully created the account, we can log the user in
				tokens, err := jwt.CreateJwt(*user, vtuberId)

				if err != nil {
					http.Error(w, "Could not login", http.StatusInternalServerError)
					return
				}

				http.Redirect(w, r, getClientRedirectURL(tokens.AccessToken, tokens.RefreshToken), http.StatusSeeOther)

			}
		} else {

			// update the existing account with the new tokens
			_, err = h.accountsRepo.UpdateAccountTokens(context.Background(), accountsqueries.UpdateAccountTokensParams{
				AccessToken:        &accessToken,
				RefreshToken:       &refreshToken,
				IDToken:            &idToken,
				AccessTokenExpires: &oauthuser.ExpiresAt,
				ID:                 existingAccount.Account.ID,
				ProviderID:         provider,
				UserID:             existingAccount.User.ID,
			})

			if err != nil {
				http.Error(w, "Could not update account tokens", http.StatusInternalServerError)
				return
			}

			var vtuberId *int64
			vtuber, _ := h.vtuberProfileRepo.GetVtuberProfileByUserID(context.Background(), existingAccount.User.ID)

			if vtuber.ID != 0 {
				vtuberId = &vtuber.ID
			}

			tokens, err := jwt.CreateJwt(usersqueries.User{
				ID:            existingAccount.User.ID,
				FullName:      existingAccount.User.FullName,
				Email:         existingAccount.User.Email,
				DateOfBirth:   existingAccount.User.DateOfBirth,
				Image:         existingAccount.User.Image,
				EmailVerified: existingAccount.User.EmailVerified,
				Role:          usersqueries.UserRole(existingAccount.User.Role),
				IsBanned:      existingAccount.User.IsBanned,
				BanReason:     existingAccount.User.BanReason,
				BanExpiresAt:  existingAccount.User.BanExpiresAt,
				CreatedAt:     existingAccount.User.CreatedAt,
				UpdatedAt:     existingAccount.User.UpdatedAt,
				DeletedAt:     existingAccount.User.DeletedAt,
			}, vtuberId)

			if err != nil {
				http.Error(w, "Could not login", http.StatusInternalServerError)
				return
			}

			// Redirect the user to the frontend with the JWT token
			http.Redirect(w, r, getClientRedirectURL(tokens.AccessToken, tokens.RefreshToken), http.StatusSeeOther)
		}

	})
}

func getClientRedirectURL(accessToken, refreshToken string) string {
	redirectUrl := env.GetString("CLIENT_REDIRECT_URL", "http://localhost:3000/oauth-complete")

	return redirectUrl + "?token=" + accessToken + "&refreshToken=" + refreshToken

}
