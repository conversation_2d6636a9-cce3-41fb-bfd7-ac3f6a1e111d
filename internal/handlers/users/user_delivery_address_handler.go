package users

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	v1 "github.com/nsp-inc/vtuber/api/userdeliveryaddress/v1"
	userdeliveryaddressqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/userdeliveryaddress"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type DeliveryAddressService struct {
	repo *userdeliveryaddressqueries.Queries
}

func (d DeliveryAddressService) AddUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.AddUserDeliveryAddressRequest]) (*connect.Response[v1.AddUserDeliveryAddressResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)

	_, err := d.repo.GetUserDeliveryAddressByUserId(ctx, sessionUser.ID)
	if err == nil {
		return nil, errors.New("user already has a delivery address")
	}

	savedAddress, err := d.repo.AddUserDeliveryAddress(ctx, userdeliveryaddressqueries.AddUserDeliveryAddressParams{
		UserID:                sessionUser.ID,
		Recipient:             req.Msg.Recipient,
		PhoneNumber:           req.Msg.PhoneNumber,
		PostalCode:            req.Msg.PostalCode,
		Prefecture:            req.Msg.Prefecture,
		City:                  req.Msg.City,
		AddressLine1:          req.Msg.AddressLine1,
		AddressLine2:          req.Msg.AddressLine2,
		PreferredDeliveryTime: req.Msg.PreferredDeliveryTime,
		PreferredDeliveryDate: helpers.ConvertToTime(req.Msg.PreferredDeliveryDate),
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(
		&v1.AddUserDeliveryAddressResponse{
			Data: &v1.UserDeliveryAddress{
				Id:                    savedAddress.ID,
				Recipient:             savedAddress.Recipient,
				PhoneNumber:           savedAddress.PhoneNumber,
				PostalCode:            savedAddress.PostalCode,
				Prefecture:            savedAddress.Prefecture,
				City:                  savedAddress.City,
				AddressLine1:          savedAddress.AddressLine1,
				AddressLine2:          savedAddress.AddressLine2,
				PreferredDeliveryTime: savedAddress.PreferredDeliveryTime,
				PreferredDeliveryDate: helpers.ConvertToTimeStamp(savedAddress.PreferredDeliveryDate),
				CreatedAt:             timestamppb.New(savedAddress.CreatedAt),
			},
		},
	), nil

}

func (d DeliveryAddressService) GetCurrentUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.GetCurrentUsersDeliveryAddressRequest]) (*connect.Response[v1.GetCurrentUsersDeliveryAddressResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	address, err := d.repo.GetUserDeliveryAddressByUserId(ctx, sessionUser.ID)
	if err != nil && helpers.IsNoRowsError(err) {
		return nil, connect.NewError(connect.CodeNotFound, errors.New("delivery address not found for the current user"))
	}

	if err != nil {
		return nil, err

	}

	return connect.NewResponse(
		&v1.GetCurrentUsersDeliveryAddressResponse{
			Data: &v1.UserDeliveryAddress{
				Id:                    address.ID,
				Recipient:             address.Recipient,
				PhoneNumber:           address.PhoneNumber,
				PostalCode:            address.PostalCode,
				Prefecture:            address.Prefecture,
				City:                  address.City,
				AddressLine1:          address.AddressLine1,
				AddressLine2:          address.AddressLine2,
				PreferredDeliveryTime: address.PreferredDeliveryTime,
				PreferredDeliveryDate: helpers.ConvertToTimeStamp(address.PreferredDeliveryDate),
				CreatedAt:             timestamppb.New(address.CreatedAt),
			},
		},
	), nil
}

func (d DeliveryAddressService) GetUserDeliveryAddressById(ctx context.Context, req *connect.Request[v1.GetUserDeliveryAddressRequest]) (*connect.Response[v1.GetUserDeliveryAddressResponse], error) {
	address, err := d.repo.GetUserDeliveryAddressByUserId(ctx, req.Msg.Id)

	if err != nil && helpers.IsNoRowsError(err) {
		return nil, connect.NewError(connect.CodeNotFound, errors.New("delivery address not found for this user"))
	}

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(
		&v1.GetUserDeliveryAddressResponse{
			Data: &v1.UserDeliveryAddress{
				Id:                    address.ID,
				Recipient:             address.Recipient,
				PhoneNumber:           address.PhoneNumber,
				PostalCode:            address.PostalCode,
				Prefecture:            address.Prefecture,
				City:                  address.City,
				AddressLine1:          address.AddressLine1,
				AddressLine2:          address.AddressLine2,
				PreferredDeliveryTime: address.PreferredDeliveryTime,
				PreferredDeliveryDate: helpers.ConvertToTimeStamp(address.PreferredDeliveryDate),
				CreatedAt:             timestamppb.New(address.CreatedAt),
			},
		},
	), nil
}

func (d DeliveryAddressService) DeleteUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.DeleteUserDeliveryAddressRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	byId, err := d.repo.GetUserDeliveryAddressById(ctx, req.Msg.Id)

	if err != nil {
		return nil, err
	}

	if byId.UserID != web.GetUserFromContext(ctx).ID {
		return nil, errors.New("unauthorized to delete this address")
	}

	err = d.repo.DeleteUserDeliveryAddress(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(
		&sharedv1.GenericResponse{
			Status:  200,
			Message: "Delivery address deleted successfully",
			Success: true,
		},
	), nil

}

func (d DeliveryAddressService) UpdateUserDeliveryAddress(ctx context.Context, req *connect.Request[v1.UpdateUserDeliveryAddressRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	byId, err := d.repo.GetUserDeliveryAddressById(ctx, req.Msg.Id)

	if err != nil {
		return nil, err
	}

	if byId.UserID != web.GetUserFromContext(ctx).ID {
		return nil, errors.New("unauthorized to update this address")
	}

	err = d.repo.UpdateUserDeliveryAddress(ctx, userdeliveryaddressqueries.UpdateUserDeliveryAddressParams{
		Recipient:             req.Msg.Recipient,
		PhoneNumber:           req.Msg.PhoneNumber,
		PostalCode:            req.Msg.PostalCode,
		Prefecture:            req.Msg.Prefecture,
		City:                  req.Msg.City,
		AddressLine1:          req.Msg.AddressLine1,
		AddressLine2:          req.Msg.AddressLine2,
		PreferredDeliveryTime: req.Msg.PreferredDeliveryTime,
		PreferredDeliveryDate: helpers.ConvertToTime(req.Msg.PreferredDeliveryDate),
		ID:                    req.Msg.Id,
	})

	if err != nil {
		return nil, err
	}

	return connect.NewResponse(
		&sharedv1.GenericResponse{
			Status:  200,
			Message: "Delivery address updated successfully",
			Success: true,
		},
	), nil

}

func NewDeliveryAddressService(userDeliveryAddressRepo *userdeliveryaddressqueries.Queries) *DeliveryAddressService {
	return &DeliveryAddressService{
		repo: userDeliveryAddressRepo,
	}
}
