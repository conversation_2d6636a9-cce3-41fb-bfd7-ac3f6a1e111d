package vtubers

import (
	"context"
	"strconv"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	vtubergalleriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtubergalleries"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

type VtuberGalleryService struct {
	repo           *vtubergalleriesqueries.Queries
	storageService *storagev1.StorageService
}

func NewVtuberGalleryService(repo *vtubergalleriesqueries.Queries, storageService *storagev1.StorageService) *VtuberGalleryService {
	return &VtuberGalleryService{
		repo:           repo,
		storageService: storageService,
	}
}

func (v *VtuberGalleryService) AddVtuberGallery(ctx context.Context, c *connect.Request[vtubersv1.AddVtuberGalleryRequest]) (*connect.Response[vtubersv1.AddVtuberGalleryResponse], error) {
	var vtuberId = web.GetUserFromContext(ctx).VtuberId
	mediaType := storagev1.ImageFileType
	if c.Msg.MediaType == "video" {
		mediaType = storagev1.VideoFileType
	}
	var uploadPath = "vtuber-gallery" + "/" + strconv.FormatInt(*vtuberId, 10)
	newPath, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Media, mediaType, uploadPath, true, nil)
	if err != nil {
		return nil, validation.NewFieldError("media", err)
	}

	var galleryMediaType = vtubergalleriesqueries.MediaTypePicture
	if c.Msg.MediaType == "video" {
		galleryMediaType = vtubergalleriesqueries.MediaTypeVideo
	}

	gallery, err := v.repo.AddVtuberGallery(ctx, vtubergalleriesqueries.AddVtuberGalleryParams{
		VtuberID:    *vtuberId,
		Media:       newPath,
		MediaType:   galleryMediaType,
		Description: c.Msg.Description,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.AddVtuberGalleryResponse{
		Data: &vtubersv1.VtuberGallery{
			Id:          gallery.ID,
			VtuberId:    gallery.VtuberID,
			Media:       helpers.GetCdnUrl(gallery.Media),
			MediaType:   string(gallery.MediaType),
			Description: gallery.Description,
			CreatedAt:   timestamppb.New(gallery.CreatedAt),
			UpdatedAt:   timestamppb.New(gallery.UpdatedAt),
		},
	}), nil

}

func (v *VtuberGalleryService) GetVtuberGalleryById(p0 context.Context, p1 *connect.Request[vtubersv1.GetVtuberGalleryByIdRequest]) (*connect.Response[vtubersv1.GetVtuberGalleryByIdResponse], error) {

	gallery, err := v.repo.GetVtuberGalleryById(p0, p1.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.GetVtuberGalleryByIdResponse{
		Data: &vtubersv1.VtuberGallery{
			Id:          gallery.ID,
			VtuberId:    gallery.VtuberID,
			Media:       helpers.GetCdnUrl(gallery.Media),
			MediaType:   string(gallery.MediaType),
			Description: gallery.Description,
			CreatedAt:   timestamppb.New(gallery.CreatedAt),
			UpdatedAt:   timestamppb.New(gallery.UpdatedAt),
		},
	}), nil
}

func (v *VtuberGalleryService) GetVtuberGalleries(p0 context.Context, p1 *connect.Request[vtubersv1.GetVtuberGalleriesRequest]) (*connect.Response[vtubersv1.GetVtuberGalleriesResponse], error) {
	paginationInfo := sharedv1.GetPaginationRequestInfo(p1.Msg.Pagination, []string{"created_at"})
	galleries, err := v.repo.GetVtuberGalleries(p0, vtubergalleriesqueries.GetVtuberGalleriesParams{
		Limit:    paginationInfo.PageSize,
		Offset:   paginationInfo.Offset,
		Order:    paginationInfo.OrderDirection,
		Sort:     paginationInfo.OrderBy,
		VtuberID: p1.Msg.VtuberId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.GetVtuberGalleriesResponse{
		Data: helpers.Map(galleries, func(gallery vtubergalleriesqueries.GetVtuberGalleriesRow) *vtubersv1.VtuberGallery {
			return &vtubersv1.VtuberGallery{
				Id:          gallery.ID,
				VtuberId:    gallery.VtuberID,
				Media:       helpers.GetCdnUrl(gallery.Media),
				MediaType:   string(gallery.MediaType),
				Description: gallery.Description,
				CreatedAt:   timestamppb.New(gallery.CreatedAt),
				UpdatedAt:   timestamppb.New(gallery.UpdatedAt),
			}
		}),
		PaginationDetails: sharedv1.GetPaginationResponseInfo(paginationInfo, helpers.GetFirstElement(galleries)),
	}), nil
}

func (v *VtuberGalleryService) DeleteVtuberGalleryById(p0 context.Context, p1 *connect.Request[vtubersv1.DeleteVtuberGalleryByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {

	err := v.repo.DeleteVtuberGalleryById(p0, p1.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Success: true,
		Status:  200,
		Message: "Gallery deleted",
	}), nil
}

func (v *VtuberGalleryService) UpdateVtuberGalleryById(p0 context.Context, p1 *connect.Request[vtubersv1.UpdateVtuberGalleryByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	gallery, err := v.repo.GetVtuberGalleryById(p0, p1.Msg.Id)
	if err != nil {
		return nil, err
	}

	mediaType := storagev1.ImageFileType

	if p1.Msg.MediaType == "video" {
		mediaType = storagev1.VideoFileType
	}

	var uploadPath = "vtuber-gallery" + "/" + strconv.FormatInt(gallery.VtuberID, 10)
	newPath, err := v.storageService.ValidateAndUploadFromTemp(p0, &p1.Msg.Media, mediaType, uploadPath, false, &gallery.Media)
	if err != nil {
		return nil, validation.NewFieldError("media", err)
	}

	var galleryMediaType = vtubergalleriesqueries.MediaTypePicture
	if p1.Msg.MediaType == "video" {
		galleryMediaType = vtubergalleriesqueries.MediaTypeVideo
	}

	err = v.repo.UpdateVtuberGalleryById(p0, vtubergalleriesqueries.UpdateVtuberGalleryByIdParams{
		ID:          p1.Msg.Id,
		Media:       newPath,
		MediaType:   galleryMediaType,
		Description: p1.Msg.Description,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Success: true,
		Status:  200,
		Message: "Gallery updated",
	}), nil
}
