package vtubers

import (
	"context"
	"errors"

	"connectrpc.com/connect"
	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	vtuberplansqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberplans"

	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/web"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

type VtuberPlanService struct {
	repo *vtuberplansqueries.Queries
}

func NewVtuberPlanService(vtuberPlanRepo *vtuberplansqueries.Queries) *VtuberPlanService {
	return &VtuberPlanService{
		repo: vtuberPlanRepo,
	}
}

func (c VtuberPlanService) AddVtuberPlan(ctx context.Context, req *connect.Request[vtubersv1.AddVtuberPlanRequest]) (*connect.Response[vtubersv1.AddVtuberPlanResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	_, err := c.repo.GetVtuberPlanByIndexAndVtuberId(ctx, vtuberplansqueries.GetVtuberPlanByIndexAndVtuberIdParams{
		Index:    req.Msg.Index,
		VtuberID: *sessionUser.VtuberId,
	})
	if err == nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "creatorSubscriptionIndexExists", nil),
		)

	}
	count, err := c.repo.CountVtuberPlans(ctx, *sessionUser.VtuberId)
	if err != nil {
		return nil, err
	}

	if count >= 3 {
		return nil, errors.New("cannot add more than 3 plans")
	}

	vtuberPlan, err := c.repo.CreateVtuberPlan(ctx, vtuberplansqueries.CreateVtuberPlanParams{
		VtuberID:         *sessionUser.VtuberId,
		Description:      req.Msg.Description,
		Title:            req.Msg.Title,
		Price:            req.Msg.Price,
		Index:            req.Msg.Index,
		ShortDescription: req.Msg.ShortDescription,
		AnnualPrice:      req.Msg.AnnualPrice,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.AddVtuberPlanResponse{
		Data: &vtubersv1.VtuberPlan{
			Id:               vtuberPlan.ID,
			VtuberId:         vtuberPlan.VtuberID,
			Description:      vtuberPlan.Description,
			Title:            vtuberPlan.Title,
			Price:            vtuberPlan.Price,
			Index:            vtuberPlan.Index,
			CreatedAt:        timestamppb.New(vtuberPlan.CreatedAt),
			ShortDescription: vtuberPlan.ShortDescription,
			AnnualPrice:      vtuberPlan.AnnualPrice,
		},
	}), nil
}

func (c VtuberPlanService) GetVtuberPlanById(ctx context.Context, req *connect.Request[vtubersv1.GetVtuberPlanByIdRequest]) (*connect.Response[vtubersv1.GetVtuberPlanByIdResponse], error) {
	user := web.GetUserFromContext(ctx)
	var userId *int64
	if user.VtuberId != nil {
		userId = &user.ID
	}
	vtuberPlan, err := c.repo.GetVtuberPlanById(ctx, vtuberplansqueries.GetVtuberPlanByIdParams{
		ID:     req.Msg.Id,
		UserID: userId,
	})
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "creatorSub", nil),
		}))
	}
	return connect.NewResponse(&vtubersv1.GetVtuberPlanByIdResponse{
		Data: &vtubersv1.VtuberPlan{
			Id:               vtuberPlan.ID,
			VtuberId:         vtuberPlan.VtuberID,
			Description:      vtuberPlan.Description,
			Title:            vtuberPlan.Title,
			Price:            vtuberPlan.Price,
			Index:            vtuberPlan.Index,
			CreatedAt:        timestamppb.New(vtuberPlan.CreatedAt),
			IsSubscribed:     vtuberPlan.Subscribed,
			ShortDescription: vtuberPlan.ShortDescription,
			AnnualPrice:      vtuberPlan.AnnualPrice,
		},
	}), nil
}

func (c VtuberPlanService) DeleteVtuberPlanById(ctx context.Context, req *connect.Request[vtubersv1.DeleteVtuberPlanByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	sub, err := c.repo.FindVtuberPlanById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "creatorSub", nil),
		}))
	}
	if *sessionUser.VtuberId != sub.VtuberID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedCreatorSubDelete", nil))
	}
	err = c.repo.DeleteVtuberPlan(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "creatorSubDeleted", nil),
		Success: true,
	}), nil
}

func (c VtuberPlanService) UpdateVtuberPlanById(ctx context.Context, req *connect.Request[vtubersv1.UpdateVtuberPlanByIdRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	sessionUser := web.GetUserFromContext(ctx)
	vtuberPlan, err := c.repo.FindVtuberPlanById(ctx, req.Msg.Id)
	if err != nil {
		return nil, errors.New(web.GetTranslation(ctx, "fieldNotFound", map[string]string{
			"field": web.GetTranslation(ctx, "creatorSub", nil),
		}))
	}

	if *sessionUser.VtuberId != vtuberPlan.VtuberID {
		return nil, errors.New(web.GetTranslation(ctx, "unauthorizedCreatorSubUpdate", nil))
	}

	if vtuberPlan.Index != req.Msg.Index {
		_, err = c.repo.GetVtuberPlanByIndexAndVtuberId(ctx, vtuberplansqueries.GetVtuberPlanByIndexAndVtuberIdParams{
			Index:    req.Msg.Index,
			VtuberID: *sessionUser.VtuberId,
		})
		if err == nil {
			return nil, errors.New(web.GetTranslation(ctx, "creatorSubscriptionIndexExists", nil))
		}
	}

	err = c.repo.UpdateVtuberPlan(ctx, vtuberplansqueries.UpdateVtuberPlanParams{
		ID:               req.Msg.Id,
		Description:      req.Msg.Description,
		Price:            req.Msg.Price,
		Title:            req.Msg.Title,
		Index:            req.Msg.Index,
		ShortDescription: req.Msg.ShortDescription,
		AnnualPrice:      req.Msg.AnnualPrice,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "creatorSubUpdated", nil),
		Success: true,
	}), nil
}

func (c *VtuberPlanService) GetAllVtuberPlansByVtuberId(ctx context.Context, req *connect.Request[vtubersv1.GetAllVtuberPlansByVtuberIdRequest]) (*connect.Response[vtubersv1.GetAllVtuberPlansResponse], error) {
	user := web.GetUserFromContext(ctx)
	var userId *int64
	if user != nil {
		userId = &user.ID
	}
	vtuberPlans, err := c.repo.GetVtuberPlansByVtuber(ctx, vtuberplansqueries.GetVtuberPlansByVtuberParams{
		VtuberID: req.Msg.VtuberId,
		UserID:   userId,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.GetAllVtuberPlansResponse{
		VtuberPlan: helpers.Map(vtuberPlans, func(vtuberPlan vtuberplansqueries.GetVtuberPlansByVtuberRow) *vtubersv1.VtuberPlan {
			return &vtubersv1.VtuberPlan{
				Id:               vtuberPlan.ID,
				VtuberId:         vtuberPlan.VtuberID,
				Description:      vtuberPlan.Description,
				Title:            vtuberPlan.Title,
				Price:            vtuberPlan.Price,
				Index:            vtuberPlan.Index,
				CreatedAt:        timestamppb.New(vtuberPlan.CreatedAt),
				IsSubscribed:     vtuberPlan.Subscribed,
				ShortDescription: vtuberPlan.ShortDescription,
				AnnualPrice:      vtuberPlan.AnnualPrice,
			}
		}),
	},
	), nil
}
