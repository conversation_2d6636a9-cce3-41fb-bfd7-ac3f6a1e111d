package vtubers

import (
	"context"
	"errors"
	"strconv"

	"connectrpc.com/connect"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	vtuberbannersqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtuberbanners"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/helpers"
	"github.com/nsp-inc/vtuber/packages/validation"
	"github.com/nsp-inc/vtuber/packages/web"
	"google.golang.org/protobuf/types/known/timestamppb"
)

type VtuberBannerService struct {
	repo           *vtuberbannersqueries.Queries
	storageService *storagev1.StorageService
}

func NewVtuberBannerService(repo *vtuberbannersqueries.Queries, storageService *storagev1.StorageService) *VtuberBannerService {
	return &VtuberBannerService{
		repo:           repo,
		storageService: storageService,
	}
}

func (v VtuberBannerService) AddVtuberBanner(ctx context.Context, c *connect.Request[vtubersv1.AddVtuberBannerRequest]) (*connect.Response[vtubersv1.AddVtuberBannerResponse], error) {
	var vtuberId = web.GetUserFromContext(ctx).VtuberId

	count, err := v.repo.CountVtuberBannerByVtuberId(ctx, *vtuberId)
	if err != nil {
		return nil, err
	}
	if count >= 5 {
		return nil, errors.New("cannot add more than 5 banners")
	}

	var distFolder = "vtuber-banner" + "/" + strconv.FormatInt(*vtuberId, 10)
	newFileName, err := v.storageService.ValidateAndUploadFromTemp(ctx, &c.Msg.Image, storagev1.ImageFileType, distFolder, true, nil)
	if err != nil {
		return nil, validation.NewFieldError("image", err)
	}

	banner, err := v.repo.AddVtuberBanner(ctx, vtuberbannersqueries.AddVtuberBannerParams{
		VtuberID: *vtuberId,
		Image:    newFileName,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.AddVtuberBannerResponse{
		Data: &vtubersv1.VtuberBanner{
			Id:        banner.ID,
			VtuberId:  banner.VtuberID,
			Image:     helpers.GetCdnUrl(banner.Image),
			CreatedAt: timestamppb.New(banner.CreatedAt),
			UpdatedAt: timestamppb.New(banner.UpdatedAt),
		},
	}), nil

}

func (v *VtuberBannerService) GetVtuberBannerByVtuberId(p0 context.Context, p1 *connect.Request[vtubersv1.GetVtuberBannerByVtuberIdRequest]) (*connect.Response[vtubersv1.GetVtuberBannerByVtuberIdResponse], error) {

	banners, err := v.repo.GetVtuberBannerByVtuberId(p0, p1.Msg.VtuberId)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.GetVtuberBannerByVtuberIdResponse{
		Data: helpers.Map(banners, func(banner vtuberbannersqueries.VtuberBanner) *vtubersv1.VtuberBanner {
			return &vtubersv1.VtuberBanner{
				Id:        banner.ID,
				VtuberId:  banner.VtuberID,
				Image:     helpers.GetCdnUrl(banner.Image),
				CreatedAt: timestamppb.New(banner.CreatedAt),
				UpdatedAt: timestamppb.New(banner.UpdatedAt),
			}
		}),
	}), nil
}

func (v *VtuberBannerService) DeleteVtuberBannerById(p0 context.Context, p1 *connect.Request[vtubersv1.DeleteVtuberBannerByIdRequest]) (*connect.Response[vtubersv1.DeleteVtuberBannerByIdResponse], error) {
	err := v.repo.DeleteVtuberBannerById(p0, p1.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.DeleteVtuberBannerByIdResponse{
		Success: true,
		Message: "Banner deleted",
	}), nil
}

func (v *VtuberBannerService) UpdateVtuberBannerById(p0 context.Context, p1 *connect.Request[vtubersv1.UpdateVtuberBannerByIdRequest]) (*connect.Response[vtubersv1.UpdateVtuberBannerByIdResponse], error) {
	err := v.repo.UpdateVtuberBannerById(p0, vtuberbannersqueries.UpdateVtuberBannerByIdParams{
		ID:    p1.Msg.Id,
		Image: p1.Msg.Image,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&vtubersv1.UpdateVtuberBannerByIdResponse{
		Success: true,
		Message: "Banner updated",
	}), nil
}
