package vtubers

import (
	"context"

	"connectrpc.com/connect"
	vtubersv1 "github.com/nsp-inc/vtuber/api/vtubers/v1"
	vtubercategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/vtubercategories"
	"github.com/nsp-inc/vtuber/packages/helpers"
)

type VtuberCategoryHandler struct {
	vtuberCategoriesRepo *vtubercategoriesqueries.Queries
}

func NewVtuberCategoryHandler(vtuberCategoriesRepo *vtubercategoriesqueries.Queries) *VtuberCategoryHandler {
	return &VtuberCategoryHandler{
		vtuberCategoriesRepo: vtuberCategoriesRepo,
	}
}

func (v VtuberCategoryHandler) GetAllVtuberCategories(ctx context.Context, c *connect.Request[vtubersv1.GetAllVtuberCategoriesRequest]) (*connect.Response[vtubersv1.GetVtuberCategoriesResponse], error) {
	categories, err := v.vtuberCategoriesRepo.GetAllVtuberCategories(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&vtubersv1.GetVtuberCategoriesResponse{
		Data: helpers.Map(categories, func(category int64) *vtubersv1.VtuberCategory {
			return &vtubersv1.VtuberCategory{
				Id: category,
			}
		}),
	}), nil
}
