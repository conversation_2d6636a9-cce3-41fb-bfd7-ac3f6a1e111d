package taxonomy

import (
	"context"
	"errors"
	"strconv"

	sharedv1 "github.com/nsp-inc/vtuber/api/shared/v1"
	taxonomyv1 "github.com/nsp-inc/vtuber/api/taxonomy/v1"
	"github.com/nsp-inc/vtuber/packages/web"

	categoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/categories"
	eventcategoriesqueries "github.com/nsp-inc/vtuber/internal/db/sqlc/tables/eventcategories"
	storagev1 "github.com/nsp-inc/vtuber/internal/storage"
	"github.com/nsp-inc/vtuber/packages/validation"

	"google.golang.org/protobuf/types/known/timestamppb"

	"connectrpc.com/connect"
	"github.com/nsp-inc/vtuber/packages/helpers"
)

type CategoryHandlerService struct {
	queries           *categoriesqueries.Queries
	storageService    *storagev1.StorageService
	eventCategoryRepo *eventcategoriesqueries.Queries
}

func NewCategoryHandlerService(categoryRepo *categoriesqueries.Queries, storageService *storagev1.StorageService, eventCategoryRepo *eventcategoriesqueries.Queries) *CategoryHandlerService {
	return &CategoryHandlerService{
		queries:           categoryRepo,
		storageService:    storageService,
		eventCategoryRepo: eventCategoryRepo,
	}
}

func (c *CategoryHandlerService) GetCategory(ctx context.Context, req *connect.Request[taxonomyv1.GetCategoryRequest]) (*connect.Response[taxonomyv1.GetCategoryResponse], error) {
	category, err := c.queries.GetCategoryById(ctx, strconv.FormatInt(req.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}),
		)
	}
	return connect.NewResponse(
		&taxonomyv1.GetCategoryResponse{
			Data: &taxonomyv1.Category{
				Id:          category.ID,
				Name:        category.Name,
				Description: *category.Description,
				CreatedAt:   timestamppb.New(category.CreatedAt),
				Slug:        category.Slug,
			},
		},
	), nil
}

func (c *CategoryHandlerService) UpdateCategory(ctx context.Context, req *connect.Request[taxonomyv1.UpdateCategoryRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	_, err := c.queries.GetCategoryById(ctx, strconv.FormatInt(req.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}),
		)
	}

	_, err = c.queries.UpdateCategory(ctx, categoriesqueries.UpdateCategoryParams{
		Name:        req.Msg.Name,
		Description: &req.Msg.Description,
		ID:          req.Msg.Id,
	})
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "categoryUpdated", nil),
		Success: true,
	}), nil
}

func (c *CategoryHandlerService) DeleteCategory(ctx context.Context, req *connect.Request[taxonomyv1.DeleteCategoryRequest]) (*connect.Response[sharedv1.GenericResponse], error) {
	_, err := c.queries.GetCategoryById(ctx, strconv.FormatInt(req.Msg.Id, 10))
	if err != nil {
		return nil, errors.New(
			web.GetTranslation(ctx, "fieldNotFound", map[string]string{
				"field": web.GetTranslation(ctx, "category", nil),
			}),
		)
	}
	_, err = c.queries.DeleteCategory(ctx, req.Msg.Id)
	if err != nil {
		return nil, err
	}
	return connect.NewResponse(&sharedv1.GenericResponse{
		Status:  200,
		Message: web.GetTranslation(ctx, "categoryDeleted", nil),
		Success: true,
	}), nil
}

func (c *CategoryHandlerService) AddCategory(ctx context.Context, req *connect.Request[taxonomyv1.AddCategoryRequest]) (*connect.Response[taxonomyv1.AddCategoryResponse], error) {
	var img = "https://cdn.v-sai.com/dummy/136.jpg"

	if req.Msg.Image != nil {

		newPath, err := c.storageService.ValidateAndUploadFromTemp(ctx, req.Msg.Image, storagev1.ImageFileType, "categories", false, nil)

		if err != nil {

			return nil, validation.NewFieldError("image", err)

		}

		img = newPath

	}
	slug := helpers.Slugify(req.Msg.Name)

	_, err := c.queries.GetCategoryById(ctx, slug)
	if err == nil {
		slug = helpers.SlugifyWithTimestamp(slug)
	}

	newCategory, err := c.queries.CreateCategory(ctx, categoriesqueries.CreateCategoryParams{
		Name:        req.Msg.Name,
		Description: &req.Msg.Description,
		Image:       img,
		Slug:        slug,
	})
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&taxonomyv1.AddCategoryResponse{
		Data: &taxonomyv1.Category{
			Id:          newCategory.ID,
			Name:        newCategory.Name,
			Description: *newCategory.Description,
			CreatedAt:   timestamppb.New(newCategory.CreatedAt),
			Slug:        newCategory.Slug,
		},
	}), nil

}

func (c *CategoryHandlerService) GetAllCategories(ctx context.Context, _ *connect.Request[taxonomyv1.GetAllCategoriesRequest]) (*connect.Response[taxonomyv1.GetAllCategoriesResponse], error) {
	categories, err := c.queries.GetAllCategories(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&taxonomyv1.GetAllCategoriesResponse{
		Categories: helpers.Map(categories, func(category categoriesqueries.Category) *taxonomyv1.Category {
			return &taxonomyv1.Category{
				Id:          category.ID,
				Name:        category.Name,
				Description: *category.Description,
				CreatedAt:   timestamppb.New(category.CreatedAt),
				Slug:        category.Slug,
			}
		}),
	}), nil
}

func (c *CategoryHandlerService) GetEventCategories(ctx context.Context, req *connect.Request[taxonomyv1.GetEventCategoriesRequest]) (*connect.Response[taxonomyv1.GetEventCategoriesResponse], error) {

	categories, err := c.eventCategoryRepo.GetEventCategories(ctx)
	if err != nil {
		return nil, err
	}

	return connect.NewResponse(&taxonomyv1.GetEventCategoriesResponse{
		Categories: helpers.Map(categories, func(category eventcategoriesqueries.GetEventCategoriesRow) *taxonomyv1.Category {
			return &taxonomyv1.Category{
				Id:          category.ID,
				Name:        category.Name,
				Description: *category.Description,
				CreatedAt:   timestamppb.New(category.CreatedAt),
				Slug:        category.Slug,
			}
		}),
	}), nil
}
