package users

import (
	"encoding/json"
	"time"

	"github.com/nsp-inc/vtuber/packages/hasher"
)

type LastThreePassword struct {
	Password  string
	CreatedAt time.Time
}

type LastThreePasswords []LastThreePassword

func (l LastThreePasswords) GetLastThree() []string {
	return []string{}
}

func (l *LastThreePasswords) AddPassword(password string) {
	if len(*l) == 3 {
		*l = (*l)[1:]
	}
	*l = append(*l, LastThreePassword{
		Password:  password,
		CreatedAt: time.Now(),
	})
}

func (l LastThreePasswords) CheckIsOnLastThree(password string) bool {
	for _, p := range l {
		if hasher.CompareHash(p.Password, password) {
			return true
		}
	}
	return false
}

func (l LastThreePasswords) ToByte() []byte {
	jsonData, err := json.Marshal(l)
	if err != nil {
		return []byte("[]")
	}
	return jsonData
}

func LastThreePasswordsFromByte(data []byte) LastThreePasswords {
	var lastThreePasswords LastThreePasswords
	err := json.Unmarshal(data, &lastThreePasswords)
	if err != nil {
		return LastThreePasswords{}
	}
	return lastThreePasswords
}
