// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: announcements.sql

package announcementsqueries

import (
	"context"
)

const addAnnouncement = `-- name: AddAnnouncement :one
INSERT INTO announcements ( image, description )
VALUES ($1, $2) RETURNING id, image, description, active, created_at, updated_at
`

type AddAnnouncementParams struct {
	Image       string
	Description string
}

func (q *Queries) AddAnnouncement(ctx context.Context, arg AddAnnouncementParams) (Announcement, error) {
	row := q.db.QueryRow(ctx, addAnnouncement, arg.Image, arg.Description)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Description,
		&i.Active,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getAnnouncement = `-- name: GetAnnouncement :one
SELECT id, image, description, active, created_at, updated_at FROM announcements LIMIT 1
`

func (q *Queries) GetAnnouncement(ctx context.Context) (Announcement, error) {
	row := q.db.QueryRow(ctx, getAnnouncement)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Description,
		&i.Active,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getAnnouncementById = `-- name: GetAnnouncementById :one
SELECT id, image, description, active, created_at, updated_at FROM announcements WHERE id = $1
`

func (q *Queries) GetAnnouncementById(ctx context.Context, id int64) (Announcement, error) {
	row := q.db.QueryRow(ctx, getAnnouncementById, id)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Description,
		&i.Active,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const toggleAnnouncement = `-- name: ToggleAnnouncement :one
UPDATE announcements
SET active = NOT active, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 RETURNING id, image, description, active, created_at, updated_at
`

func (q *Queries) ToggleAnnouncement(ctx context.Context, id int64) (Announcement, error) {
	row := q.db.QueryRow(ctx, toggleAnnouncement, id)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Description,
		&i.Active,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateAnnouncement = `-- name: UpdateAnnouncement :one
UPDATE announcements
SET image = $1, description = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING id, image, description, active, created_at, updated_at
`

type UpdateAnnouncementParams struct {
	Image       string
	Description string
	ID          int64
}

func (q *Queries) UpdateAnnouncement(ctx context.Context, arg UpdateAnnouncementParams) (Announcement, error) {
	row := q.db.QueryRow(ctx, updateAnnouncement, arg.Image, arg.Description, arg.ID)
	var i Announcement
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Description,
		&i.Active,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
