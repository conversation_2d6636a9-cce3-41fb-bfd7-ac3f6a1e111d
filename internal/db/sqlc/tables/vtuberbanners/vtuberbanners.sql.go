// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtuberbanners.sql

package vtuberbannersqueries

import (
	"context"
)

const addVtuberBanner = `-- name: AddVtuberBanner :one
INSERT INTO vtuber_banners (vtuber_id, image)
VALUES ($1, $2) RETURNING id, vtuber_id, image, created_at, updated_at
`

type AddVtuberBannerParams struct {
	VtuberID int64
	Image    string
}

func (q *Queries) AddVtuberBanner(ctx context.Context, arg AddVtuberBannerParams) (VtuberBanner, error) {
	row := q.db.QueryRow(ctx, addVtuberBanner, arg.VtuberID, arg.Image)
	var i VtuberBanner
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Image,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const countVtuberBannerByVtuberId = `-- name: CountVtuberBannerByVtuberId :one
SELECT COUNT(*) FROM vtuber_banners WHERE vtuber_id = $1
`

func (q *Queries) CountVtuberBannerByVtuberId(ctx context.Context, vtuberID int64) (int64, error) {
	row := q.db.QueryRow(ctx, countVtuberBannerByVtuberId, vtuberID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const deleteVtuberBannerById = `-- name: DeleteVtuberBannerById :exec
DELETE FROM vtuber_banners WHERE id = $1
`

func (q *Queries) DeleteVtuberBannerById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberBannerById, id)
	return err
}

const getVtuberBannerById = `-- name: GetVtuberBannerById :one
SELECT id, vtuber_id, image, created_at, updated_at FROM vtuber_banners WHERE id = $1
`

func (q *Queries) GetVtuberBannerById(ctx context.Context, id int64) (VtuberBanner, error) {
	row := q.db.QueryRow(ctx, getVtuberBannerById, id)
	var i VtuberBanner
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Image,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getVtuberBannerByVtuberId = `-- name: GetVtuberBannerByVtuberId :many
SELECT vtuber_banners.id, vtuber_banners.vtuber_id, vtuber_banners.image, vtuber_banners.created_at, vtuber_banners.updated_at FROM vtuber_banners INNER JOIN vtuber_profiles ON vtuber_banners.vtuber_id = vtuber_profiles.id WHERE vtuber_profiles.id::TEXT = $1::TEXT OR vtuber_profiles.username::TEXT = $1::TEXT ORDER BY vtuber_banners.created_at DESC
`

func (q *Queries) GetVtuberBannerByVtuberId(ctx context.Context, id string) ([]VtuberBanner, error) {
	rows, err := q.db.Query(ctx, getVtuberBannerByVtuberId, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []VtuberBanner
	for rows.Next() {
		var i VtuberBanner
		if err := rows.Scan(
			&i.ID,
			&i.VtuberID,
			&i.Image,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateVtuberBannerById = `-- name: UpdateVtuberBannerById :exec
UPDATE vtuber_banners SET image = $1 WHERE id = $2
`

type UpdateVtuberBannerByIdParams struct {
	Image string
	ID    int64
}

func (q *Queries) UpdateVtuberBannerById(ctx context.Context, arg UpdateVtuberBannerByIdParams) error {
	_, err := q.db.Exec(ctx, updateVtuberBannerById, arg.Image, arg.ID)
	return err
}
