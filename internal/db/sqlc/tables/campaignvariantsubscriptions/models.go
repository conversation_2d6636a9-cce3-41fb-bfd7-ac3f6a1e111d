// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package campaignvariantsubscriptionsqueries

import (
	"time"
)

type CampaignVariantSubscription struct {
	ID                int64
	UserID            int64
	CampaignVariantID int64
	CampaignID        int64
	VtuberID          int64
	Price             int32
	Comment           *string
	CreatedAt         time.Time
	UpdatedAt         time.Time
}
