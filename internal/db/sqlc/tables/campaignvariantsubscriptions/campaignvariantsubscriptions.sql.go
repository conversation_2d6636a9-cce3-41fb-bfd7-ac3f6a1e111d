// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaignvariantsubscriptions.sql

package campaignvariantsubscriptionsqueries

import (
	"context"
	"time"
)

const addCampaignVariantSubscription = `-- name: AddCampaignVariantSubscription :one
INSERT INTO campaign_variant_subscriptions (user_id, campaign_variant_id, vtuber_id, price,campaign_id, comment)
VALUES ($1,$2,$3,$4,$5, $6) RETURNING id, user_id, campaign_variant_id, campaign_id, vtuber_id, price, comment, created_at, updated_at
`

type AddCampaignVariantSubscriptionParams struct {
	UserID            int64
	CampaignVariantID int64
	VtuberID          int64
	Price             int32
	CampaignID        int64
	Comment           *string
}

func (q *Queries) AddCampaignVariantSubscription(ctx context.Context, arg AddCampaignVariantSubscriptionParams) (CampaignVariantSubscription, error) {
	row := q.db.QueryRow(ctx, addCampaignVariantSubscription,
		arg.UserID,
		arg.CampaignVariantID,
		arg.VtuberID,
		arg.Price,
		arg.CampaignID,
		arg.Comment,
	)
	var i CampaignVariantSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.CampaignVariantID,
		&i.CampaignID,
		&i.VtuberID,
		&i.Price,
		&i.Comment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteCampaignVariantSubscription = `-- name: DeleteCampaignVariantSubscription :exec
DELETE FROM campaign_variant_subscriptions WHERE id=$1
`

func (q *Queries) DeleteCampaignVariantSubscription(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCampaignVariantSubscription, id)
	return err
}

const getCampaignVariantSubscriptionByUserIdAndCampaignVariantId = `-- name: GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId :one
SELECT id, user_id, campaign_variant_id, campaign_id, vtuber_id, price, comment, created_at, updated_at FROM campaign_variant_subscriptions WHERE user_id=$1 AND campaign_variant_id=$2
`

type GetCampaignVariantSubscriptionByUserIdAndCampaignVariantIdParams struct {
	UserID            int64
	CampaignVariantID int64
}

func (q *Queries) GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId(ctx context.Context, arg GetCampaignVariantSubscriptionByUserIdAndCampaignVariantIdParams) (CampaignVariantSubscription, error) {
	row := q.db.QueryRow(ctx, getCampaignVariantSubscriptionByUserIdAndCampaignVariantId, arg.UserID, arg.CampaignVariantID)
	var i CampaignVariantSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.CampaignVariantID,
		&i.CampaignID,
		&i.VtuberID,
		&i.Price,
		&i.Comment,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getCampaignVariantSubscriptionCount = `-- name: GetCampaignVariantSubscriptionCount :one
SELECT COUNT(*) FROM campaign_variant_subscriptions WHERE campaign_variant_id=$1
`

func (q *Queries) GetCampaignVariantSubscriptionCount(ctx context.Context, campaignVariantID int64) (int64, error) {
	row := q.db.QueryRow(ctx, getCampaignVariantSubscriptionCount, campaignVariantID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getUserCampaignVariantSubscriptions = `-- name: GetUserCampaignVariantSubscriptions :many
WITH FILTERED AS (SELECT
                      cvs.id, cvs.user_id, cvs.campaign_variant_id, cvs.campaign_id, cvs.vtuber_id, cvs.price, cvs.comment, cvs.created_at, cvs.updated_at,
                      c.name as campaign_name,
                      c.thumbnail as campaign_thumbnail,
                      cv.title AS campaign_variant_title,
                      cv.description AS campaign_variant_description,
                      cv.image AS campaign_variant_image
                  FROM campaign_variant_subscriptions cvs
                           INNER JOIN campaign_variants cv ON cvs.campaign_variant_id = cv.id
                           INNER JOIN campaigns c ON c.id = cvs.campaign_id
                  WHERE cvs.user_id = $1
),
        COUNTED AS (SELECT COUNT(*) AS total
                    FROM FILTERED)
SELECT f.id, f.user_id, f.campaign_variant_id, f.campaign_id, f.vtuber_id, f.price, f.comment, f.created_at, f.updated_at, f.campaign_name, f.campaign_thumbnail, f.campaign_variant_title, f.campaign_variant_description, f.campaign_variant_image, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at END DESC
LIMIT $5 OFFSET $4
`

type GetUserCampaignVariantSubscriptionsParams struct {
	UserID int64
	Sort   string
	Order  string
	Offset int32
	Limit  int32
}

type GetUserCampaignVariantSubscriptionsRow struct {
	ID                         int64
	UserID                     int64
	CampaignVariantID          int64
	CampaignID                 int64
	VtuberID                   int64
	Price                      int32
	Comment                    *string
	CreatedAt                  time.Time
	UpdatedAt                  time.Time
	CampaignName               string
	CampaignThumbnail          string
	CampaignVariantTitle       string
	CampaignVariantDescription string
	CampaignVariantImage       string
	Total                      int64
}

func (q *Queries) GetUserCampaignVariantSubscriptions(ctx context.Context, arg GetUserCampaignVariantSubscriptionsParams) ([]GetUserCampaignVariantSubscriptionsRow, error) {
	rows, err := q.db.Query(ctx, getUserCampaignVariantSubscriptions,
		arg.UserID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUserCampaignVariantSubscriptionsRow
	for rows.Next() {
		var i GetUserCampaignVariantSubscriptionsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.CampaignVariantID,
			&i.CampaignID,
			&i.VtuberID,
			&i.Price,
			&i.Comment,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.CampaignName,
			&i.CampaignThumbnail,
			&i.CampaignVariantTitle,
			&i.CampaignVariantDescription,
			&i.CampaignVariantImage,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const popularCampaign = `-- name: PopularCampaign :one
SELECT SUM(cvs.price) as price, cvs.campaign_id
FROM campaign_variant_subscriptions cvs 
INNER JOIN campaigns c ON c.id = cvs.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY cvs.campaign_id ORDER BY price DESC LIMIT 1
`

type PopularCampaignRow struct {
	Price      int64
	CampaignID int64
}

func (q *Queries) PopularCampaign(ctx context.Context) (PopularCampaignRow, error) {
	row := q.db.QueryRow(ctx, popularCampaign)
	var i PopularCampaignRow
	err := row.Scan(&i.Price, &i.CampaignID)
	return i, err
}
