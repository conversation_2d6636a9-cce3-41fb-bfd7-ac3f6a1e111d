// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: accounts.sql

package accountsqueries

import (
	"context"
	"time"
)

const createAccount = `-- name: CreateAccount :one
INSERT INTO accounts (account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires, refresh_token_expires, scope, password , last_three_password)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10 , $11) RETURNING id, account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires, refresh_token_expires, scope, password, created_at, updated_at, last_three_password
`

type CreateAccountParams struct {
	AccountID           string
	ProviderID          string
	UserID              int64
	AccessToken         *string
	RefreshToken        *string
	IDToken             *string
	AccessTokenExpires  *time.Time
	RefreshTokenExpires *time.Time
	Scope               *string
	Password            *string
	LastThreePassword   []byte
}

func (q *Queries) CreateAccount(ctx context.Context, arg CreateAccountParams) (Account, error) {
	row := q.db.QueryRow(ctx, createAccount,
		arg.AccountID,
		arg.ProviderID,
		arg.UserID,
		arg.AccessToken,
		arg.RefreshToken,
		arg.IDToken,
		arg.AccessTokenExpires,
		arg.RefreshTokenExpires,
		arg.Scope,
		arg.Password,
		arg.LastThreePassword,
	)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.AccountID,
		&i.ProviderID,
		&i.UserID,
		&i.AccessToken,
		&i.RefreshToken,
		&i.IDToken,
		&i.AccessTokenExpires,
		&i.RefreshTokenExpires,
		&i.Scope,
		&i.Password,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastThreePassword,
	)
	return i, err
}

const getAccountByEmail = `-- name: GetAccountByEmail :one
SELECT users.id, users.full_name, users.email, users.date_of_birth, users.image, users.email_verified, users.role, users.is_banned, users.ban_reason, users.ban_expires_at, users.created_at, users.updated_at, users.deleted_at , accounts.id, accounts.account_id, accounts.provider_id, accounts.user_id, accounts.access_token, accounts.refresh_token, accounts.id_token, accounts.access_token_expires, accounts.refresh_token_expires, accounts.scope, accounts.password, accounts.created_at, accounts.updated_at, accounts.last_three_password FROM accounts INNER JOIN users ON accounts.user_id = users.id WHERE accounts.provider_id = 'email' AND users.email = $1
`

type GetAccountByEmailRow struct {
	User    User
	Account Account
}

func (q *Queries) GetAccountByEmail(ctx context.Context, email *string) (GetAccountByEmailRow, error) {
	row := q.db.QueryRow(ctx, getAccountByEmail, email)
	var i GetAccountByEmailRow
	err := row.Scan(
		&i.User.ID,
		&i.User.FullName,
		&i.User.Email,
		&i.User.DateOfBirth,
		&i.User.Image,
		&i.User.EmailVerified,
		&i.User.Role,
		&i.User.IsBanned,
		&i.User.BanReason,
		&i.User.BanExpiresAt,
		&i.User.CreatedAt,
		&i.User.UpdatedAt,
		&i.User.DeletedAt,
		&i.Account.ID,
		&i.Account.AccountID,
		&i.Account.ProviderID,
		&i.Account.UserID,
		&i.Account.AccessToken,
		&i.Account.RefreshToken,
		&i.Account.IDToken,
		&i.Account.AccessTokenExpires,
		&i.Account.RefreshTokenExpires,
		&i.Account.Scope,
		&i.Account.Password,
		&i.Account.CreatedAt,
		&i.Account.UpdatedAt,
		&i.Account.LastThreePassword,
	)
	return i, err
}

const getAccountByProviderAndAccountID = `-- name: GetAccountByProviderAndAccountID :one
SELECT users.id, users.full_name, users.email, users.date_of_birth, users.image, users.email_verified, users.role, users.is_banned, users.ban_reason, users.ban_expires_at, users.created_at, users.updated_at, users.deleted_at , accounts.id, accounts.account_id, accounts.provider_id, accounts.user_id, accounts.access_token, accounts.refresh_token, accounts.id_token, accounts.access_token_expires, accounts.refresh_token_expires, accounts.scope, accounts.password, accounts.created_at, accounts.updated_at, accounts.last_three_password FROM accounts INNER JOIN users ON accounts.user_id = users.id
WHERE accounts.provider_id = $1 AND accounts.account_id = $2
`

type GetAccountByProviderAndAccountIDParams struct {
	ProviderID string
	AccountID  string
}

type GetAccountByProviderAndAccountIDRow struct {
	User    User
	Account Account
}

func (q *Queries) GetAccountByProviderAndAccountID(ctx context.Context, arg GetAccountByProviderAndAccountIDParams) (GetAccountByProviderAndAccountIDRow, error) {
	row := q.db.QueryRow(ctx, getAccountByProviderAndAccountID, arg.ProviderID, arg.AccountID)
	var i GetAccountByProviderAndAccountIDRow
	err := row.Scan(
		&i.User.ID,
		&i.User.FullName,
		&i.User.Email,
		&i.User.DateOfBirth,
		&i.User.Image,
		&i.User.EmailVerified,
		&i.User.Role,
		&i.User.IsBanned,
		&i.User.BanReason,
		&i.User.BanExpiresAt,
		&i.User.CreatedAt,
		&i.User.UpdatedAt,
		&i.User.DeletedAt,
		&i.Account.ID,
		&i.Account.AccountID,
		&i.Account.ProviderID,
		&i.Account.UserID,
		&i.Account.AccessToken,
		&i.Account.RefreshToken,
		&i.Account.IDToken,
		&i.Account.AccessTokenExpires,
		&i.Account.RefreshTokenExpires,
		&i.Account.Scope,
		&i.Account.Password,
		&i.Account.CreatedAt,
		&i.Account.UpdatedAt,
		&i.Account.LastThreePassword,
	)
	return i, err
}

const updateAccountPassword = `-- name: UpdateAccountPassword :exec
UPDATE accounts
SET password = $1, last_three_password = $2
WHERE id = $3 AND provider_id = 'email' AND user_id = $4
RETURNING id, account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires, refresh_token_expires, scope, password, created_at, updated_at, last_three_password
`

type UpdateAccountPasswordParams struct {
	Password          *string
	LastThreePassword []byte
	ID                int64
	UserID            int64
}

func (q *Queries) UpdateAccountPassword(ctx context.Context, arg UpdateAccountPasswordParams) error {
	_, err := q.db.Exec(ctx, updateAccountPassword,
		arg.Password,
		arg.LastThreePassword,
		arg.ID,
		arg.UserID,
	)
	return err
}

const updateAccountTokens = `-- name: UpdateAccountTokens :one
UPDATE accounts
SET access_token = $1, refresh_token = $2, id_token = $3,
    access_token_expires = $4, refresh_token_expires = $5, scope = $6
WHERE id = $7 AND provider_id = $8 AND user_id = $9
RETURNING id, account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires, refresh_token_expires, scope, password, created_at, updated_at, last_three_password
`

type UpdateAccountTokensParams struct {
	AccessToken         *string
	RefreshToken        *string
	IDToken             *string
	AccessTokenExpires  *time.Time
	RefreshTokenExpires *time.Time
	Scope               *string
	ID                  int64
	ProviderID          string
	UserID              int64
}

func (q *Queries) UpdateAccountTokens(ctx context.Context, arg UpdateAccountTokensParams) (Account, error) {
	row := q.db.QueryRow(ctx, updateAccountTokens,
		arg.AccessToken,
		arg.RefreshToken,
		arg.IDToken,
		arg.AccessTokenExpires,
		arg.RefreshTokenExpires,
		arg.Scope,
		arg.ID,
		arg.ProviderID,
		arg.UserID,
	)
	var i Account
	err := row.Scan(
		&i.ID,
		&i.AccountID,
		&i.ProviderID,
		&i.UserID,
		&i.AccessToken,
		&i.RefreshToken,
		&i.IDToken,
		&i.AccessTokenExpires,
		&i.RefreshTokenExpires,
		&i.Scope,
		&i.Password,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.LastThreePassword,
	)
	return i, err
}
