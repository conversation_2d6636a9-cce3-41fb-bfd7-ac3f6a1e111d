// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtubergalleries.sql

package vtubergalleriesqueries

import (
	"context"
	"time"
)

const addVtuberGallery = `-- name: AddVtuberGallery :one
INSERT INTO vtuber_galleries (vtuber_id, media, media_type, description)
VALUES ($1, $2, $3, $4) RETURNING id, vtuber_id, media, media_type, description, created_at, updated_at
`

type AddVtuberGalleryParams struct {
	VtuberID    int64
	Media       string
	MediaType   MediaType
	Description *string
}

func (q *Queries) AddVtuberGallery(ctx context.Context, arg AddVtuberGalleryParams) (VtuberGallery, error) {
	row := q.db.QueryRow(ctx, addVtuberGallery,
		arg.VtuberID,
		arg.Media,
		arg.MediaType,
		arg.Description,
	)
	var i VtuberGallery
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Media,
		&i.MediaType,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteVtuberGalleryById = `-- name: DeleteVtuberGalleryById :exec
DELETE FROM vtuber_galleries WHERE id = $1
`

func (q *Queries) DeleteVtuberGalleryById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberGalleryById, id)
	return err
}

const getVtuberGalleries = `-- name: GetVtuberGalleries :many
WITH FILTERED AS (
    SELECT id, vtuber_id, media, media_type, description, created_at, updated_at FROM vtuber_galleries WHERE vtuber_id = $1
),
COUNTED AS (
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.vtuber_id, f.media, f.media_type, f.description, f.created_at, f.updated_at, c.total
FROM FILTERED f, COUNTED c
ORDER BY
    -- Primary Sort
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at END DESC,

    -- Secondary Sort (tie-breaker)
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN id END DESC

LIMIT $5 OFFSET $4
`

type GetVtuberGalleriesParams struct {
	VtuberID int64
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
}

type GetVtuberGalleriesRow struct {
	ID          int64
	VtuberID    int64
	Media       string
	MediaType   MediaType
	Description *string
	CreatedAt   time.Time
	UpdatedAt   time.Time
	Total       int64
}

func (q *Queries) GetVtuberGalleries(ctx context.Context, arg GetVtuberGalleriesParams) ([]GetVtuberGalleriesRow, error) {
	rows, err := q.db.Query(ctx, getVtuberGalleries,
		arg.VtuberID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetVtuberGalleriesRow
	for rows.Next() {
		var i GetVtuberGalleriesRow
		if err := rows.Scan(
			&i.ID,
			&i.VtuberID,
			&i.Media,
			&i.MediaType,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVtuberGalleryById = `-- name: GetVtuberGalleryById :one
SELECT id, vtuber_id, media, media_type, description, created_at, updated_at FROM vtuber_galleries WHERE id = $1
`

func (q *Queries) GetVtuberGalleryById(ctx context.Context, id int64) (VtuberGallery, error) {
	row := q.db.QueryRow(ctx, getVtuberGalleryById, id)
	var i VtuberGallery
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Media,
		&i.MediaType,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateVtuberGalleryById = `-- name: UpdateVtuberGalleryById :exec
UPDATE vtuber_galleries SET media = $1, media_type = $2, description = $3 WHERE id = $4
`

type UpdateVtuberGalleryByIdParams struct {
	Media       string
	MediaType   MediaType
	Description *string
	ID          int64
}

func (q *Queries) UpdateVtuberGalleryById(ctx context.Context, arg UpdateVtuberGalleryByIdParams) error {
	_, err := q.db.Exec(ctx, updateVtuberGalleryById,
		arg.Media,
		arg.MediaType,
		arg.Description,
		arg.ID,
	)
	return err
}
