// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package notificationsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type NotificationSeverity string

const (
	NotificationSeverityInfo    NotificationSeverity = "info"
	NotificationSeverityWarning NotificationSeverity = "warning"
	NotificationSeveritySuccess NotificationSeverity = "success"
	NotificationSeverityDanger  NotificationSeverity = "danger"
)

func (e *NotificationSeverity) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = NotificationSeverity(s)
	case string:
		*e = NotificationSeverity(s)
	default:
		return fmt.Errorf("unsupported scan type for NotificationSeverity: %T", src)
	}
	return nil
}

type NullNotificationSeverity struct {
	NotificationSeverity NotificationSeverity
	Valid                bool // Valid is true if NotificationSeverity is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullNotificationSeverity) Scan(value interface{}) error {
	if value == nil {
		ns.NotificationSeverity, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.NotificationSeverity.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullNotificationSeverity) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.NotificationSeverity), nil
}

type Notification struct {
	ID            int64
	Title         string
	TitleJp       string
	Description   string
	DescriptionJp string
	Json          *string
	Deeplink      *string
	Severity      NotificationSeverity
	UserID        int64
	VtuberID      *int64
	IsRead        bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
}
