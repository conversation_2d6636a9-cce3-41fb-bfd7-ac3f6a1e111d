// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: notifications.sql

package notificationsqueries

import (
	"context"
	"time"
)

const addNotification = `-- name: AddNotification :one
INSERT INTO notifications (title,title_jp, description,description_jp,  json, deeplink, severity, user_id, vtuber_id )
VALUES ($1, $2, $3, $4, $5, $6, $7, $8,$9)
RETURNING id, title, title_jp, description, description_jp, json, deeplink, severity, user_id, vtuber_id, is_read, created_at, updated_at
`

type AddNotificationParams struct {
	Title         string
	TitleJp       string
	Description   string
	DescriptionJp string
	Json          *string
	Deeplink      *string
	Severity      NotificationSeverity
	UserID        int64
	VtuberID      *int64
}

func (q *Queries) AddNotification(ctx context.Context, arg AddNotificationParams) (Notification, error) {
	row := q.db.QueryRow(ctx, addNotification,
		arg.Title,
		arg.TitleJp,
		arg.Description,
		arg.DescriptionJp,
		arg.Json,
		arg.Deeplink,
		arg.Severity,
		arg.UserID,
		arg.VtuberID,
	)
	var i Notification
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.TitleJp,
		&i.Description,
		&i.DescriptionJp,
		&i.Json,
		&i.Deeplink,
		&i.Severity,
		&i.UserID,
		&i.VtuberID,
		&i.IsRead,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteNotification = `-- name: DeleteNotification :exec
DELETE FROM notifications WHERE id = $1
`

func (q *Queries) DeleteNotification(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteNotification, id)
	return err
}

const getNotification = `-- name: GetNotification :one
SELECT id, title, title_jp, description, description_jp, json, deeplink, severity, user_id, vtuber_id, is_read, created_at, updated_at FROM notifications WHERE id = $1
`

func (q *Queries) GetNotification(ctx context.Context, id int64) (Notification, error) {
	row := q.db.QueryRow(ctx, getNotification, id)
	var i Notification
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.TitleJp,
		&i.Description,
		&i.DescriptionJp,
		&i.Json,
		&i.Deeplink,
		&i.Severity,
		&i.UserID,
		&i.VtuberID,
		&i.IsRead,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getNotificationCount = `-- name: GetNotificationCount :one
SELECT COUNT(id) FROM notifications WHERE user_id = $1 AND is_read=false
`

func (q *Queries) GetNotificationCount(ctx context.Context, userID int64) (int64, error) {
	row := q.db.QueryRow(ctx, getNotificationCount, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const listNotifications = `-- name: ListNotifications :many
WITH FILTERED AS (SELECT id, title, title_jp, description, description_jp, json, deeplink, severity, user_id, vtuber_id, is_read, created_at, updated_at
FROM notifications
WHERE user_id= COALESCE($3 , user_id)
AND vtuber_id=COALESCE($4 , vtuber_id)
ORDER BY created_at DESC
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.title, f.title_jp, f.description, f.description_jp, f.json, f.deeplink, f.severity, f.user_id, f.vtuber_id, f.is_read, f.created_at, f.updated_at, c.total FROM FILTERED f, COUNTED c
LIMIT $2 OFFSET $1
`

type ListNotificationsParams struct {
	Offset   int32
	Limit    int32
	UserID   *int64
	VtuberID *int64
}

type ListNotificationsRow struct {
	ID            int64
	Title         string
	TitleJp       string
	Description   string
	DescriptionJp string
	Json          *string
	Deeplink      *string
	Severity      NotificationSeverity
	UserID        int64
	VtuberID      *int64
	IsRead        bool
	CreatedAt     time.Time
	UpdatedAt     time.Time
	Total         int64
}

func (q *Queries) ListNotifications(ctx context.Context, arg ListNotificationsParams) ([]ListNotificationsRow, error) {
	rows, err := q.db.Query(ctx, listNotifications,
		arg.Offset,
		arg.Limit,
		arg.UserID,
		arg.VtuberID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListNotificationsRow
	for rows.Next() {
		var i ListNotificationsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.TitleJp,
			&i.Description,
			&i.DescriptionJp,
			&i.Json,
			&i.Deeplink,
			&i.Severity,
			&i.UserID,
			&i.VtuberID,
			&i.IsRead,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const readNotification = `-- name: ReadNotification :exec
UPDATE notifications SET is_read = true WHERE id = $1
`

func (q *Queries) ReadNotification(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, readNotification, id)
	return err
}

const unreadNotification = `-- name: UnreadNotification :exec
UPDATE notifications SET is_read = false WHERE id = $1
`

func (q *Queries) UnreadNotification(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, unreadNotification, id)
	return err
}
