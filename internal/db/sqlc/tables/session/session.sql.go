// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: session.sql

package sessionqueries

import (
	"context"
	"time"
)

const createSession = `-- name: CreateSession :exec
INSERT INTO session (user_id, location, uuid, expires_at, ip_address, user_agent)
VALUES ($1, $2, $3, $4, $5, $6 )
`

type CreateSessionParams struct {
	UserID    int64
	Location  *string
	Uuid      string
	ExpiresAt time.Time
	IpAddress *string
	UserAgent *string
}

func (q *Queries) CreateSession(ctx context.Context, arg CreateSessionParams) error {
	_, err := q.db.Exec(ctx, createSession,
		arg.UserID,
		arg.Location,
		arg.Uuid,
		arg.ExpiresAt,
		arg.IpAddress,
		arg.UserAgent,
	)
	return err
}

const deleteSession = `-- name: DeleteSession :exec
DELETE FROM session WHERE uuid = $1 AND user_id = $2
`

type DeleteSessionParams struct {
	Uuid   string
	UserID int64
}

func (q *Queries) DeleteSession(ctx context.Context, arg DeleteSessionParams) error {
	_, err := q.db.Exec(ctx, deleteSession, arg.Uuid, arg.UserID)
	return err
}

const deleteSessionById = `-- name: DeleteSessionById :exec
DELETE FROM session WHERE id = $1
`

func (q *Queries) DeleteSessionById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteSessionById, id)
	return err
}

const getSession = `-- name: GetSession :one
SELECT id, user_id, location, uuid, expires_at, ip_address, user_agent, created_at, updated_at FROM session WHERE uuid = $1 AND user_id = $2
`

type GetSessionParams struct {
	Uuid   string
	UserID int64
}

func (q *Queries) GetSession(ctx context.Context, arg GetSessionParams) (Session, error) {
	row := q.db.QueryRow(ctx, getSession, arg.Uuid, arg.UserID)
	var i Session
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Location,
		&i.Uuid,
		&i.ExpiresAt,
		&i.IpAddress,
		&i.UserAgent,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const listSessions = `-- name: ListSessions :many
SELECT id, user_id, location, uuid, expires_at, ip_address, user_agent, created_at, updated_at FROM session WHERE user_id = $1 AND expired_at > CURRENT_TIMESTAMP
`

func (q *Queries) ListSessions(ctx context.Context, userID int64) ([]Session, error) {
	rows, err := q.db.Query(ctx, listSessions, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Session
	for rows.Next() {
		var i Session
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Location,
			&i.Uuid,
			&i.ExpiresAt,
			&i.IpAddress,
			&i.UserAgent,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateSession = `-- name: UpdateSession :exec
UPDATE session 
SET uuid = $4,
ip_address = $1,
user_agent = $2,
expires_at = $3
WHERE uuid = $5 RETURNING id, user_id, location, uuid, expires_at, ip_address, user_agent, created_at, updated_at
`

type UpdateSessionParams struct {
	IpAddress *string
	UserAgent *string
	ExpiresAt time.Time
	NewUuid   string
	OldUuid   string
}

func (q *Queries) UpdateSession(ctx context.Context, arg UpdateSessionParams) error {
	_, err := q.db.Exec(ctx, updateSession,
		arg.IpAddress,
		arg.UserAgent,
		arg.ExpiresAt,
		arg.NewUuid,
		arg.OldUuid,
	)
	return err
}
