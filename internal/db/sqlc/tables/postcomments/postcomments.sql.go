// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: postcomments.sql

package postcommentsqueries

import (
	"context"
	"time"
)

const addPostComment = `-- name: AddPostComment :one
WITH inserted AS (INSERT INTO post_comments (post_id, user_id, parent_id, content, vtuber_id)
    VALUES ($1, $2, $3, $4, $5)
    RETURNING id, user_id, vtuber_id, parent_id, content, post_id, created_at, updated_at)
SELECT inserted.id, inserted.user_id, inserted.vtuber_id, inserted.parent_id, inserted.content, inserted.post_id, inserted.created_at, inserted.updated_at,
       u.full_name     as userName,
       u.id            as userId,
       u.image         as userImage,
       vp.id           as vtuberId,
       vp.display_name as vtuberDisplayName,
       vp.image        as vtuberImage
FROM inserted
         JOIN users u ON inserted.user_id = u.id
         LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
`

type AddPostCommentParams struct {
	PostID   int64
	UserID   int64
	ParentID *int64
	Content  string
	VtuberID *int64
}

type AddPostCommentRow struct {
	ID                int64
	UserID            int64
	VtuberID          *int64
	ParentID          *int64
	Content           string
	PostID            int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	Username          string
	Userid            int64
	Userimage         *string
	Vtuberid          *int64
	Vtuberdisplayname *string
	Vtuberimage       *string
}

func (q *Queries) AddPostComment(ctx context.Context, arg AddPostCommentParams) (AddPostCommentRow, error) {
	row := q.db.QueryRow(ctx, addPostComment,
		arg.PostID,
		arg.UserID,
		arg.ParentID,
		arg.Content,
		arg.VtuberID,
	)
	var i AddPostCommentRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.ParentID,
		&i.Content,
		&i.PostID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Username,
		&i.Userid,
		&i.Userimage,
		&i.Vtuberid,
		&i.Vtuberdisplayname,
		&i.Vtuberimage,
	)
	return i, err
}

const deletePostCommentById = `-- name: DeletePostCommentById :exec
DELETE
FROM post_comments
WHERE id = $1
`

func (q *Queries) DeletePostCommentById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deletePostCommentById, id)
	return err
}

const getAllPostComments = `-- name: GetAllPostComments :many
WITH FILTERED AS (SELECT ps.id             AS id,
                         ps.id             AS user_id,
                         ps.vtuber_id      as comment_vtuber_id,
                         ps.content        AS content,
                         ps.post_id        AS post_id,
                         ps.parent_id      AS parent_id,
                         ps.created_at     AS created_at,
                         ps.updated_at     AS updated_at,
                         u.id              AS user_id_from_users,
                         u.full_name       AS full_name,
                         u.image           AS image,
                         vp.id             AS vtuber_id,
                         vp.display_name   AS vtuber_display_name,
                         vp.image          AS vtuber_image,
                         COUNT(vps.id) > 0 AS has_replies
                  FROM post_comments AS ps
                           LEFT JOIN
                       post_comments AS vps ON vps.parent_id = ps.id
                           INNER JOIN
                       users AS u ON u.id = ps.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                           INNER JOIN posts p ON p.id = ps.post_id
                  WHERE (ps.post_id::TEXT = $5::TEXT OR p.slug::TEXT = $5::TEXT)
                    AND ps.parent_id IS NULL
                  GROUP BY ps.id, ps.content, ps.post_id, ps.parent_id, ps.created_at, ps.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.user_id, f.comment_vtuber_id, f.content, f.post_id, f.parent_id, f.created_at, f.updated_at, f.user_id_from_users, f.full_name, f.image, f.vtuber_id, f.vtuber_display_name, f.vtuber_image, f.has_replies, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN f.id END,
         CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT $4 OFFSET $3
`

type GetAllPostCommentsParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
	PostID string
}

type GetAllPostCommentsRow struct {
	ID                int64
	UserID            int64
	CommentVtuberID   *int64
	Content           string
	PostID            int64
	ParentID          *int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	UserIDFromUsers   int64
	FullName          string
	Image             *string
	VtuberID          *int64
	VtuberDisplayName *string
	VtuberImage       *string
	HasReplies        bool
	Total             int64
}

func (q *Queries) GetAllPostComments(ctx context.Context, arg GetAllPostCommentsParams) ([]GetAllPostCommentsRow, error) {
	rows, err := q.db.Query(ctx, getAllPostComments,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.PostID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllPostCommentsRow
	for rows.Next() {
		var i GetAllPostCommentsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.CommentVtuberID,
			&i.Content,
			&i.PostID,
			&i.ParentID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserIDFromUsers,
			&i.FullName,
			&i.Image,
			&i.VtuberID,
			&i.VtuberDisplayName,
			&i.VtuberImage,
			&i.HasReplies,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRepliesOfComment = `-- name: GetAllRepliesOfComment :many
WITH FILTERED AS (SELECT ps.id             AS id,
                         ps.id             AS user_id,
                            ps.vtuber_id      as comment_vtuber_id,
                         ps.content        AS content,
                         ps.post_id        AS post_id,
                         ps.parent_id      AS parent_id,
                         ps.created_at     AS created_at,
                         ps.updated_at     AS updated_at,
                         u.id AS user_id_from_users,
                         u.full_name AS full_name,
                         u.image AS image,
                         vp.id AS vtuber_id,
                         vp.display_name AS vtuber_display_name,
                         vp.image AS vtuber_image,
                         COUNT(vps.id) > 0 AS has_replies
                  FROM post_comments AS ps
                           LEFT JOIN
                       post_comments AS vps ON vps.parent_id = ps.id
                           INNER JOIN
                       users AS u ON u.id = ps.user_id
                            LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ps.parent_id = $1
                  GROUP BY ps.id, ps.content, ps.post_id, ps.parent_id, ps.created_at, ps.updated_at, u.id, vp.id),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.user_id, f.comment_vtuber_id, f.content, f.post_id, f.parent_id, f.created_at, f.updated_at, f.user_id_from_users, f.full_name, f.image, f.vtuber_id, f.vtuber_display_name, f.vtuber_image, f.has_replies, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'ASC' THEN f.id END,
         CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT $5 OFFSET $4
`

type GetAllRepliesOfCommentParams struct {
	ParentID *int64
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
}

type GetAllRepliesOfCommentRow struct {
	ID                int64
	UserID            int64
	CommentVtuberID   *int64
	Content           string
	PostID            int64
	ParentID          *int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	UserIDFromUsers   int64
	FullName          string
	Image             *string
	VtuberID          *int64
	VtuberDisplayName *string
	VtuberImage       *string
	HasReplies        bool
	Total             int64
}

func (q *Queries) GetAllRepliesOfComment(ctx context.Context, arg GetAllRepliesOfCommentParams) ([]GetAllRepliesOfCommentRow, error) {
	rows, err := q.db.Query(ctx, getAllRepliesOfComment,
		arg.ParentID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllRepliesOfCommentRow
	for rows.Next() {
		var i GetAllRepliesOfCommentRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.CommentVtuberID,
			&i.Content,
			&i.PostID,
			&i.ParentID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserIDFromUsers,
			&i.FullName,
			&i.Image,
			&i.VtuberID,
			&i.VtuberDisplayName,
			&i.VtuberImage,
			&i.HasReplies,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getPostCommentById = `-- name: GetPostCommentById :one
SELECT id, user_id, vtuber_id, parent_id, content, post_id, created_at, updated_at
FROM post_comments
WHERE id = $1
`

func (q *Queries) GetPostCommentById(ctx context.Context, id int64) (PostComment, error) {
	row := q.db.QueryRow(ctx, getPostCommentById, id)
	var i PostComment
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.ParentID,
		&i.Content,
		&i.PostID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updatePostCommentById = `-- name: UpdatePostCommentById :exec
UPDATE post_comments
SET content = $1
WHERE id = $2
`

type UpdatePostCommentByIdParams struct {
	Content string
	ID      int64
}

func (q *Queries) UpdatePostCommentById(ctx context.Context, arg UpdatePostCommentByIdParams) error {
	_, err := q.db.Exec(ctx, updatePostCommentById, arg.Content, arg.ID)
	return err
}
