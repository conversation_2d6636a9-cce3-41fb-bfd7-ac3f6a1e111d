// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtuberprofiles.sql

package vtuberprofilesqueries

import (
	"context"
	"time"
)

const createVtuberProfile = `-- name: CreateVtuberProfile :one
INSERT INTO vtuber_profiles (
    user_id, display_name, furigana, image, banner_image, social_media_links,username
)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING id, user_id, display_name, username, furigana, image, banner_image, social_media_links, description, created_at, updated_at, deleted_at, is_plan_active
`

type CreateVtuberProfileParams struct {
	UserID           int64
	DisplayName      string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Username         string
}

func (q *Queries) CreateVtuberProfile(ctx context.Context, arg CreateVtuberProfileParams) (VtuberProfile, error) {
	row := q.db.QueryRow(ctx, createVtuberProfile,
		arg.UserID,
		arg.DisplayName,
		arg.Furigana,
		arg.Image,
		arg.BannerImage,
		arg.SocialMediaLinks,
		arg.Username,
	)
	var i VtuberProfile
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsPlanActive,
	)
	return i, err
}

const deleteVtuberProfileByID = `-- name: DeleteVtuberProfileByID :exec
UPDATE vtuber_profiles
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeleteVtuberProfileByID(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberProfileByID, id)
	return err
}

const getVtuberId = `-- name: GetVtuberId :one
SELECT id FROM vtuber_profiles WHERE user_id = $1
`

func (q *Queries) GetVtuberId(ctx context.Context, userID int64) (int64, error) {
	row := q.db.QueryRow(ctx, getVtuberId, userID)
	var id int64
	err := row.Scan(&id)
	return id, err
}

const getVtuberProfileByID = `-- name: GetVtuberProfileByID :one
SELECT vp.id, vp.user_id, vp.display_name, vp.username, vp.furigana, vp.image, vp.banner_image, vp.social_media_links, vp.description, vp.created_at, vp.updated_at, vp.deleted_at, vp.is_plan_active,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
FROM vtuber_profiles as vp
    LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
WHERE (vp.id::TEXT = $1::TEXT OR vp.username::TEXT=$1::TEXT) 
AND deleted_at IS NULL 
GROUP BY vp.id
`

type GetVtuberProfileByIDRow struct {
	ID               int64
	UserID           int64
	DisplayName      string
	Username         string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description      *string
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	IsPlanActive     bool
	CategoryIds      []int64
}

func (q *Queries) GetVtuberProfileByID(ctx context.Context, id string) (GetVtuberProfileByIDRow, error) {
	row := q.db.QueryRow(ctx, getVtuberProfileByID, id)
	var i GetVtuberProfileByIDRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsPlanActive,
		&i.CategoryIds,
	)
	return i, err
}

const getVtuberProfileByUserID = `-- name: GetVtuberProfileByUserID :one
SELECT vp.id, vp.user_id, vp.display_name, vp.username, vp.furigana, vp.image, vp.banner_image, vp.social_media_links, vp.description, vp.created_at, vp.updated_at, vp.deleted_at, vp.is_plan_active,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
FROM vtuber_profiles as vp
    LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
WHERE user_id = $1 
AND deleted_at IS NULL 
GROUP BY vp.id
`

type GetVtuberProfileByUserIDRow struct {
	ID               int64
	UserID           int64
	DisplayName      string
	Username         string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description      *string
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	IsPlanActive     bool
	CategoryIds      []int64
}

func (q *Queries) GetVtuberProfileByUserID(ctx context.Context, userID int64) (GetVtuberProfileByUserIDRow, error) {
	row := q.db.QueryRow(ctx, getVtuberProfileByUserID, userID)
	var i GetVtuberProfileByUserIDRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsPlanActive,
		&i.CategoryIds,
	)
	return i, err
}

const getVtuberProfileByUsername = `-- name: GetVtuberProfileByUsername :one
SELECT id, user_id, display_name, username, furigana, image, banner_image, social_media_links, description, created_at, updated_at, deleted_at, is_plan_active FROM vtuber_profiles
WHERE username = $1
`

func (q *Queries) GetVtuberProfileByUsername(ctx context.Context, username string) (VtuberProfile, error) {
	row := q.db.QueryRow(ctx, getVtuberProfileByUsername, username)
	var i VtuberProfile
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsPlanActive,
	)
	return i, err
}

const getVtuberUserId = `-- name: GetVtuberUserId :one
SELECT user_id FROM vtuber_profiles WHERE id = $1
`

func (q *Queries) GetVtuberUserId(ctx context.Context, id int64) (int64, error) {
	row := q.db.QueryRow(ctx, getVtuberUserId, id)
	var user_id int64
	err := row.Scan(&user_id)
	return user_id, err
}

const listVtuberProfiles = `-- name: ListVtuberProfiles :many
WITH FILTERED AS (SELECT vp.id, vp.user_id, vp.display_name, vp.username, vp.furigana, vp.image, vp.banner_image, vp.social_media_links, vp.description, vp.created_at, vp.updated_at, vp.deleted_at, vp.is_plan_active,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
   FROM vtuber_profiles as vp
      LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
      WHERE display_name LIKE COALESCE('%' || $5 || '%', display_name)
      AND ($6::BIGINT IS NULL OR vc.category_id = $6::BIGINT)
      AND deleted_at IS NULL
      GROUP BY vp.id
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.id, f.user_id, f.display_name, f.username, f.furigana, f.image, f.banner_image, f.social_media_links, f.description, f.created_at, f.updated_at, f.deleted_at, f.is_plan_active, f.category_ids, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN $1::TEXT = 'display_name' AND $2::TEXT = 'ASC' THEN display_name END ASC,
      CASE WHEN $1::TEXT = 'display_name' AND $2::TEXT = 'DESC' THEN display_name END DESC,
      CASE WHEN $1::TEXT = 'furigana' AND $2::TEXT = 'ASC' THEN furigana END ASC,
      CASE WHEN $1::TEXT = 'furigana' AND $2::TEXT = 'DESC' THEN furigana END DESC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT $4 OFFSET $3
`

type ListVtuberProfilesParams struct {
	Sort        string
	Order       string
	Offset      int32
	Limit       int32
	DisplayName *string
	CategoryID  *int64
}

type ListVtuberProfilesRow struct {
	ID               int64
	UserID           int64
	DisplayName      string
	Username         string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description      *string
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	IsPlanActive     bool
	CategoryIds      []int64
	Total            int64
}

func (q *Queries) ListVtuberProfiles(ctx context.Context, arg ListVtuberProfilesParams) ([]ListVtuberProfilesRow, error) {
	rows, err := q.db.Query(ctx, listVtuberProfiles,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.DisplayName,
		arg.CategoryID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListVtuberProfilesRow
	for rows.Next() {
		var i ListVtuberProfilesRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.DisplayName,
			&i.Username,
			&i.Furigana,
			&i.Image,
			&i.BannerImage,
			&i.SocialMediaLinks,
			&i.Description,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.IsPlanActive,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateVtuberProfile = `-- name: UpdateVtuberProfile :one
UPDATE vtuber_profiles
SET display_name = $1,
    furigana = $2,
    image = $3,
    banner_image = $4,
    social_media_links=$5,
    description = $6,
    is_plan_active = $7,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $8
RETURNING id, user_id, display_name, username, furigana, image, banner_image, social_media_links, description, created_at, updated_at, deleted_at, is_plan_active
`

type UpdateVtuberProfileParams struct {
	DisplayName      string
	Furigana         string
	Image            *string
	BannerImage      *string
	SocialMediaLinks []byte
	Description      *string
	IsPlanActive     bool
	ID               int64
}

func (q *Queries) UpdateVtuberProfile(ctx context.Context, arg UpdateVtuberProfileParams) (VtuberProfile, error) {
	row := q.db.QueryRow(ctx, updateVtuberProfile,
		arg.DisplayName,
		arg.Furigana,
		arg.Image,
		arg.BannerImage,
		arg.SocialMediaLinks,
		arg.Description,
		arg.IsPlanActive,
		arg.ID,
	)
	var i VtuberProfile
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.DisplayName,
		&i.Username,
		&i.Furigana,
		&i.Image,
		&i.BannerImage,
		&i.SocialMediaLinks,
		&i.Description,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.IsPlanActive,
	)
	return i, err
}

const verifyVtuberProfile = `-- name: VerifyVtuberProfile :exec
UPDATE users SET role = 'vtuber'
WHERE id = $1
`

func (q *Queries) VerifyVtuberProfile(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, verifyVtuberProfile, id)
	return err
}
