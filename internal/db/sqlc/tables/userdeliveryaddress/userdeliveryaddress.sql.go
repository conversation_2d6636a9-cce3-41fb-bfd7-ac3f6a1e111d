// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: userdeliveryaddress.sql

package userdeliveryaddressqueries

import (
	"context"
	"time"
)

const addUserDeliveryAddress = `-- name: AddUserDeliveryAddress :one
INSERT INTO user_delivery_address (
    user_id,
    recipient,
    phone_number,
    postal_code,
    prefecture,
    city,
    address_line1,
    address_line2,
    preferred_delivery_time,
    preferred_delivery_date
) VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10 
) RETURNING id, user_id, recipient, phone_number, postal_code, prefecture, city, address_line1, address_line2, preferred_delivery_time, preferred_delivery_date, created_at, updated_at
`

type AddUserDeliveryAddressParams struct {
	UserID                int64
	Recipient             string
	PhoneNumber           string
	PostalCode            string
	Prefecture            string
	City                  string
	AddressLine1          string
	AddressLine2          *string
	PreferredDeliveryTime *string
	PreferredDeliveryDate *time.Time
}

func (q *Queries) AddUserDeliveryAddress(ctx context.Context, arg AddUserDeliveryAddressParams) (UserDeliveryAddress, error) {
	row := q.db.QueryRow(ctx, addUserDeliveryAddress,
		arg.UserID,
		arg.Recipient,
		arg.PhoneNumber,
		arg.PostalCode,
		arg.Prefecture,
		arg.City,
		arg.AddressLine1,
		arg.AddressLine2,
		arg.PreferredDeliveryTime,
		arg.PreferredDeliveryDate,
	)
	var i UserDeliveryAddress
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Recipient,
		&i.PhoneNumber,
		&i.PostalCode,
		&i.Prefecture,
		&i.City,
		&i.AddressLine1,
		&i.AddressLine2,
		&i.PreferredDeliveryTime,
		&i.PreferredDeliveryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteUserDeliveryAddress = `-- name: DeleteUserDeliveryAddress :exec
DELETE FROM user_delivery_address
WHERE id = $1
`

func (q *Queries) DeleteUserDeliveryAddress(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteUserDeliveryAddress, id)
	return err
}

const getUserDeliveryAddressById = `-- name: GetUserDeliveryAddressById :one
SELECT id, user_id, recipient, phone_number, postal_code, prefecture, city, address_line1, address_line2, preferred_delivery_time, preferred_delivery_date, created_at, updated_at FROM user_delivery_address
WHERE id = $1
`

func (q *Queries) GetUserDeliveryAddressById(ctx context.Context, id int64) (UserDeliveryAddress, error) {
	row := q.db.QueryRow(ctx, getUserDeliveryAddressById, id)
	var i UserDeliveryAddress
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Recipient,
		&i.PhoneNumber,
		&i.PostalCode,
		&i.Prefecture,
		&i.City,
		&i.AddressLine1,
		&i.AddressLine2,
		&i.PreferredDeliveryTime,
		&i.PreferredDeliveryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserDeliveryAddressByUserId = `-- name: GetUserDeliveryAddressByUserId :one
SELECT id, user_id, recipient, phone_number, postal_code, prefecture, city, address_line1, address_line2, preferred_delivery_time, preferred_delivery_date, created_at, updated_at FROM user_delivery_address
WHERE user_id = $1
`

func (q *Queries) GetUserDeliveryAddressByUserId(ctx context.Context, userID int64) (UserDeliveryAddress, error) {
	row := q.db.QueryRow(ctx, getUserDeliveryAddressByUserId, userID)
	var i UserDeliveryAddress
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Recipient,
		&i.PhoneNumber,
		&i.PostalCode,
		&i.Prefecture,
		&i.City,
		&i.AddressLine1,
		&i.AddressLine2,
		&i.PreferredDeliveryTime,
		&i.PreferredDeliveryDate,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateUserDeliveryAddress = `-- name: UpdateUserDeliveryAddress :exec
UPDATE user_delivery_address
SET
    recipient = $1,
    phone_number = $2,
    postal_code = $3,
    prefecture = $4,
    city = $5,
    address_line1 = $6,
    address_line2 = $7,
    preferred_delivery_time = $8,
    preferred_delivery_date = $9,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $10
`

type UpdateUserDeliveryAddressParams struct {
	Recipient             string
	PhoneNumber           string
	PostalCode            string
	Prefecture            string
	City                  string
	AddressLine1          string
	AddressLine2          *string
	PreferredDeliveryTime *string
	PreferredDeliveryDate *time.Time
	ID                    int64
}

func (q *Queries) UpdateUserDeliveryAddress(ctx context.Context, arg UpdateUserDeliveryAddressParams) error {
	_, err := q.db.Exec(ctx, updateUserDeliveryAddress,
		arg.Recipient,
		arg.PhoneNumber,
		arg.PostalCode,
		arg.Prefecture,
		arg.City,
		arg.AddressLine1,
		arg.AddressLine2,
		arg.PreferredDeliveryTime,
		arg.PreferredDeliveryDate,
		arg.ID,
	)
	return err
}
