// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: eventcomments.sql

package eventcommentsqueries

import (
	"context"
	"time"
)

const addEventComment = `-- name: AddEventComment :one
WITH inserted AS (INSERT INTO event_comments (event_id, user_id, parent_id, content, vtuber_id) VALUES ($1, $2, $3, $4,
                                                                                                       $5) RETURNING id, user_id, vtuber_id, parent_id, content, event_id, created_at, updated_at)
SELECT inserted.id, inserted.user_id, inserted.vtuber_id, inserted.parent_id, inserted.content, inserted.event_id, inserted.created_at, inserted.updated_at,
       u.full_name     as userName,
       u.id            as userId,
       u.image         as userImage,
       vp.id           as vtuberId,
       vp.display_name as vtuberDisplayName,
       vp.image        as vtuberImage
FROM inserted
         JOIN users u ON inserted.user_id = u.id
         LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
`

type AddEventCommentParams struct {
	EventID  int64
	UserID   int64
	ParentID *int64
	Content  string
	VtuberID *int64
}

type AddEventCommentRow struct {
	ID                int64
	UserID            int64
	VtuberID          *int64
	ParentID          *int64
	Content           string
	EventID           int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	Username          string
	Userid            int64
	Userimage         *string
	Vtuberid          *int64
	Vtuberdisplayname *string
	Vtuberimage       *string
}

func (q *Queries) AddEventComment(ctx context.Context, arg AddEventCommentParams) (AddEventCommentRow, error) {
	row := q.db.QueryRow(ctx, addEventComment,
		arg.EventID,
		arg.UserID,
		arg.ParentID,
		arg.Content,
		arg.VtuberID,
	)
	var i AddEventCommentRow
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.ParentID,
		&i.Content,
		&i.EventID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Username,
		&i.Userid,
		&i.Userimage,
		&i.Vtuberid,
		&i.Vtuberdisplayname,
		&i.Vtuberimage,
	)
	return i, err
}

const deleteEventCommentById = `-- name: DeleteEventCommentById :exec
DELETE
FROM event_comments
WHERE id = $1
`

func (q *Queries) DeleteEventCommentById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteEventCommentById, id)
	return err
}

const getAllEventComments = `-- name: GetAllEventComments :many
WITH FILTERED AS (SELECT ec.id            AS id,
                         ec.user_id       AS user_id,
                         ec.vtuber_id     as comment_vtuber_id,
                         ec.content       AS content,
                         ec.event_id      AS event_id,
                         ec.parent_id     AS parent_id,
                         ec.created_at    AS created_at,
                         ec.updated_at    AS updated_at,
                         u.id             AS user_id_from_users,
                         u.full_name      AS full_name,
                         u.image          AS image,
                         vp.id            AS vtuber_id,
                         vp.display_name  AS vtuber_display_name,
                         vp.image         AS vtuber_image,
                         COUNT(ve.id) > 0 AS has_replies
                  FROM event_comments AS ec
                           LEFT JOIN
                       event_comments AS ve ON ve.parent_id = ec.id
                           INNER JOIN
                       users AS u ON u.id = ec.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ec.event_id = $1
                    AND ec.parent_id IS NULL
                  GROUP BY ec.id, ec.content, ec.event_id, ec.parent_id, ec.created_at, ec.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.user_id, f.comment_vtuber_id, f.content, f.event_id, f.parent_id, f.created_at, f.updated_at, f.user_id_from_users, f.full_name, f.image, f.vtuber_id, f.vtuber_display_name, f.vtuber_image, f.has_replies, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'ASC' THEN f.id END,
         CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT $5 OFFSET $4
`

type GetAllEventCommentsParams struct {
	EventID int64
	Sort    string
	Order   string
	Offset  int32
	Limit   int32
}

type GetAllEventCommentsRow struct {
	ID                int64
	UserID            int64
	CommentVtuberID   *int64
	Content           string
	EventID           int64
	ParentID          *int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	UserIDFromUsers   int64
	FullName          string
	Image             *string
	VtuberID          *int64
	VtuberDisplayName *string
	VtuberImage       *string
	HasReplies        bool
	Total             int64
}

func (q *Queries) GetAllEventComments(ctx context.Context, arg GetAllEventCommentsParams) ([]GetAllEventCommentsRow, error) {
	rows, err := q.db.Query(ctx, getAllEventComments,
		arg.EventID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllEventCommentsRow
	for rows.Next() {
		var i GetAllEventCommentsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.CommentVtuberID,
			&i.Content,
			&i.EventID,
			&i.ParentID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserIDFromUsers,
			&i.FullName,
			&i.Image,
			&i.VtuberID,
			&i.VtuberDisplayName,
			&i.VtuberImage,
			&i.HasReplies,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllRepliesOfEventComment = `-- name: GetAllRepliesOfEventComment :many
WITH FILTERED AS (SELECT ec.id            AS id,
                         ec.user_id       AS user_id,
                         ec.vtuber_id     as comment_vtuber_id,
                         ec.content       AS content,
                         ec.event_id      AS event_id,
                         ec.parent_id     AS parent_id,
                         ec.created_at    AS created_at,
                         ec.updated_at    AS updated_at,
                         u.id             AS user_id_from_users,
                         u.full_name      AS full_name,
                         u.image          AS image,
                         vp.id            AS vtuber_id,
                         vp.display_name  AS vtuber_display_name,
                         vp.image         AS vtuber_image,
                         COUNT(ve.id) > 0 AS has_replies
                  FROM event_comments AS ec
                           LEFT JOIN event_comments AS ve ON ve.parent_id = ec.id
                           INNER JOIN users AS u ON u.id = ec.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ec.parent_id = $1
                  GROUP BY ec.id, ec.content, ec.event_id, ec.parent_id, ec.created_at, ec.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.user_id, f.comment_vtuber_id, f.content, f.event_id, f.parent_id, f.created_at, f.updated_at, f.user_id_from_users, f.full_name, f.image, f.vtuber_id, f.vtuber_display_name, f.vtuber_image, f.has_replies, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'ASC' THEN f.id END,
         CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT $5 OFFSET $4
`

type GetAllRepliesOfEventCommentParams struct {
	ParentID *int64
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
}

type GetAllRepliesOfEventCommentRow struct {
	ID                int64
	UserID            int64
	CommentVtuberID   *int64
	Content           string
	EventID           int64
	ParentID          *int64
	CreatedAt         time.Time
	UpdatedAt         time.Time
	UserIDFromUsers   int64
	FullName          string
	Image             *string
	VtuberID          *int64
	VtuberDisplayName *string
	VtuberImage       *string
	HasReplies        bool
	Total             int64
}

func (q *Queries) GetAllRepliesOfEventComment(ctx context.Context, arg GetAllRepliesOfEventCommentParams) ([]GetAllRepliesOfEventCommentRow, error) {
	rows, err := q.db.Query(ctx, getAllRepliesOfEventComment,
		arg.ParentID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllRepliesOfEventCommentRow
	for rows.Next() {
		var i GetAllRepliesOfEventCommentRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.CommentVtuberID,
			&i.Content,
			&i.EventID,
			&i.ParentID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.UserIDFromUsers,
			&i.FullName,
			&i.Image,
			&i.VtuberID,
			&i.VtuberDisplayName,
			&i.VtuberImage,
			&i.HasReplies,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventCommentById = `-- name: GetEventCommentById :one
SELECT id, user_id, vtuber_id, parent_id, content, event_id, created_at, updated_at
FROM event_comments
WHERE id = $1
`

func (q *Queries) GetEventCommentById(ctx context.Context, id int64) (EventComment, error) {
	row := q.db.QueryRow(ctx, getEventCommentById, id)
	var i EventComment
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.ParentID,
		&i.Content,
		&i.EventID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updateEventCommentById = `-- name: UpdateEventCommentById :exec
UPDATE event_comments
SET content = $1
WHERE id = $2
`

type UpdateEventCommentByIdParams struct {
	Content string
	ID      int64
}

func (q *Queries) UpdateEventCommentById(ctx context.Context, arg UpdateEventCommentByIdParams) error {
	_, err := q.db.Exec(ctx, updateEventCommentById, arg.Content, arg.ID)
	return err
}
