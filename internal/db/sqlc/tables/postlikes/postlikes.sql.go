// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: postlikes.sql

package postlikesqueries

import (
	"context"
	"time"
)

const addPostLike = `-- name: AddPostLike :one
INSERT INTO post_likes (post_id, user_id) VALUES
($1, $2)
RETURNING id, post_id, user_id, created_at, updated_at
`

type AddPostLikeParams struct {
	PostID int64
	UserID int64
}

func (q *Queries) AddPostLike(ctx context.Context, arg AddPostLikeParams) (PostLike, error) {
	row := q.db.QueryRow(ctx, addPostLike, arg.PostID, arg.UserID)
	var i PostLike
	err := row.Scan(
		&i.ID,
		&i.PostID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deletePostLikeByPostId = `-- name: DeletePostLikeByPostId :exec
DELETE FROM post_likes WHERE post_id = $1 AND user_id = $2
`

type DeletePostLikeByPostIdParams struct {
	PostID int64
	UserID int64
}

func (q *Queries) DeletePostLikeByPostId(ctx context.Context, arg DeletePostLikeByPostIdParams) error {
	_, err := q.db.Exec(ctx, deletePostLikeByPostId, arg.PostID, arg.UserID)
	return err
}

const getPostLikeByUserIdAndPostId = `-- name: GetPostLikeByUserIdAndPostId :one
SELECT id, post_id, user_id, created_at, updated_at FROM post_likes WHERE user_id = $1 AND post_id = $2
`

type GetPostLikeByUserIdAndPostIdParams struct {
	UserID int64
	PostID int64
}

func (q *Queries) GetPostLikeByUserIdAndPostId(ctx context.Context, arg GetPostLikeByUserIdAndPostIdParams) (PostLike, error) {
	row := q.db.QueryRow(ctx, getPostLikeByUserIdAndPostId, arg.UserID, arg.PostID)
	var i PostLike
	err := row.Scan(
		&i.ID,
		&i.PostID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPostLikeCount = `-- name: GetPostLikeCount :one
SELECT COUNT(id) FROM post_likes WHERE post_id = $1
`

func (q *Queries) GetPostLikeCount(ctx context.Context, postID int64) (int64, error) {
	row := q.db.QueryRow(ctx, getPostLikeCount, postID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const getPostLikesOfUser = `-- name: GetPostLikesOfUser :many
WITH FILTERED AS (SELECT id, post_id, user_id, created_at, updated_at
   FROM post_likes
    WHERE user_id = $1
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.id, f.post_id, f.user_id, f.created_at, f.updated_at, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $2::TEXT = 'id' AND $3::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN $2::TEXT = 'user_id' AND $3::TEXT = 'ASC' THEN user_id END ASC,
      CASE WHEN $2::TEXT = 'user_id' AND $3::TEXT = 'DESC' THEN user_id END DESC,
      CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN $2::TEXT = 'updated_at' AND $3::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT $5 OFFSET $4
`

type GetPostLikesOfUserParams struct {
	UserID int64
	Sort   string
	Order  string
	Offset int32
	Limit  int32
}

type GetPostLikesOfUserRow struct {
	ID        int64
	PostID    int64
	UserID    int64
	CreatedAt time.Time
	UpdatedAt time.Time
	Total     int64
}

func (q *Queries) GetPostLikesOfUser(ctx context.Context, arg GetPostLikesOfUserParams) ([]GetPostLikesOfUserRow, error) {
	rows, err := q.db.Query(ctx, getPostLikesOfUser,
		arg.UserID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetPostLikesOfUserRow
	for rows.Next() {
		var i GetPostLikesOfUserRow
		if err := rows.Scan(
			&i.ID,
			&i.PostID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
