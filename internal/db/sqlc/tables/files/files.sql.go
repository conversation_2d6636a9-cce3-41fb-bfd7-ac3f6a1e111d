// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: files.sql

package filesqueries

import (
	"context"
)

const addFile = `-- name: AddFile :one
INSERT INTO files (file_path, mime_type, size)
VALUES ($1, $2, $3)
RETURNING id, file_path, mime_type, created_at, size
`

type AddFileParams struct {
	FilePath string
	MimeType string
	Size     int64
}

func (q *Queries) AddFile(ctx context.Context, arg AddFileParams) (File, error) {
	row := q.db.QueryRow(ctx, addFile, arg.FilePath, arg.MimeType, arg.Size)
	var i File
	err := row.Scan(
		&i.ID,
		&i.FilePath,
		&i.MimeType,
		&i.CreatedAt,
		&i.Size,
	)
	return i, err
}

const deleteFile = `-- name: DeleteFile :exec
DELETE FROM files
WHERE file_path = $1
`

func (q *Queries) DeleteFile(ctx context.Context, filePath string) error {
	_, err := q.db.Exec(ctx, deleteFile, filePath)
	return err
}

const getFileByPath = `-- name: GetFileByPath :one
SELECT id, file_path, mime_type, created_at, size FROM files
WHERE file_path = $1
`

func (q *Queries) GetFileByPath(ctx context.Context, filePath string) (File, error) {
	row := q.db.QueryRow(ctx, getFileByPath, filePath)
	var i File
	err := row.Scan(
		&i.ID,
		&i.FilePath,
		&i.MimeType,
		&i.CreatedAt,
		&i.Size,
	)
	return i, err
}
