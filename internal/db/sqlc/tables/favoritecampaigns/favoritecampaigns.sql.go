// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: favoritecampaigns.sql

package favoritecampaignsqueries

import (
	"context"
	"time"
)

const addFavoriteCampaign = `-- name: AddFavoriteCampaign :one
INSERT INTO favorite_campaigns (campaign_id, user_id) VALUES ($1, $2) RETURNING id, campaign_id, user_id, created_at, updated_at
`

type AddFavoriteCampaignParams struct {
	CampaignID int64
	UserID     int64
}

func (q *Queries) AddFavoriteCampaign(ctx context.Context, arg AddFavoriteCampaignParams) (FavoriteCampaign, error) {
	row := q.db.QueryRow(ctx, addFavoriteCampaign, arg.CampaignID, arg.UserID)
	var i FavoriteCampaign
	err := row.Scan(
		&i.ID,
		&i.CampaignID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const countFavoriteCampaign = `-- name: CountFavoriteCampaign :one
SELECT COUNT(id) FROM favorite_campaigns WHERE campaign_id = $1
`

func (q *Queries) CountFavoriteCampaign(ctx context.Context, campaignID int64) (int64, error) {
	row := q.db.QueryRow(ctx, countFavoriteCampaign, campaignID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const deleteFavoriteCampaign = `-- name: DeleteFavoriteCampaign :exec
DELETE FROM favorite_campaigns WHERE campaign_id = $1 AND user_id = $2
`

type DeleteFavoriteCampaignParams struct {
	CampaignID int64
	UserID     int64
}

func (q *Queries) DeleteFavoriteCampaign(ctx context.Context, arg DeleteFavoriteCampaignParams) error {
	_, err := q.db.Exec(ctx, deleteFavoriteCampaign, arg.CampaignID, arg.UserID)
	return err
}

const getFavoriteCampaign = `-- name: GetFavoriteCampaign :many
WITH FILTERED AS (SELECT fc.id, fc.campaign_id, fc.user_id, fc.created_at, fc.updated_at, c.name, c.thumbnail, c.short_description FROM favorite_campaigns fc INNER JOIN campaigns c ON fc.campaign_id = c.id WHERE fc.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.campaign_id, f.user_id, f.created_at, f.updated_at, f.name, f.thumbnail, f.short_description, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT $3 OFFSET $2
`

type GetFavoriteCampaignParams struct {
	UserID int64
	Offset int32
	Limit  int32
}

type GetFavoriteCampaignRow struct {
	ID               int64
	CampaignID       int64
	UserID           int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	Name             string
	Thumbnail        string
	ShortDescription string
	Total            int64
}

func (q *Queries) GetFavoriteCampaign(ctx context.Context, arg GetFavoriteCampaignParams) ([]GetFavoriteCampaignRow, error) {
	rows, err := q.db.Query(ctx, getFavoriteCampaign, arg.UserID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetFavoriteCampaignRow
	for rows.Next() {
		var i GetFavoriteCampaignRow
		if err := rows.Scan(
			&i.ID,
			&i.CampaignID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Name,
			&i.Thumbnail,
			&i.ShortDescription,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFavoriteCampaignByUserIdAndCampaignId = `-- name: GetFavoriteCampaignByUserIdAndCampaignId :one
SELECT id, campaign_id, user_id, created_at, updated_at FROM favorite_campaigns WHERE user_id = $1 AND campaign_id = $2
`

type GetFavoriteCampaignByUserIdAndCampaignIdParams struct {
	UserID     int64
	CampaignID int64
}

func (q *Queries) GetFavoriteCampaignByUserIdAndCampaignId(ctx context.Context, arg GetFavoriteCampaignByUserIdAndCampaignIdParams) (FavoriteCampaign, error) {
	row := q.db.QueryRow(ctx, getFavoriteCampaignByUserIdAndCampaignId, arg.UserID, arg.CampaignID)
	var i FavoriteCampaign
	err := row.Scan(
		&i.ID,
		&i.CampaignID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const popularCampaign = `-- name: PopularCampaign :one
SELECT COUNT(fc.id) as popular, fc.campaign_id FROM favorite_campaigns fc
INNER JOIN campaigns c ON c.id = fc.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY campaign_id ORDER BY popular DESC LIMIT 1
`

type PopularCampaignRow struct {
	Popular    int64
	CampaignID int64
}

func (q *Queries) PopularCampaign(ctx context.Context) (PopularCampaignRow, error) {
	row := q.db.QueryRow(ctx, popularCampaign)
	var i PopularCampaignRow
	err := row.Scan(&i.Popular, &i.CampaignID)
	return i, err
}
