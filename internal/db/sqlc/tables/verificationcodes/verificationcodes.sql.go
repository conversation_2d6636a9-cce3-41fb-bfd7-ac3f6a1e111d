// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: verificationcodes.sql

package verificationcodesqueries

import (
	"context"
	"time"
)

const createVerificationCode = `-- name: CreateVerificationCode :one
INSERT INTO verification_codes (user_id, code, type, email, expires_at)
VALUES ($1, $2, $3, $4, $5) RETURNING id, user_id, code, email, expires_at, created_at, updated_at, type
`

type CreateVerificationCodeParams struct {
	UserID    *int64
	Code      string
	Type      VerificationCodeType
	Email     *string
	ExpiresAt time.Time
}

func (q *Queries) CreateVerificationCode(ctx context.Context, arg CreateVerificationCodeParams) (VerificationCode, error) {
	row := q.db.QueryRow(ctx, createVerificationCode,
		arg.UserID,
		arg.Code,
		arg.Type,
		arg.Email,
		arg.ExpiresAt,
	)
	var i VerificationCode
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Code,
		&i.Email,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Type,
	)
	return i, err
}

const deleteVerificationCode = `-- name: DeleteVerificationCode :exec
DELETE FROM verification_codes WHERE id = $1
`

func (q *Queries) DeleteVerificationCode(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVerificationCode, id)
	return err
}

const getEmailChangeVerificationCode = `-- name: GetEmailChangeVerificationCode :one
SELECT id, user_id, code, email, expires_at, created_at, updated_at, type FROM verification_codes WHERE user_id = $1 AND type = $2
`

type GetEmailChangeVerificationCodeParams struct {
	UserID *int64
	Type   VerificationCodeType
}

func (q *Queries) GetEmailChangeVerificationCode(ctx context.Context, arg GetEmailChangeVerificationCodeParams) (VerificationCode, error) {
	row := q.db.QueryRow(ctx, getEmailChangeVerificationCode, arg.UserID, arg.Type)
	var i VerificationCode
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Code,
		&i.Email,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Type,
	)
	return i, err
}

const getVerificationCode = `-- name: GetVerificationCode :one
SELECT id, user_id, code, email, expires_at, created_at, updated_at, type FROM verification_codes WHERE  
($2::BIGINT IS NULL OR user_id = $2::BIGINT)
AND ($3::TEXT IS NULL OR email = $3::TEXT)
AND type = $1
`

type GetVerificationCodeParams struct {
	Type   VerificationCodeType
	UserID *int64
	Email  *string
}

func (q *Queries) GetVerificationCode(ctx context.Context, arg GetVerificationCodeParams) (VerificationCode, error) {
	row := q.db.QueryRow(ctx, getVerificationCode, arg.Type, arg.UserID, arg.Email)
	var i VerificationCode
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Code,
		&i.Email,
		&i.ExpiresAt,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Type,
	)
	return i, err
}

const updateVerificationCode = `-- name: UpdateVerificationCode :exec
UPDATE verification_codes
SET code = $1, expires_at = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING id, user_id, code, email, expires_at, created_at, updated_at, type
`

type UpdateVerificationCodeParams struct {
	Code      string
	ExpiresAt time.Time
	ID        int64
}

func (q *Queries) UpdateVerificationCode(ctx context.Context, arg UpdateVerificationCodeParams) error {
	_, err := q.db.Exec(ctx, updateVerificationCode, arg.Code, arg.ExpiresAt, arg.ID)
	return err
}
