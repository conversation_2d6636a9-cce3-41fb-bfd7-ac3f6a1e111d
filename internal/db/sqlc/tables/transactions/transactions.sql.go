// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: transactions.sql

package transactionsqueries

import (
	"context"
	"time"
)

const addTransaction = `-- name: AddTransaction :one
INSERT INTO transactions  (user_id, amount, type, status, gmo_order_id, vtuber_id,details)
VALUES ($1, $2, $3, $4, $5, $6,$7)
RETURNING id, user_id, amount, type, status, details, gmo_order_id, vtuber_id, created_at, updated_at
`

type AddTransactionParams struct {
	UserID     int64
	Amount     int32
	Type       TransactionType
	Status     TransactionStatus
	GmoOrderID string
	VtuberID   *int64
	Details    []byte
}

func (q *Queries) AddTransaction(ctx context.Context, arg AddTransactionParams) (Transaction, error) {
	row := q.db.QueryRow(ctx, addTransaction,
		arg.UserID,
		arg.Amount,
		arg.Type,
		arg.Status,
		arg.GmoOrderID,
		arg.VtuberID,
		arg.Details,
	)
	var i Transaction
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Type,
		&i.Status,
		&i.Details,
		&i.GmoOrderID,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const checkGmoOrderIdExist = `-- name: CheckGmoOrderIdExist :one
SELECT id, user_id, amount, type, status, details, gmo_order_id, vtuber_id, created_at, updated_at FROM transactions WHERE gmo_order_id = $1
`

func (q *Queries) CheckGmoOrderIdExist(ctx context.Context, gmoOrderID string) (Transaction, error) {
	row := q.db.QueryRow(ctx, checkGmoOrderIdExist, gmoOrderID)
	var i Transaction
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Type,
		&i.Status,
		&i.Details,
		&i.GmoOrderID,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getAllTransactions = `-- name: GetAllTransactions :many
WITH FILTERED AS (SELECT id, user_id, amount, type, status, details, gmo_order_id, vtuber_id, created_at, updated_at
FROM
    transactions
WHERE
    (($3::bigint IS NULL) OR user_id = $3::bigint)
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.id, f.user_id, f.amount, f.type, f.status, f.details, f.gmo_order_id, f.vtuber_id, f.created_at, f.updated_at, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT $2 OFFSET $1
`

type GetAllTransactionsParams struct {
	Offset int32
	Limit  int32
	UserID *int64
}

type GetAllTransactionsRow struct {
	ID         int64
	UserID     int64
	Amount     int32
	Type       TransactionType
	Status     TransactionStatus
	Details    []byte
	GmoOrderID string
	VtuberID   *int64
	CreatedAt  time.Time
	UpdatedAt  time.Time
	Total      int64
}

func (q *Queries) GetAllTransactions(ctx context.Context, arg GetAllTransactionsParams) ([]GetAllTransactionsRow, error) {
	rows, err := q.db.Query(ctx, getAllTransactions, arg.Offset, arg.Limit, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllTransactionsRow
	for rows.Next() {
		var i GetAllTransactionsRow
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.Amount,
			&i.Type,
			&i.Status,
			&i.Details,
			&i.GmoOrderID,
			&i.VtuberID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTransactionByID = `-- name: GetTransactionByID :one
SELECT id, user_id, amount, type, status, details, gmo_order_id, vtuber_id, created_at, updated_at FROM transactions WHERE id = $1
`

func (q *Queries) GetTransactionByID(ctx context.Context, id int64) (Transaction, error) {
	row := q.db.QueryRow(ctx, getTransactionByID, id)
	var i Transaction
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.Amount,
		&i.Type,
		&i.Status,
		&i.Details,
		&i.GmoOrderID,
		&i.VtuberID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
