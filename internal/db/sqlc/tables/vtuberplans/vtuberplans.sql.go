// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtuberplans.sql

package vtuberplansqueries

import (
	"context"
	"time"
)

const countVtuberPlans = `-- name: CountVtuberPlans :one
SELECT COUNT(id) FROM vtuber_plans WHERE vtuber_id = $1 AND deleted_at IS NULL
`

func (q *Queries) CountVtuberPlans(ctx context.Context, vtuberID int64) (int64, error) {
	row := q.db.QueryRow(ctx, countVtuberPlans, vtuberID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const createVtuberPlan = `-- name: CreateVtuberPlan :one
INSERT INTO vtuber_plans
    (vtuber_id, description, title, price, index, short_description, annual_price)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING id, vtuber_id, description, title, short_description, price, index, created_at, updated_at, deleted_at, annual_price
`

type CreateVtuberPlanParams struct {
	VtuberID         int64
	Description      string
	Title            string
	Price            int32
	Index            int32
	ShortDescription string
	AnnualPrice      int32
}

func (q *Queries) CreateVtuberPlan(ctx context.Context, arg CreateVtuberPlanParams) (VtuberPlan, error) {
	row := q.db.QueryRow(ctx, createVtuberPlan,
		arg.VtuberID,
		arg.Description,
		arg.Title,
		arg.Price,
		arg.Index,
		arg.ShortDescription,
		arg.AnnualPrice,
	)
	var i VtuberPlan
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Description,
		&i.Title,
		&i.ShortDescription,
		&i.Price,
		&i.Index,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.AnnualPrice,
	)
	return i, err
}

const deleteVtuberPlan = `-- name: DeleteVtuberPlan :exec
UPDATE vtuber_plans
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeleteVtuberPlan(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberPlan, id)
	return err
}

const findVtuberPlanById = `-- name: FindVtuberPlanById :one
SELECT id, vtuber_id, description, title, short_description, price, index, created_at, updated_at, deleted_at, annual_price
FROM vtuber_plans
WHERE id = $1
  AND deleted_at IS NULL
`

func (q *Queries) FindVtuberPlanById(ctx context.Context, id int64) (VtuberPlan, error) {
	row := q.db.QueryRow(ctx, findVtuberPlanById, id)
	var i VtuberPlan
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Description,
		&i.Title,
		&i.ShortDescription,
		&i.Price,
		&i.Index,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.AnnualPrice,
	)
	return i, err
}

const getVtuberPlanById = `-- name: GetVtuberPlanById :one
SELECT cs.id, cs.vtuber_id, cs.description, cs.title, cs.short_description, cs.price, cs.index, cs.created_at, cs.updated_at, cs.deleted_at, cs.annual_price,
       CASE
           WHEN $2::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM vtuber_user_subscriptions cus
                        WHERE cus.vtuber_plan_id = cs.id AND cus.deleted_at IS NULL
                          AND cus.user_id = $2::BIGINT)
               THEN TRUE
           ELSE FALSE
           END AS subscribed
FROM vtuber_plans cs
WHERE cs.id = $1
  AND cs.deleted_at IS NULL
`

type GetVtuberPlanByIdParams struct {
	ID     int64
	UserID *int64
}

type GetVtuberPlanByIdRow struct {
	ID               int64
	VtuberID         int64
	Description      string
	Title            string
	ShortDescription string
	Price            int32
	Index            int32
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	AnnualPrice      int32
	Subscribed       bool
}

func (q *Queries) GetVtuberPlanById(ctx context.Context, arg GetVtuberPlanByIdParams) (GetVtuberPlanByIdRow, error) {
	row := q.db.QueryRow(ctx, getVtuberPlanById, arg.ID, arg.UserID)
	var i GetVtuberPlanByIdRow
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Description,
		&i.Title,
		&i.ShortDescription,
		&i.Price,
		&i.Index,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.AnnualPrice,
		&i.Subscribed,
	)
	return i, err
}

const getVtuberPlanByIndexAndVtuberId = `-- name: GetVtuberPlanByIndexAndVtuberId :one
SELECT id, vtuber_id, description, title, short_description, price, index, created_at, updated_at, deleted_at, annual_price
FROM vtuber_plans
WHERE vtuber_id = $1
  AND index = $2
  AND deleted_at IS NULL
`

type GetVtuberPlanByIndexAndVtuberIdParams struct {
	VtuberID int64
	Index    int32
}

func (q *Queries) GetVtuberPlanByIndexAndVtuberId(ctx context.Context, arg GetVtuberPlanByIndexAndVtuberIdParams) (VtuberPlan, error) {
	row := q.db.QueryRow(ctx, getVtuberPlanByIndexAndVtuberId, arg.VtuberID, arg.Index)
	var i VtuberPlan
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.Description,
		&i.Title,
		&i.ShortDescription,
		&i.Price,
		&i.Index,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.AnnualPrice,
	)
	return i, err
}

const getVtuberPlansByVtuber = `-- name: GetVtuberPlansByVtuber :many
SELECT cs.id, cs.vtuber_id, cs.description, cs.title, cs.short_description, cs.price, cs.index, cs.created_at, cs.updated_at, cs.deleted_at, cs.annual_price,
       CASE
           WHEN $2::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM vtuber_user_subscriptions cus
                        WHERE cus.vtuber_plan_id = cs.id AND cus.deleted_at IS NULL
                          AND cus.user_id = $2::BIGINT)
               THEN TRUE
           ELSE FALSE
           END AS subscribed
FROM vtuber_plans cs
WHERE cs.vtuber_id = $1
  AND cs.deleted_at IS NULL
ORDER BY cs.index ASC
`

type GetVtuberPlansByVtuberParams struct {
	VtuberID int64
	UserID   *int64
}

type GetVtuberPlansByVtuberRow struct {
	ID               int64
	VtuberID         int64
	Description      string
	Title            string
	ShortDescription string
	Price            int32
	Index            int32
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	AnnualPrice      int32
	Subscribed       bool
}

func (q *Queries) GetVtuberPlansByVtuber(ctx context.Context, arg GetVtuberPlansByVtuberParams) ([]GetVtuberPlansByVtuberRow, error) {
	rows, err := q.db.Query(ctx, getVtuberPlansByVtuber, arg.VtuberID, arg.UserID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetVtuberPlansByVtuberRow
	for rows.Next() {
		var i GetVtuberPlansByVtuberRow
		if err := rows.Scan(
			&i.ID,
			&i.VtuberID,
			&i.Description,
			&i.Title,
			&i.ShortDescription,
			&i.Price,
			&i.Index,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.AnnualPrice,
			&i.Subscribed,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateVtuberPlan = `-- name: UpdateVtuberPlan :exec
UPDATE vtuber_plans
SET description = $1,
    title       = $2,
    price       = $3,
    index       = $4,
    short_description = $5,
    annual_price = $6
WHERE id = $7
`

type UpdateVtuberPlanParams struct {
	Description      string
	Title            string
	Price            int32
	Index            int32
	ShortDescription string
	AnnualPrice      int32
	ID               int64
}

func (q *Queries) UpdateVtuberPlan(ctx context.Context, arg UpdateVtuberPlanParams) error {
	_, err := q.db.Exec(ctx, updateVtuberPlan,
		arg.Description,
		arg.Title,
		arg.Price,
		arg.Index,
		arg.ShortDescription,
		arg.AnnualPrice,
		arg.ID,
	)
	return err
}
