// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtubercategories.sql

package vtubercategoriesqueries

import (
	"context"
)

type AddVtuberCategoriesParams struct {
	VtuberProfileID int64
	CategoryID      int64
}

const deleteVtuberCategories = `-- name: DeleteVtuberCategories :exec
DELETE FROM vtuber_categories WHERE vtuber_profile_id = $1
`

func (q *Queries) DeleteVtuberCategories(ctx context.Context, vtuberProfileID int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberCategories, vtuberProfileID)
	return err
}

const getAllVtuberCategories = `-- name: GetAllVtuberCategories :many
SELECT category_id FROM vtuber_categories 
GROUP BY category_id 
ORDER BY COUNT(category_id) DESC
`

func (q *Queries) GetAllVtuberCategories(ctx context.Context) ([]int64, error) {
	rows, err := q.db.Query(ctx, getAllVtuberCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var category_id int64
		if err := rows.Scan(&category_id); err != nil {
			return nil, err
		}
		items = append(items, category_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
