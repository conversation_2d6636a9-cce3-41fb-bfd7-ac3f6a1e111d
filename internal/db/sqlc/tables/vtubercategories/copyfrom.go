// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package vtubercategoriesqueries

import (
	"context"
)

// iteratorForAddVtuberCategories implements pgx.CopyFromSource.
type iteratorForAddVtuberCategories struct {
	rows                 []AddVtuberCategoriesParams
	skippedFirstNextCall bool
}

func (r *iteratorForAddVtuberCategories) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForAddVtuberCategories) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].VtuberProfileID,
		r.rows[0].CategoryID,
	}, nil
}

func (r iteratorForAddVtuberCategories) Err() error {
	return nil
}

func (q *Queries) AddVtuberCategories(ctx context.Context, arg []AddVtuberCategoriesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"vtuber_categories"}, []string{"vtuber_profile_id", "category_id"}, &iteratorForAddVtuberCategories{rows: arg})
}
