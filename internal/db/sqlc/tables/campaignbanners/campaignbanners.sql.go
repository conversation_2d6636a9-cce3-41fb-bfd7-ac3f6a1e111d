// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaignbanners.sql

package campaignbannersqueries

import (
	"context"
)

const addCampaignBanner = `-- name: AddCampaignBanner :one
INSERT INTO campaign_banners (image, index, campaign_id)
VALUES ($1, $2, $3) RETURNING id, image, index, campaign_id, created_at, updated_at
`

type AddCampaignBannerParams struct {
	Image      string
	Index      int32
	CampaignID int64
}

func (q *Queries) AddCampaignBanner(ctx context.Context, arg AddCampaignBannerParams) (CampaignBanner, error) {
	row := q.db.QueryRow(ctx, addCampaignBanner, arg.Image, arg.Index, arg.CampaignID)
	var i CampaignBanner
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Index,
		&i.CampaignID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const deleteCampaignBannerById = `-- name: DeleteCampaignBannerById :exec
DELETE FROM campaign_banners WHERE id = $1
`

func (q *Queries) DeleteCampaignBannerById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteCampaignBannerById, id)
	return err
}

const getAllCampaignBanners = `-- name: GetAllCampaignBanners :many
SELECT id, image, index, campaign_id, created_at, updated_at FROM campaign_banners WHERE campaign_id = $1 ORDER BY index
`

func (q *Queries) GetAllCampaignBanners(ctx context.Context, campaignID int64) ([]CampaignBanner, error) {
	rows, err := q.db.Query(ctx, getAllCampaignBanners, campaignID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []CampaignBanner
	for rows.Next() {
		var i CampaignBanner
		if err := rows.Scan(
			&i.ID,
			&i.Image,
			&i.Index,
			&i.CampaignID,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCampaignBannerById = `-- name: GetCampaignBannerById :one
SELECT id, image, index, campaign_id, created_at, updated_at FROM campaign_banners WHERE id = $1
`

func (q *Queries) GetCampaignBannerById(ctx context.Context, id int64) (CampaignBanner, error) {
	row := q.db.QueryRow(ctx, getCampaignBannerById, id)
	var i CampaignBanner
	err := row.Scan(
		&i.ID,
		&i.Image,
		&i.Index,
		&i.CampaignID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

type InsertManyCampaignBannersParams struct {
	Image      string
	Index      int32
	CampaignID int64
}

const updateCampaignBannerById = `-- name: UpdateCampaignBannerById :exec
UPDATE campaign_banners SET image = $1, index = $2 WHERE id = $3
`

type UpdateCampaignBannerByIdParams struct {
	Image string
	Index int32
	ID    int64
}

func (q *Queries) UpdateCampaignBannerById(ctx context.Context, arg UpdateCampaignBannerByIdParams) error {
	_, err := q.db.Exec(ctx, updateCampaignBannerById, arg.Image, arg.Index, arg.ID)
	return err
}
