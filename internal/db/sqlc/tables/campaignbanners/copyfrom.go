// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package campaignbannersqueries

import (
	"context"
)

// iteratorForInsertManyCampaignBanners implements pgx.CopyFromSource.
type iteratorForInsertManyCampaignBanners struct {
	rows                 []InsertManyCampaignBannersParams
	skippedFirstNextCall bool
}

func (r *iteratorForInsertManyCampaignBanners) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForInsertManyCampaignBanners) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].Image,
		r.rows[0].Index,
		r.rows[0].CampaignID,
	}, nil
}

func (r iteratorForInsertManyCampaignBanners) Err() error {
	return nil
}

func (q *Queries) InsertManyCampaignBanners(ctx context.Context, arg []InsertManyCampaignBannersParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"campaign_banners"}, []string{"image", "index", "campaign_id"}, &iteratorForInsertManyCampaignBanners{rows: arg})
}
