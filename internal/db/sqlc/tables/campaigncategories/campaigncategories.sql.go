// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: campaigncategories.sql

package campaigncategoriesqueries

import (
	"context"
)

type AddCampaignCategoriesParams struct {
	CampaignID int64
	CategoryID int64
}

const deleteCampaignCategories = `-- name: DeleteCampaignCategories :exec
DELETE FROM campaign_categories WHERE campaign_id = $1
`

func (q *Queries) DeleteCampaignCategories(ctx context.Context, campaignID int64) error {
	_, err := q.db.Exec(ctx, deleteCampaignCategories, campaignID)
	return err
}
