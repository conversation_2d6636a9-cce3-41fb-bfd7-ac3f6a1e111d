// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package campaigncategoriesqueries

import (
	"context"
)

// iteratorForAddCampaignCategories implements pgx.CopyFromSource.
type iteratorForAddCampaignCategories struct {
	rows                 []AddCampaignCategoriesParams
	skippedFirstNextCall bool
}

func (r *iteratorForAddCampaignCategories) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForAddCampaignCategories) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].CampaignID,
		r.rows[0].CategoryID,
	}, nil
}

func (r iteratorForAddCampaignCategories) Err() error {
	return nil
}

func (q *Queries) AddCampaignCategories(ctx context.Context, arg []AddCampaignCategoriesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"campaign_categories"}, []string{"campaign_id", "category_id"}, &iteratorForAddCampaignCategories{rows: arg})
}
