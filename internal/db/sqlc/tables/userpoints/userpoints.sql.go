// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: userpoints.sql

package userpointsqueries

import (
	"context"
)

const addUserPoints = `-- name: AddUserPoints :one
INSERT INTO user_points (user_id,transaction_id,points,remarks)
VALUES ($1,$2,$3,$4) RETURNING id, user_id, transaction_id, points, remarks, created_at, updated_at
`

type AddUserPointsParams struct {
	UserID        int64
	TransactionID *int64
	Points        float64
	Remarks       []byte
}

func (q *Queries) AddUserPoints(ctx context.Context, arg AddUserPointsParams) (UserPoint, error) {
	row := q.db.QueryRow(ctx, addUserPoints,
		arg.UserID,
		arg.TransactionID,
		arg.Points,
		arg.Remarks,
	)
	var i UserPoint
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.TransactionID,
		&i.Points,
		&i.Remarks,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getUserPoints = `-- name: GetUserPoints :one
SELECT  COALESCE(SUM(points),0)::FLOAT  FROM user_points WHERE user_id=$1
`

func (q *Queries) GetUserPoints(ctx context.Context, userID int64) (float64, error) {
	row := q.db.QueryRow(ctx, getUserPoints, userID)
	var column_1 float64
	err := row.Scan(&column_1)
	return column_1, err
}
