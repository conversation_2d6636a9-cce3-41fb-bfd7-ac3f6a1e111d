// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: favoritevtubers.sql

package favoritevtubersqueries

import (
	"context"
	"time"
)

const addFavoriteVtuber = `-- name: AddFavoriteVtuber :one
WITH inserted AS ( INSERT INTO favorite_vtubers (vtuber_id, user_id) VALUES ($1, $2) RETURNING id, vtuber_id, user_id, created_at, updated_at) 
SELECT inserted.id, inserted.vtuber_id, inserted.user_id, inserted.created_at, inserted.updated_at, vp.username FROM inserted INNER JOIN vtuber_profiles vp ON inserted.vtuber_id = vp.id
`

type AddFavoriteVtuberParams struct {
	VtuberID int64
	UserID   int64
}

type AddFavoriteVtuberRow struct {
	ID        int64
	VtuberID  int64
	UserID    int64
	CreatedAt time.Time
	UpdatedAt time.Time
	Username  string
}

func (q *Queries) AddFavoriteVtuber(ctx context.Context, arg AddFavoriteVtuberParams) (AddFavoriteVtuberRow, error) {
	row := q.db.QueryRow(ctx, addFavoriteVtuber, arg.VtuberID, arg.UserID)
	var i AddFavoriteVtuberRow
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Username,
	)
	return i, err
}

const deleteFavoriteVtuber = `-- name: DeleteFavoriteVtuber :exec
DELETE FROM favorite_vtubers WHERE vtuber_id = $1 AND user_id = $2
`

type DeleteFavoriteVtuberParams struct {
	VtuberID int64
	UserID   int64
}

func (q *Queries) DeleteFavoriteVtuber(ctx context.Context, arg DeleteFavoriteVtuberParams) error {
	_, err := q.db.Exec(ctx, deleteFavoriteVtuber, arg.VtuberID, arg.UserID)
	return err
}

const getFavoriteVtuber = `-- name: GetFavoriteVtuber :many
WITH FILTERED AS (SELECT fv.id, fv.vtuber_id, fv.user_id, fv.created_at, fv.updated_at, vp.display_name as vtuber_name, vp.image as vtuber_image, vp.username FROM favorite_vtubers fv INNER JOIN vtuber_profiles vp ON fv.vtuber_id = vp.id WHERE fv.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.vtuber_id, f.user_id, f.created_at, f.updated_at, f.vtuber_name, f.vtuber_image, f.username, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT $3 OFFSET $2
`

type GetFavoriteVtuberParams struct {
	UserID int64
	Offset int32
	Limit  int32
}

type GetFavoriteVtuberRow struct {
	ID          int64
	VtuberID    int64
	UserID      int64
	CreatedAt   time.Time
	UpdatedAt   time.Time
	VtuberName  string
	VtuberImage *string
	Username    string
	Total       int64
}

func (q *Queries) GetFavoriteVtuber(ctx context.Context, arg GetFavoriteVtuberParams) ([]GetFavoriteVtuberRow, error) {
	rows, err := q.db.Query(ctx, getFavoriteVtuber, arg.UserID, arg.Offset, arg.Limit)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetFavoriteVtuberRow
	for rows.Next() {
		var i GetFavoriteVtuberRow
		if err := rows.Scan(
			&i.ID,
			&i.VtuberID,
			&i.UserID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.VtuberName,
			&i.VtuberImage,
			&i.Username,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFavoriteVtuberByUserIdAndVtuberId = `-- name: GetFavoriteVtuberByUserIdAndVtuberId :one
SELECT id, vtuber_id, user_id, created_at, updated_at FROM favorite_vtubers WHERE user_id = $1 AND vtuber_id = $2
`

type GetFavoriteVtuberByUserIdAndVtuberIdParams struct {
	UserID   int64
	VtuberID int64
}

func (q *Queries) GetFavoriteVtuberByUserIdAndVtuberId(ctx context.Context, arg GetFavoriteVtuberByUserIdAndVtuberIdParams) (FavoriteVtuber, error) {
	row := q.db.QueryRow(ctx, getFavoriteVtuberByUserIdAndVtuberId, arg.UserID, arg.VtuberID)
	var i FavoriteVtuber
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.UserID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
