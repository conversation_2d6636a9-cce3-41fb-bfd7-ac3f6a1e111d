// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: events.sql

package eventsqueries

import (
	"context"
	"time"
)

const addEvent = `-- name: AddEvent :one
WITH inserted as (
    INSERT
        INTO events (vtuber_profile_id, user_id, title, description, image, rules, status, start_date,
                     end_date, short_description, participation_flow, benefits, requirements, overview, social_media_links,slug)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) RETURNING id, slug, social_media_links, user_id, vtuber_profile_id, short_description, title, description, image, rules, start_date, end_date, status, participation_flow, benefits, requirements, overview, created_at, updated_at, deleted_at)
SELECT inserted.id,
       inserted.title,
       inserted.description,
       inserted.image,
       inserted.rules,
       inserted.start_date,
       inserted.end_date,
       inserted.created_at,
       inserted.status,
       inserted.short_description,
       inserted.participation_flow,
       inserted.benefits,
       inserted.requirements,
       inserted.overview,
       inserted.social_media_links,
       inserted.slug,
       u.id            as userId,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage
FROM inserted
         LEFT JOIN users u ON inserted.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON inserted.vtuber_profile_id = vp.id
`

type AddEventParams struct {
	VtuberProfileID   *int64
	UserID            int64
	Title             string
	Description       string
	Image             string
	Rules             string
	Status            EventStatus
	StartDate         time.Time
	EndDate           time.Time
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
}

type AddEventRow struct {
	ID                int64
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	CreatedAt         time.Time
	Status            EventStatus
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
	Userid            *int64
	Username          *string
	UserImage         *string
	Vtuberid          *int64
	Vtubername        *string
	Vtuberimage       *string
}

func (q *Queries) AddEvent(ctx context.Context, arg AddEventParams) (AddEventRow, error) {
	row := q.db.QueryRow(ctx, addEvent,
		arg.VtuberProfileID,
		arg.UserID,
		arg.Title,
		arg.Description,
		arg.Image,
		arg.Rules,
		arg.Status,
		arg.StartDate,
		arg.EndDate,
		arg.ShortDescription,
		arg.ParticipationFlow,
		arg.Benefits,
		arg.Requirements,
		arg.Overview,
		arg.SocialMediaLinks,
		arg.Slug,
	)
	var i AddEventRow
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Image,
		&i.Rules,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.Status,
		&i.ShortDescription,
		&i.ParticipationFlow,
		&i.Benefits,
		&i.Requirements,
		&i.Overview,
		&i.SocialMediaLinks,
		&i.Slug,
		&i.Userid,
		&i.Username,
		&i.UserImage,
		&i.Vtuberid,
		&i.Vtubername,
		&i.Vtuberimage,
	)
	return i, err
}

const approveOrRejectEventById = `-- name: ApproveOrRejectEventById :exec
UPDATE events
SET status = $1
WHERE id = $2
`

type ApproveOrRejectEventByIdParams struct {
	Status EventStatus
	ID     int64
}

func (q *Queries) ApproveOrRejectEventById(ctx context.Context, arg ApproveOrRejectEventByIdParams) error {
	_, err := q.db.Exec(ctx, approveOrRejectEventById, arg.Status, arg.ID)
	return err
}

const deleteEventById = `-- name: DeleteEventById :exec
UPDATE events
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeleteEventById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteEventById, id)
	return err
}

const getAllEvents = `-- name: GetAllEvents :many
WITH FILTERED AS (
  SELECT 
    events.id,
    events.title,
    events.description,
    events.image,
    events.rules,
    events.start_date,
    events.end_date,
    events.created_at,
    events.updated_at,
    events.status,
    events.short_description,
    events.participation_flow,
    events.benefits,
    events.requirements,
    events.overview,
    events.social_media_links,
    events.slug,
    u.id AS userId,
    u.full_name AS userName,
    u.image AS user_image,
    vp.id AS vtuberId,
    vp.display_name AS vtuberName,
    vp.image AS vtuberImage,
      CASE 
        WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
    END as category_ids
  FROM events
  LEFT JOIN users u 
    ON events.user_id = u.id
  LEFT JOIN vtuber_profiles vp 
    ON events.vtuber_profile_id = vp.id
  LEFT JOIN event_categories ec
    ON events.id = ec.event_id
  WHERE 
     ($5::BIGINT IS NULL OR events.vtuber_profile_id = $5::BIGINT)
    AND events.deleted_at IS NULL AND ($6::BIGINT IS NULL OR ec.category_id = $6::BIGINT)
    AND ($7::boolean OR events.status = 'approved')
GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
),
COUNTED AS (
  SELECT COUNT(*) AS total
  FROM FILTERED
)
SELECT f.id, f.title, f.description, f.image, f.rules, f.start_date, f.end_date, f.created_at, f.updated_at, f.status, f.short_description, f.participation_flow, f.benefits, f.requirements, f.overview, f.social_media_links, f.slug, f.userid, f.username, f.user_image, f.vtuberid, f.vtubername, f.vtuberimage, f.category_ids, c.total
FROM FILTERED f, COUNTED c
ORDER BY 
  CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END,
  CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
  CASE WHEN $1::TEXT = 'title' AND $2::TEXT = 'ASC' THEN title END,
  CASE WHEN $1::TEXT = 'title' AND $2::TEXT = 'DESC' THEN title END DESC,
  CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END,
  CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
  CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END,
  CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
LIMIT $4 
OFFSET $3
`

type GetAllEventsParams struct {
	Sort       string
	Order      string
	Offset     int32
	Limit      int32
	VtuberID   *int64
	CategoryID *int64
	IsAdmin    bool
}

type GetAllEventsRow struct {
	ID                int64
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	CreatedAt         time.Time
	UpdatedAt         time.Time
	Status            EventStatus
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
	Userid            *int64
	Username          *string
	UserImage         *string
	Vtuberid          *int64
	Vtubername        *string
	Vtuberimage       *string
	CategoryIds       []int64
	Total             int64
}

func (q *Queries) GetAllEvents(ctx context.Context, arg GetAllEventsParams) ([]GetAllEventsRow, error) {
	rows, err := q.db.Query(ctx, getAllEvents,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.VtuberID,
		arg.CategoryID,
		arg.IsAdmin,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllEventsRow
	for rows.Next() {
		var i GetAllEventsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Image,
			&i.Rules,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ShortDescription,
			&i.ParticipationFlow,
			&i.Benefits,
			&i.Requirements,
			&i.Overview,
			&i.SocialMediaLinks,
			&i.Slug,
			&i.Userid,
			&i.Username,
			&i.UserImage,
			&i.Vtuberid,
			&i.Vtubername,
			&i.Vtuberimage,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventById = `-- name: GetEventById :one
SELECT events.id,
       events.title,
       events.description,
       events.image,
       events.rules,
       events.start_date,
       events.end_date,
       events.created_at,
       events.status,
       events.short_description,
       events.participation_flow,
       events.benefits,
       events.requirements,
       events.overview,
       events.social_media_links,
       events.slug,
       u.id            as user_id,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage,
       CASE 
        WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
    END as category_ids
FROM events
         LEFT JOIN users u ON events.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON events.vtuber_profile_id = vp.id
         LEFT JOIN event_categories ec ON events.id = ec.event_id
WHERE (events.id::TEXT = $1::TEXT OR events.slug::TEXt = $1::TEXT) 
AND events.deleted_at IS NULL 
GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
`

type GetEventByIdRow struct {
	ID                int64
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	CreatedAt         time.Time
	Status            EventStatus
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
	UserID            *int64
	Username          *string
	UserImage         *string
	Vtuberid          *int64
	Vtubername        *string
	Vtuberimage       *string
	CategoryIds       []int64
}

func (q *Queries) GetEventById(ctx context.Context, id string) (GetEventByIdRow, error) {
	row := q.db.QueryRow(ctx, getEventById, id)
	var i GetEventByIdRow
	err := row.Scan(
		&i.ID,
		&i.Title,
		&i.Description,
		&i.Image,
		&i.Rules,
		&i.StartDate,
		&i.EndDate,
		&i.CreatedAt,
		&i.Status,
		&i.ShortDescription,
		&i.ParticipationFlow,
		&i.Benefits,
		&i.Requirements,
		&i.Overview,
		&i.SocialMediaLinks,
		&i.Slug,
		&i.UserID,
		&i.Username,
		&i.UserImage,
		&i.Vtuberid,
		&i.Vtubername,
		&i.Vtuberimage,
		&i.CategoryIds,
	)
	return i, err
}

const getEventBySlug = `-- name: GetEventBySlug :one
SELECT id, slug, social_media_links, user_id, vtuber_profile_id, short_description, title, description, image, rules, start_date, end_date, status, participation_flow, benefits, requirements, overview, created_at, updated_at, deleted_at from events
WHERE slug = $1
`

func (q *Queries) GetEventBySlug(ctx context.Context, slug string) (Event, error) {
	row := q.db.QueryRow(ctx, getEventBySlug, slug)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.SocialMediaLinks,
		&i.UserID,
		&i.VtuberProfileID,
		&i.ShortDescription,
		&i.Title,
		&i.Description,
		&i.Image,
		&i.Rules,
		&i.StartDate,
		&i.EndDate,
		&i.Status,
		&i.ParticipationFlow,
		&i.Benefits,
		&i.Requirements,
		&i.Overview,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getMyEvents = `-- name: GetMyEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                            events.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                        END as category_ids
                  FROM events
                           LEFT JOIN users u ON events.user_id = u.id
                           LEFT JOIN vtuber_profiles vp
                                     ON events.vtuber_profile_id = vp.id
                           LEFT JOIN event_categories ec
                            ON events.id = ec.event_id
                  WHERE events.deleted_at IS NULL AND events.vtuber_profile_id = $5::BIGINT 
                  GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
                  ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.title, f.description, f.image, f.rules, f.start_date, f.end_date, f.created_at, f.updated_at, f.status, f.short_description, f.participation_flow, f.benefits, f.requirements, f.overview, f.social_media_links, f.slug, f.userid, f.username, f.user_image, f.vtuberid, f.vtubername, f.vtuberimage, f.category_ids, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END,
         CASE
             WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id
             END
        DESC,
         CASE
             WHEN $1::TEXT = 'title' AND $2::TEXT = 'ASC' THEN title
             END
        ,
         CASE
             WHEN $1::TEXT = 'title' AND $2::TEXT = 'DESC' THEN title
             END
        DESC,
         CASE
             WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT $4 OFFSET $3
`

type GetMyEventsParams struct {
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
	VtuberID int64
}

type GetMyEventsRow struct {
	ID                int64
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	CreatedAt         time.Time
	UpdatedAt         time.Time
	Status            EventStatus
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
	Userid            *int64
	Username          *string
	UserImage         *string
	Vtuberid          *int64
	Vtubername        *string
	Vtuberimage       *string
	CategoryIds       []int64
	Total             int64
}

func (q *Queries) GetMyEvents(ctx context.Context, arg GetMyEventsParams) ([]GetMyEventsRow, error) {
	rows, err := q.db.Query(ctx, getMyEvents,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.VtuberID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetMyEventsRow
	for rows.Next() {
		var i GetMyEventsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Image,
			&i.Rules,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ShortDescription,
			&i.ParticipationFlow,
			&i.Benefits,
			&i.Requirements,
			&i.Overview,
			&i.SocialMediaLinks,
			&i.Slug,
			&i.Userid,
			&i.Username,
			&i.UserImage,
			&i.Vtuberid,
			&i.Vtubername,
			&i.Vtuberimage,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getOneEvent = `-- name: GetOneEvent :one
SELECT id, slug, social_media_links, user_id, vtuber_profile_id, short_description, title, description, image, rules, start_date, end_date, status, participation_flow, benefits, requirements, overview, created_at, updated_at, deleted_at
FROM events
WHERE (id::TEXT = $1::TEXT OR slug::TEXT = $1::TEXT)
`

func (q *Queries) GetOneEvent(ctx context.Context, id string) (Event, error) {
	row := q.db.QueryRow(ctx, getOneEvent, id)
	var i Event
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.SocialMediaLinks,
		&i.UserID,
		&i.VtuberProfileID,
		&i.ShortDescription,
		&i.Title,
		&i.Description,
		&i.Image,
		&i.Rules,
		&i.StartDate,
		&i.EndDate,
		&i.Status,
		&i.ParticipationFlow,
		&i.Benefits,
		&i.Requirements,
		&i.Overview,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getUserEvents = `-- name: GetUserEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                         events.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                          CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                        END as category_ids
                  FROM events
                           INNER JOIN users u ON events.user_id = u.id
                           LEFT JOIN event_categories ec ON events.id = ec.event_id
                  WHERE events.deleted_at IS NULL AND events.user_id = $5::BIGINT
                  
                  ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.title, f.description, f.image, f.rules, f.start_date, f.end_date, f.created_at, f.updated_at, f.status, f.short_description, f.participation_flow, f.benefits, f.requirements, f.overview, f.social_media_links, f.slug, f.userid, f.username, f.user_image, f.category_ids, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
         CASE
             WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT $4 OFFSET $3
`

type GetUserEventsParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
	UserID int64
}

type GetUserEventsRow struct {
	ID                int64
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	CreatedAt         time.Time
	UpdatedAt         time.Time
	Status            EventStatus
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	Slug              string
	Userid            int64
	Username          string
	UserImage         *string
	CategoryIds       []int64
	Total             int64
}

func (q *Queries) GetUserEvents(ctx context.Context, arg GetUserEventsParams) ([]GetUserEventsRow, error) {
	rows, err := q.db.Query(ctx, getUserEvents,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.UserID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetUserEventsRow
	for rows.Next() {
		var i GetUserEventsRow
		if err := rows.Scan(
			&i.ID,
			&i.Title,
			&i.Description,
			&i.Image,
			&i.Rules,
			&i.StartDate,
			&i.EndDate,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Status,
			&i.ShortDescription,
			&i.ParticipationFlow,
			&i.Benefits,
			&i.Requirements,
			&i.Overview,
			&i.SocialMediaLinks,
			&i.Slug,
			&i.Userid,
			&i.Username,
			&i.UserImage,
			&i.CategoryIds,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateEventById = `-- name: UpdateEventById :exec
UPDATE events
SET title             = $1,
    description       = $2,
    image             = $3,
    rules             = $4,
    start_date        = $5,
    end_date          = $6,
    short_description = $7,
    participation_flow = $8,
    benefits          = $9,
    requirements      = $10,
    overview          = $11,
    social_media_links = $12
WHERE id = $13
`

type UpdateEventByIdParams struct {
	Title             string
	Description       string
	Image             string
	Rules             string
	StartDate         time.Time
	EndDate           time.Time
	ShortDescription  string
	ParticipationFlow string
	Benefits          string
	Requirements      string
	Overview          string
	SocialMediaLinks  []byte
	ID                int64
}

func (q *Queries) UpdateEventById(ctx context.Context, arg UpdateEventByIdParams) error {
	_, err := q.db.Exec(ctx, updateEventById,
		arg.Title,
		arg.Description,
		arg.Image,
		arg.Rules,
		arg.StartDate,
		arg.EndDate,
		arg.ShortDescription,
		arg.ParticipationFlow,
		arg.Benefits,
		arg.Requirements,
		arg.Overview,
		arg.SocialMediaLinks,
		arg.ID,
	)
	return err
}
