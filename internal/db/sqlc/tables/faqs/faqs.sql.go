// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: faqs.sql

package faqsqueries

import (
	"context"
)

const createFaq = `-- name: CreateFaq :one
    INSERT INTO faqs ( question, response, index, active, language, tag)
    VALUES ($1, $2, $3, $4, $5, $6) RETURNING id, question, response, index, active, language, created_at, updated_at, tag
`

type CreateFaqParams struct {
	Question string
	Response string
	Index    int32
	Active   bool
	Language string
	Tag      string
}

func (q *Queries) CreateFaq(ctx context.Context, arg CreateFaqParams) (Faq, error) {
	row := q.db.QueryRow(ctx, createFaq,
		arg.Question,
		arg.Response,
		arg.Index,
		arg.Active,
		arg.Language,
		arg.Tag,
	)
	var i Faq
	err := row.Scan(
		&i.ID,
		&i.Question,
		&i.Response,
		&i.Index,
		&i.Active,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Tag,
	)
	return i, err
}

const deleteFaqById = `-- name: DeleteFaqById :exec
DELETE FROM faqs WHERE id = $1
`

func (q *Queries) DeleteFaqById(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteFaqById, id)
	return err
}

const getAllFaqs = `-- name: GetAllFaqs :many
SELECT id, question, response, index, active, language, created_at, updated_at, tag FROM faqs WHERE active= COALESCE($1, active) AND language = COALESCE($2, language) ORDER BY index ASC
`

type GetAllFaqsParams struct {
	IsActive *bool
	Language *string
}

func (q *Queries) GetAllFaqs(ctx context.Context, arg GetAllFaqsParams) ([]Faq, error) {
	rows, err := q.db.Query(ctx, getAllFaqs, arg.IsActive, arg.Language)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Faq
	for rows.Next() {
		var i Faq
		if err := rows.Scan(
			&i.ID,
			&i.Question,
			&i.Response,
			&i.Index,
			&i.Active,
			&i.Language,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.Tag,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getFaqById = `-- name: GetFaqById :one
SELECT id, question, response, index, active, language, created_at, updated_at, tag FROM faqs WHERE id = $1
`

func (q *Queries) GetFaqById(ctx context.Context, id int64) (Faq, error) {
	row := q.db.QueryRow(ctx, getFaqById, id)
	var i Faq
	err := row.Scan(
		&i.ID,
		&i.Question,
		&i.Response,
		&i.Index,
		&i.Active,
		&i.Language,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.Tag,
	)
	return i, err
}

const toggleFaqActive = `-- name: ToggleFaqActive :exec
UPDATE faqs SET active = $1 WHERE id = $2 RETURNING id, question, response, index, active, language, created_at, updated_at, tag
`

type ToggleFaqActiveParams struct {
	Active bool
	ID     int64
}

func (q *Queries) ToggleFaqActive(ctx context.Context, arg ToggleFaqActiveParams) error {
	_, err := q.db.Exec(ctx, toggleFaqActive, arg.Active, arg.ID)
	return err
}

const updateFaqById = `-- name: UpdateFaqById :exec
UPDATE faqs SET question = $1, response = $2, index = $3, language = $4, tag = $5 WHERE id = $6 RETURNING id, question, response, index, active, language, created_at, updated_at, tag
`

type UpdateFaqByIdParams struct {
	Question string
	Response string
	Index    int32
	Language string
	Tag      string
	ID       int64
}

func (q *Queries) UpdateFaqById(ctx context.Context, arg UpdateFaqByIdParams) error {
	_, err := q.db.Exec(ctx, updateFaqById,
		arg.Question,
		arg.Response,
		arg.Index,
		arg.Language,
		arg.Tag,
		arg.ID,
	)
	return err
}
