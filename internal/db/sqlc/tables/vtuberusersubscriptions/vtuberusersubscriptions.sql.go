// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: vtuberusersubscriptions.sql

package vtuberusersubscriptionsqueries

import (
	"context"
	"time"
)

const addVtuberUserSubscription = `-- name: AddVtuberUserSubscription :one
INSERT INTO vtuber_user_subscriptions
(vtuber_id, user_id, vtuber_plan_id, is_recurring, expires_on)
VALUES ($1, $2, $3, $4, $5) RETURNING id, user_id, vtuber_id, vtuber_plan_id, is_recurring, expires_on, created_at, updated_at, deleted_at
`

type AddVtuberUserSubscriptionParams struct {
	VtuberID     int64
	UserID       int64
	VtuberPlanID int64
	IsRecurring  bool
	ExpiresOn    time.Time
}

func (q *Queries) AddVtuberUserSubscription(ctx context.Context, arg AddVtuberUserSubscriptionParams) (VtuberUserSubscription, error) {
	row := q.db.QueryRow(ctx, addVtuberUserSubscription,
		arg.VtuberID,
		arg.UserID,
		arg.VtuberPlanID,
		arg.IsRecurring,
		arg.ExpiresOn,
	)
	var i VtuberUserSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.VtuberPlanID,
		&i.IsRecurring,
		&i.ExpiresOn,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const checkRunningSubscription = `-- name: CheckRunningSubscription :one
SELECT id, user_id, vtuber_id, vtuber_plan_id, is_recurring, expires_on, created_at, updated_at, deleted_at from vtuber_user_subscriptions WHERE user_id=$1 AND vtuber_id=$2 AND  DATE(expires_on) > CURRENT_DATE
`

type CheckRunningSubscriptionParams struct {
	UserID   int64
	VtuberID int64
}

func (q *Queries) CheckRunningSubscription(ctx context.Context, arg CheckRunningSubscriptionParams) (VtuberUserSubscription, error) {
	row := q.db.QueryRow(ctx, checkRunningSubscription, arg.UserID, arg.VtuberID)
	var i VtuberUserSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.VtuberPlanID,
		&i.IsRecurring,
		&i.ExpiresOn,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteUserVtuberSubscriptionByUserId = `-- name: DeleteUserVtuberSubscriptionByUserId :exec
UPDATE vtuber_user_subscriptions SET deleted_at = CURRENT_TIMESTAMP WHERE user_id = $1
`

func (q *Queries) DeleteUserVtuberSubscriptionByUserId(ctx context.Context, userID int64) error {
	_, err := q.db.Exec(ctx, deleteUserVtuberSubscriptionByUserId, userID)
	return err
}

const deleteVtuberUserSubscription = `-- name: DeleteVtuberUserSubscription :exec
UPDATE vtuber_user_subscriptions SET deleted_at = CURRENT_TIMESTAMP WHERE id=$1
`

func (q *Queries) DeleteVtuberUserSubscription(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteVtuberUserSubscription, id)
	return err
}

const getVtuberUserSubById = `-- name: GetVtuberUserSubById :one
SELECT cs.id, cs.vtuber_id,cs.vtuber_plan_id,cs.created_at,cs.is_recurring,cs.expires_on,cs.updated_at, u.id as user_id, u.full_name as user_name, u.image as user_image , u.email as user_email
FROM vtuber_user_subscriptions cs
         INNER JOIN users u ON cs.user_id = u.id WHERE cs.id=$1 AND cs.deleted_at IS NULL
`

type GetVtuberUserSubByIdRow struct {
	ID           int64
	VtuberID     int64
	VtuberPlanID int64
	CreatedAt    time.Time
	IsRecurring  bool
	ExpiresOn    time.Time
	UpdatedAt    time.Time
	UserID       int64
	UserName     string
	UserImage    *string
	UserEmail    *string
}

func (q *Queries) GetVtuberUserSubById(ctx context.Context, id int64) (GetVtuberUserSubByIdRow, error) {
	row := q.db.QueryRow(ctx, getVtuberUserSubById, id)
	var i GetVtuberUserSubByIdRow
	err := row.Scan(
		&i.ID,
		&i.VtuberID,
		&i.VtuberPlanID,
		&i.CreatedAt,
		&i.IsRecurring,
		&i.ExpiresOn,
		&i.UpdatedAt,
		&i.UserID,
		&i.UserName,
		&i.UserImage,
		&i.UserEmail,
	)
	return i, err
}

const getVtuberUserSubscription = `-- name: GetVtuberUserSubscription :many
WITH FILTERED AS (SELECT cs.id, cs.vtuber_id,cs.vtuber_plan_id,cs.created_at,cs.is_recurring,cs.expires_on,cs.updated_at, u.id as user_id, u.full_name as user_name, u.image as user_image , u.email as user_email
   FROM vtuber_user_subscriptions cs
   INNER JOIN users u ON cs.user_id = u.id
   WHERE
   cs.vtuber_id = COALESCE($5,cs.vtuber_id) AND
    cs.user_id = COALESCE($6,cs.user_id) AND
    cs.vtuber_plan_id = COALESCE($7,cs.vtuber_plan_id) AND cs.deleted_at IS NULL
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.id, f.vtuber_id, f.vtuber_plan_id, f.created_at, f.is_recurring, f.expires_on, f.updated_at, f.user_id, f.user_name, f.user_image, f.user_email, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN $1::TEXT = 'is_recurring' AND $2::TEXT = 'ASC' THEN is_recurring END ASC,
      CASE WHEN $1::TEXT = 'is_recurring' AND $2::TEXT = 'DESC' THEN is_recurring END DESC,
      CASE WHEN $1::TEXT = 'expires_on' AND $2::TEXT = 'ASC' THEN expires_on END ASC,
      CASE WHEN $1::TEXT = 'expires_on' AND $2::TEXT = 'DESC' THEN expires_on END DESC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT $4 OFFSET $3
`

type GetVtuberUserSubscriptionParams struct {
	Sort         string
	Order        string
	Offset       int32
	Limit        int32
	VtuberID     *int64
	UserID       *int64
	VtuberPlanID *int64
}

type GetVtuberUserSubscriptionRow struct {
	ID           int64
	VtuberID     int64
	VtuberPlanID int64
	CreatedAt    time.Time
	IsRecurring  bool
	ExpiresOn    time.Time
	UpdatedAt    time.Time
	UserID       int64
	UserName     string
	UserImage    *string
	UserEmail    *string
	Total        int64
}

func (q *Queries) GetVtuberUserSubscription(ctx context.Context, arg GetVtuberUserSubscriptionParams) ([]GetVtuberUserSubscriptionRow, error) {
	rows, err := q.db.Query(ctx, getVtuberUserSubscription,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.VtuberID,
		arg.UserID,
		arg.VtuberPlanID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetVtuberUserSubscriptionRow
	for rows.Next() {
		var i GetVtuberUserSubscriptionRow
		if err := rows.Scan(
			&i.ID,
			&i.VtuberID,
			&i.VtuberPlanID,
			&i.CreatedAt,
			&i.IsRecurring,
			&i.ExpiresOn,
			&i.UpdatedAt,
			&i.UserID,
			&i.UserName,
			&i.UserImage,
			&i.UserEmail,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubId = `-- name: GetVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubId :one
SELECT id, user_id, vtuber_id, vtuber_plan_id, is_recurring, expires_on, created_at, updated_at, deleted_at FROM vtuber_user_subscriptions WHERE
user_id = $1 AND
vtuber_id = $2 AND
vtuber_plan_id = $3 AND deleted_at IS NULL
`

type GetVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubIdParams struct {
	UserID       int64
	VtuberID     int64
	VtuberPlanID int64
}

func (q *Queries) GetVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubId(ctx context.Context, arg GetVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubIdParams) (VtuberUserSubscription, error) {
	row := q.db.QueryRow(ctx, getVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubId, arg.UserID, arg.VtuberID, arg.VtuberPlanID)
	var i VtuberUserSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.VtuberPlanID,
		&i.IsRecurring,
		&i.ExpiresOn,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const isUserSubscribed = `-- name: IsUserSubscribed :one
SELECT COUNT(cs.id) FROM vtuber_user_subscriptions cs
INNER JOIN vtuber_profiles v ON v.id = cs.vtuber_id
WHERE (cs.vtuber_id::TEXT = $2::TEXT OR v.username = $2::TEXT) AND cs.user_id = $1 AND cs.deleted_at IS NULL
`

type IsUserSubscribedParams struct {
	UserID   int64
	VtuberID string
}

func (q *Queries) IsUserSubscribed(ctx context.Context, arg IsUserSubscribedParams) (int64, error) {
	row := q.db.QueryRow(ctx, isUserSubscribed, arg.UserID, arg.VtuberID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const updateIfExist = `-- name: UpdateIfExist :one
UPDATE vtuber_user_subscriptions
SET is_recurring = $1, expires_on = $2 , vtuber_plan_id=$3
WHERE id=$4 RETURNING id, user_id, vtuber_id, vtuber_plan_id, is_recurring, expires_on, created_at, updated_at, deleted_at
`

type UpdateIfExistParams struct {
	IsRecurring  bool
	ExpiresOn    time.Time
	VtuberPlanID int64
	ID           int64
}

func (q *Queries) UpdateIfExist(ctx context.Context, arg UpdateIfExistParams) (VtuberUserSubscription, error) {
	row := q.db.QueryRow(ctx, updateIfExist,
		arg.IsRecurring,
		arg.ExpiresOn,
		arg.VtuberPlanID,
		arg.ID,
	)
	var i VtuberUserSubscription
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.VtuberID,
		&i.VtuberPlanID,
		&i.IsRecurring,
		&i.ExpiresOn,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateVtuberUserSubscription = `-- name: UpdateVtuberUserSubscription :exec
UPDATE vtuber_user_subscriptions
SET is_recurring = $1 WHERE id = $2
`

type UpdateVtuberUserSubscriptionParams struct {
	IsRecurring bool
	ID          int64
}

func (q *Queries) UpdateVtuberUserSubscription(ctx context.Context, arg UpdateVtuberUserSubscriptionParams) error {
	_, err := q.db.Exec(ctx, updateVtuberUserSubscription, arg.IsRecurring, arg.ID)
	return err
}
