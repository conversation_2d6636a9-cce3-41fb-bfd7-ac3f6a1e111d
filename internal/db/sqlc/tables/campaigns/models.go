// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package campaignsqueries

import (
	"time"
)

type Campaign struct {
	ID                 int64
	Slug               string
	Name               string
	Description        string
	ShortDescription   string
	Thumbnail          string
	StartDate          time.Time
	EndDate            time.Time
	TotalBudget        int32
	PromotionalMessage string
	SocialMediaLinks   []byte
	VtuberID           int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
	DeletedAt          *time.Time
}
