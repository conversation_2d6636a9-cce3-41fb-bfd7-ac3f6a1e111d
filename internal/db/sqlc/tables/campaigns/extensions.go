package campaignsqueries

func (r GetAllCampaignsRow) Count() int32 {
	return int32(r.Total)
}

func (r GetCampaignsByVtuberRow) Count() int32 {
	return int32(r.Total)
}

func (r GetCampaignSubscribersRow) Count() int32 {
	return int32(r.Total)
}

func (r GetCampaignSubscriberCommentsRow) Count() int32 {
	return int32(r.Total)
}

func (r GetMySupportedCampaignsRow) Count() int32 {
	return int32(r.Total)
}
