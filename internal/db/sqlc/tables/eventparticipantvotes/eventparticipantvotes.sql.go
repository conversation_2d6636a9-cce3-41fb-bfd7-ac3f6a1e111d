// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: eventparticipantvotes.sql

package eventparticipantvotesqueries

import (
	"context"
)

const addVote = `-- name: AddVote :one
INSERT INTO event_participant_votes (event_participant_id,user_id,type,remarks,event_id,count)
VALUES ($1,$2,$3,$4,$5,$6) RETURNING id, event_participant_id, user_id, type, count, remarks, event_id, created_at, updated_at
`

type AddVoteParams struct {
	EventParticipantID int64
	UserID             *int64
	Type               EventParticipantVoteType
	Remarks            *string
	EventID            int64
	Count              int32
}

func (q *Queries) AddVote(ctx context.Context, arg AddVoteParams) (EventParticipantVote, error) {
	row := q.db.QueryRow(ctx, addVote,
		arg.EventParticipantID,
		arg.UserID,
		arg.Type,
		arg.Remarks,
		arg.EventID,
		arg.Count,
	)
	var i EventParticipantVote
	err := row.Scan(
		&i.ID,
		&i.EventParticipantID,
		&i.UserID,
		&i.Type,
		&i.Count,
		&i.Remarks,
		&i.EventID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getPlatformVote = `-- name: GetPlatformVote :one
SELECT id, event_participant_id, user_id, type, count, remarks, event_id, created_at, updated_at FROM event_participant_votes
WHERE event_participant_id = $1 AND type='platform_point'
`

func (q *Queries) GetPlatformVote(ctx context.Context, eventParticipantID int64) (EventParticipantVote, error) {
	row := q.db.QueryRow(ctx, getPlatformVote, eventParticipantID)
	var i EventParticipantVote
	err := row.Scan(
		&i.ID,
		&i.EventParticipantID,
		&i.UserID,
		&i.Type,
		&i.Count,
		&i.Remarks,
		&i.EventID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getTodayVote = `-- name: GetTodayVote :one
SELECT id, event_participant_id, user_id, type, count, remarks, event_id, created_at, updated_at FROM event_participant_votes
WHERE event_id = $1 AND
      user_id = $2 AND
      DATE(created_at) = CURRENT_DATE AND type='daily_point'
`

type GetTodayVoteParams struct {
	EventID int64
	UserID  *int64
}

func (q *Queries) GetTodayVote(ctx context.Context, arg GetTodayVoteParams) (EventParticipantVote, error) {
	row := q.db.QueryRow(ctx, getTodayVote, arg.EventID, arg.UserID)
	var i EventParticipantVote
	err := row.Scan(
		&i.ID,
		&i.EventParticipantID,
		&i.UserID,
		&i.Type,
		&i.Count,
		&i.Remarks,
		&i.EventID,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const updatePlatformVote = `-- name: UpdatePlatformVote :exec
UPDATE event_participant_votes SET user_id=$1, count=$2, remarks=$3
WHERE id=$4
`

type UpdatePlatformVoteParams struct {
	UserID  *int64
	Count   int32
	Remarks *string
	ID      int64
}

func (q *Queries) UpdatePlatformVote(ctx context.Context, arg UpdatePlatformVoteParams) error {
	_, err := q.db.Exec(ctx, updatePlatformVote,
		arg.UserID,
		arg.Count,
		arg.Remarks,
		arg.ID,
	)
	return err
}
