// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package eventparticipantvotesqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type EventParticipantVoteType string

const (
	EventParticipantVoteTypeDailyPoint    EventParticipantVoteType = "daily_point"
	EventParticipantVoteTypeUserPoint     EventParticipantVoteType = "user_point"
	EventParticipantVoteTypePlatformPoint EventParticipantVoteType = "platform_point"
)

func (e *EventParticipantVoteType) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventParticipantVoteType(s)
	case string:
		*e = EventParticipantVoteType(s)
	default:
		return fmt.Errorf("unsupported scan type for EventParticipantVoteType: %T", src)
	}
	return nil
}

type NullEventParticipantVoteType struct {
	EventParticipantVoteType EventParticipantVoteType
	Valid                    bool // Valid is true if EventParticipantVoteType is not NULL
}

// <PERSON>an implements the Scanner interface.
func (ns *NullEventParticipantVoteType) Scan(value interface{}) error {
	if value == nil {
		ns.EventParticipantVoteType, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventParticipantVoteType.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventParticipantVoteType) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventParticipantVoteType), nil
}

type EventParticipantVote struct {
	ID                 int64
	EventParticipantID int64
	UserID             *int64
	Type               EventParticipantVoteType
	Count              int32
	Remarks            *string
	EventID            int64
	CreatedAt          time.Time
	UpdatedAt          time.Time
}
