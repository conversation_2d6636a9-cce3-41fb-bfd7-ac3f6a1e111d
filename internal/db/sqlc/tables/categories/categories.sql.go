// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: categories.sql

package categoriesqueries

import (
	"context"
)

const createCategory = `-- name: CreateCategory :one
INSERT INTO categories (name, description, image,slug) VALUES ($1, $2, $3,$4) RETURNING id, name, image, description, slug, created_at, updated_at, deleted_at
`

type CreateCategoryParams struct {
	Name        string
	Description *string
	Image       string
	Slug        string
}

func (q *Queries) CreateCategory(ctx context.Context, arg CreateCategoryParams) (Category, error) {
	row := q.db.QueryRow(ctx, createCategory,
		arg.Name,
		arg.Description,
		arg.Image,
		arg.Slug,
	)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Image,
		&i.Description,
		&i.Slug,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const deleteCategory = `-- name: DeleteCategory :one
UPDATE categories SET deleted_at = CURRENT_TIMESTAMP WHERE id = $1 RETURNING id, name, image, description, slug, created_at, updated_at, deleted_at
`

func (q *Queries) DeleteCategory(ctx context.Context, id int64) (Category, error) {
	row := q.db.QueryRow(ctx, deleteCategory, id)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Image,
		&i.Description,
		&i.Slug,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getAllCategories = `-- name: GetAllCategories :many
SELECT id, name, image, description, slug, created_at, updated_at, deleted_at FROM categories WHERE deleted_at IS NULL ORDER BY created_at DESC
`

func (q *Queries) GetAllCategories(ctx context.Context) ([]Category, error) {
	rows, err := q.db.Query(ctx, getAllCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Category
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Image,
			&i.Description,
			&i.Slug,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCategoriesByIds = `-- name: GetCategoriesByIds :many
SELECT id, name, image, description, slug, created_at, updated_at, deleted_at FROM categories 
WHERE id = ANY($1)
`

func (q *Queries) GetCategoriesByIds(ctx context.Context, ids []int64) ([]Category, error) {
	rows, err := q.db.Query(ctx, getCategoriesByIds, ids)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []Category
	for rows.Next() {
		var i Category
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Image,
			&i.Description,
			&i.Slug,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCategoryById = `-- name: GetCategoryById :one
SELECT id, name, image, description, slug, created_at, updated_at, deleted_at FROM categories WHERE (id::TEXT = $1::TEXT OR slug::TEXT = $1::TEXT) AND deleted_at IS NULL
`

func (q *Queries) GetCategoryById(ctx context.Context, id string) (Category, error) {
	row := q.db.QueryRow(ctx, getCategoryById, id)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Image,
		&i.Description,
		&i.Slug,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const getCategoryBySlug = `-- name: GetCategoryBySlug :one
SELECT id, name, image, description, slug, created_at, updated_at, deleted_at FROM categories WHERE slug = $1
`

func (q *Queries) GetCategoryBySlug(ctx context.Context, slug string) (Category, error) {
	row := q.db.QueryRow(ctx, getCategoryBySlug, slug)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Image,
		&i.Description,
		&i.Slug,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}

const updateCategory = `-- name: UpdateCategory :one
UPDATE categories SET name = $1, description = $2 WHERE id = $3 RETURNING id, name, image, description, slug, created_at, updated_at, deleted_at
`

type UpdateCategoryParams struct {
	Name        string
	Description *string
	ID          int64
}

func (q *Queries) UpdateCategory(ctx context.Context, arg UpdateCategoryParams) (Category, error) {
	row := q.db.QueryRow(ctx, updateCategory, arg.Name, arg.Description, arg.ID)
	var i Category
	err := row.Scan(
		&i.ID,
		&i.Name,
		&i.Image,
		&i.Description,
		&i.Slug,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
	)
	return i, err
}
