// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: copyfrom.go

package eventcategoriesqueries

import (
	"context"
)

// iteratorForAddEventCategories implements pgx.CopyFromSource.
type iteratorForAddEventCategories struct {
	rows                 []AddEventCategoriesParams
	skippedFirstNextCall bool
}

func (r *iteratorForAddEventCategories) Next() bool {
	if len(r.rows) == 0 {
		return false
	}
	if !r.skippedFirstNextCall {
		r.skippedFirstNextCall = true
		return true
	}
	r.rows = r.rows[1:]
	return len(r.rows) > 0
}

func (r iteratorForAddEventCategories) Values() ([]interface{}, error) {
	return []interface{}{
		r.rows[0].EventID,
		r.rows[0].CategoryID,
	}, nil
}

func (r iteratorForAddEventCategories) Err() error {
	return nil
}

func (q *Queries) AddEventCategories(ctx context.Context, arg []AddEventCategoriesParams) (int64, error) {
	return q.db.CopyFrom(ctx, []string{"event_categories"}, []string{"event_id", "category_id"}, &iteratorForAddEventCategories{rows: arg})
}
