// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: eventcategories.sql

package eventcategoriesqueries

import (
	"context"
	"time"
)

type AddEventCategoriesParams struct {
	EventID    int64
	CategoryID int64
}

const deleteEventCategories = `-- name: DeleteEventCategories :exec
DELETE FROM event_categories WHERE event_id = $1
`

func (q *Queries) DeleteEventCategories(ctx context.Context, eventID int64) error {
	_, err := q.db.Exec(ctx, deleteEventCategories, eventID)
	return err
}

const getEventCategories = `-- name: GetEventCategories :many
SELECT DISTINCT(c.id), c.name, c.description, c.created_at, c.slug FROM event_categories
ec INNER JOIN categories c ON ec.category_id = c.id
INNER JOIN events e ON e.id = ec.event_id WHERE e.status = 'approved' AND e.deleted_at IS NULL
ORDER BY c.created_at DESC LIMIT 3
`

type GetEventCategoriesRow struct {
	ID          int64
	Name        string
	Description *string
	CreatedAt   time.Time
	Slug        string
}

func (q *Queries) GetEventCategories(ctx context.Context) ([]GetEventCategoriesRow, error) {
	rows, err := q.db.Query(ctx, getEventCategories)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEventCategoriesRow
	for rows.Next() {
		var i GetEventCategoriesRow
		if err := rows.Scan(
			&i.ID,
			&i.Name,
			&i.Description,
			&i.CreatedAt,
			&i.Slug,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
