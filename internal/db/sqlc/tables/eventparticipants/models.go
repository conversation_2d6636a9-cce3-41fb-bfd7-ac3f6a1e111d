// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0

package eventparticipantsqueries

import (
	"database/sql/driver"
	"fmt"
	"time"
)

type EventParticipantStatus string

const (
	EventParticipantStatusAccepted EventParticipantStatus = "accepted"
	EventParticipantStatusRejected EventParticipantStatus = "rejected"
	EventParticipantStatusPending  EventParticipantStatus = "pending"
)

func (e *EventParticipantStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventParticipantStatus(s)
	case string:
		*e = EventParticipantStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for EventParticipantStatus: %T", src)
	}
	return nil
}

type NullEventParticipantStatus struct {
	EventParticipantStatus EventParticipantStatus
	Valid                  bool // Valid is true if EventParticipantStatus is not NULL
}

// <PERSON><PERSON> implements the Scanner interface.
func (ns *NullEventParticipantStatus) Scan(value interface{}) error {
	if value == nil {
		ns.EventParticipantStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventParticipantStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventParticipantStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventParticipantStatus), nil
}

type EventStatus string

const (
	EventStatusApproved EventStatus = "approved"
	EventStatusRejected EventStatus = "rejected"
	EventStatusPending  EventStatus = "pending"
)

func (e *EventStatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EventStatus(s)
	case string:
		*e = EventStatus(s)
	default:
		return fmt.Errorf("unsupported scan type for EventStatus: %T", src)
	}
	return nil
}

type NullEventStatus struct {
	EventStatus EventStatus
	Valid       bool // Valid is true if EventStatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEventStatus) Scan(value interface{}) error {
	if value == nil {
		ns.EventStatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EventStatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEventStatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EventStatus), nil
}

type EventParticipant struct {
	ID        int64
	EventID   int64
	VtuberID  int64
	Status    EventParticipantStatus
	Remarks   *string
	CreatedAt time.Time
	UpdatedAt time.Time
}
