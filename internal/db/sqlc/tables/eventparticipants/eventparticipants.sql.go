// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: eventparticipants.sql

package eventparticipantsqueries

import (
	"context"
	"time"
)

const changeStatus = `-- name: ChangeStatus :exec
UPDATE event_participants SET status = $1 , remarks=$2  WHERE id = $3
`

type ChangeStatusParams struct {
	Status  EventParticipantStatus
	Remarks *string
	ID      int64
}

func (q *Queries) ChangeStatus(ctx context.Context, arg ChangeStatusParams) error {
	_, err := q.db.Exec(ctx, changeStatus, arg.Status, arg.Remarks, arg.ID)
	return err
}

const getAllEventParticipantByEventID = `-- name: GetAllEventParticipantByEventID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.remarks, vp.id as event_vtuber_id, vp.display_name as vtuber_name, vp.furigana as vtuber_furigana, vp.description as vtuber_introduction, vp.image as vtuber_image,ep.created_at,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                         FROM event_participants ep
INNER JOIN vtuber_profiles vp ON vp.id=ep.vtuber_id WHERE ep.event_id::TEXT=$5::TEXT),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.event_id, f.vtuber_id, f.status, f.remarks, f.event_vtuber_id, f.vtuber_name, f.vtuber_furigana, f.vtuber_introduction, f.vtuber_image, f.created_at, f.vote_count, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END,
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC
LIMIT $4 OFFSET $3
`

type GetAllEventParticipantByEventIDParams struct {
	Sort    string
	Order   string
	Offset  int32
	Limit   int32
	EventID string
}

type GetAllEventParticipantByEventIDRow struct {
	ID                 int64
	EventID            int64
	VtuberID           int64
	Status             EventParticipantStatus
	Remarks            *string
	EventVtuberID      int64
	VtuberName         string
	VtuberFurigana     string
	VtuberIntroduction *string
	VtuberImage        *string
	CreatedAt          time.Time
	VoteCount          int32
	Total              int64
}

func (q *Queries) GetAllEventParticipantByEventID(ctx context.Context, arg GetAllEventParticipantByEventIDParams) ([]GetAllEventParticipantByEventIDRow, error) {
	rows, err := q.db.Query(ctx, getAllEventParticipantByEventID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.EventID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllEventParticipantByEventIDRow
	for rows.Next() {
		var i GetAllEventParticipantByEventIDRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.VtuberID,
			&i.Status,
			&i.Remarks,
			&i.EventVtuberID,
			&i.VtuberName,
			&i.VtuberFurigana,
			&i.VtuberIntroduction,
			&i.VtuberImage,
			&i.CreatedAt,
			&i.VoteCount,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getAllEventParticipants = `-- name: GetAllEventParticipants :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.created_at,
                         e.title,
                         e.description,
                         e.image,
                         e.rules,
                         e.start_date,
                         ep.remarks,
                         e.end_date,
                         e.created_at as event_created_at,
                         e.updated_at,
                         e.status as event_status,
                         e.short_description,
                         e.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         vp.description as vtuber_introduction,
                         v.id as event_vtuber_id, v.display_name as event_vtuber_name, v.furigana as event_vtuber_furigana, v.image as event_vtuber_image,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                         END as category_ids,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                  FROM event_participants ep
                           INNER JOIN events e ON e.id=ep.event_id
                           INNER JOIN vtuber_profiles v on v.id = ep.vtuber_id
                           LEFT JOIN users u ON e.user_id = u.id
                           LEFT JOIN vtuber_profiles vp ON e.vtuber_profile_id = vp.id
                           LEFT JOIN event_categories ec ON ep.event_id = ec.event_id
                  GROUP BY ep.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
                  ),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.event_id, f.vtuber_id, f.status, f.created_at, f.title, f.description, f.image, f.rules, f.start_date, f.remarks, f.end_date, f.event_created_at, f.updated_at, f.event_status, f.short_description, f.slug, f.userid, f.username, f.user_image, f.vtuberid, f.vtubername, f.vtuberimage, f.vtuber_introduction, f.event_vtuber_id, f.event_vtuber_name, f.event_vtuber_furigana, f.event_vtuber_image, f.category_ids, f.vote_count, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END,
    CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC
LIMIT $4 OFFSET $3
`

type GetAllEventParticipantsParams struct {
	Sort   string
	Order  string
	Offset int32
	Limit  int32
}

type GetAllEventParticipantsRow struct {
	ID                  int64
	EventID             int64
	VtuberID            int64
	Status              EventParticipantStatus
	CreatedAt           time.Time
	Title               string
	Description         string
	Image               string
	Rules               string
	StartDate           time.Time
	Remarks             *string
	EndDate             time.Time
	EventCreatedAt      time.Time
	UpdatedAt           time.Time
	EventStatus         EventStatus
	ShortDescription    string
	Slug                string
	Userid              *int64
	Username            *string
	UserImage           *string
	Vtuberid            *int64
	Vtubername          *string
	Vtuberimage         *string
	VtuberIntroduction  *string
	EventVtuberID       int64
	EventVtuberName     string
	EventVtuberFurigana string
	EventVtuberImage    *string
	CategoryIds         []int64
	VoteCount           int32
	Total               int64
}

func (q *Queries) GetAllEventParticipants(ctx context.Context, arg GetAllEventParticipantsParams) ([]GetAllEventParticipantsRow, error) {
	rows, err := q.db.Query(ctx, getAllEventParticipants,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetAllEventParticipantsRow
	for rows.Next() {
		var i GetAllEventParticipantsRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.VtuberID,
			&i.Status,
			&i.CreatedAt,
			&i.Title,
			&i.Description,
			&i.Image,
			&i.Rules,
			&i.StartDate,
			&i.Remarks,
			&i.EndDate,
			&i.EventCreatedAt,
			&i.UpdatedAt,
			&i.EventStatus,
			&i.ShortDescription,
			&i.Slug,
			&i.Userid,
			&i.Username,
			&i.UserImage,
			&i.Vtuberid,
			&i.Vtubername,
			&i.Vtuberimage,
			&i.VtuberIntroduction,
			&i.EventVtuberID,
			&i.EventVtuberName,
			&i.EventVtuberFurigana,
			&i.EventVtuberImage,
			&i.CategoryIds,
			&i.VoteCount,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getCreatorEventParticipation = `-- name: GetCreatorEventParticipation :many
SELECT event_id FROM event_participants WHERE vtuber_id = $1 ORDER BY created_at DESC
`

func (q *Queries) GetCreatorEventParticipation(ctx context.Context, vtuberID int64) ([]int64, error) {
	rows, err := q.db.Query(ctx, getCreatorEventParticipation, vtuberID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []int64
	for rows.Next() {
		var event_id int64
		if err := rows.Scan(&event_id); err != nil {
			return nil, err
		}
		items = append(items, event_id)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventParticipantByEventID = `-- name: GetEventParticipantByEventID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id, ep.remarks, vp.social_media_links, ep.vtuber_id,ep.status, vp.id as event_vtuber_id, vp.display_name as vtuber_name, vp.furigana as vtuber_furigana, vp.description as vtuber_introduction, vp.image as vtuber_image,ep.created_at,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                         FROM event_participants ep
INNER JOIN vtuber_profiles vp ON vp.id=ep.vtuber_id
INNER JOIN events e ON e.id=ep.event_id 
WHERE (ep.event_id::TEXT=$5::TEXT OR e.slug::TEXT = $5::TEXT) AND ep.status='accepted'),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.event_id, f.remarks, f.social_media_links, f.vtuber_id, f.status, f.event_vtuber_id, f.vtuber_name, f.vtuber_furigana, f.vtuber_introduction, f.vtuber_image, f.created_at, f.vote_count, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END,
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC
LIMIT $4 OFFSET $3
`

type GetEventParticipantByEventIDParams struct {
	Sort    string
	Order   string
	Offset  int32
	Limit   int32
	EventID string
}

type GetEventParticipantByEventIDRow struct {
	ID                 int64
	EventID            int64
	Remarks            *string
	SocialMediaLinks   []byte
	VtuberID           int64
	Status             EventParticipantStatus
	EventVtuberID      int64
	VtuberName         string
	VtuberFurigana     string
	VtuberIntroduction *string
	VtuberImage        *string
	CreatedAt          time.Time
	VoteCount          int32
	Total              int64
}

func (q *Queries) GetEventParticipantByEventID(ctx context.Context, arg GetEventParticipantByEventIDParams) ([]GetEventParticipantByEventIDRow, error) {
	rows, err := q.db.Query(ctx, getEventParticipantByEventID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.EventID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEventParticipantByEventIDRow
	for rows.Next() {
		var i GetEventParticipantByEventIDRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.Remarks,
			&i.SocialMediaLinks,
			&i.VtuberID,
			&i.Status,
			&i.EventVtuberID,
			&i.VtuberName,
			&i.VtuberFurigana,
			&i.VtuberIntroduction,
			&i.VtuberImage,
			&i.CreatedAt,
			&i.VoteCount,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getEventParticipantByEventIDAndVtuberID = `-- name: GetEventParticipantByEventIDAndVtuberID :one
SELECT id, event_id, vtuber_id, status, remarks, created_at, updated_at FROM event_participants WHERE event_id = $1 AND vtuber_id = $2
`

type GetEventParticipantByEventIDAndVtuberIDParams struct {
	EventID  int64
	VtuberID int64
}

func (q *Queries) GetEventParticipantByEventIDAndVtuberID(ctx context.Context, arg GetEventParticipantByEventIDAndVtuberIDParams) (EventParticipant, error) {
	row := q.db.QueryRow(ctx, getEventParticipantByEventIDAndVtuberID, arg.EventID, arg.VtuberID)
	var i EventParticipant
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.VtuberID,
		&i.Status,
		&i.Remarks,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getEventParticipantByID = `-- name: GetEventParticipantByID :one
SELECT id, event_id, vtuber_id, status, remarks, created_at, updated_at FROM event_participants WHERE id = $1
`

func (q *Queries) GetEventParticipantByID(ctx context.Context, id int64) (EventParticipant, error) {
	row := q.db.QueryRow(ctx, getEventParticipantByID, id)
	var i EventParticipant
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.VtuberID,
		&i.Status,
		&i.Remarks,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getEventParticipantByVtuberID = `-- name: GetEventParticipantByVtuberID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.created_at,
                         e.title,
                         e.description,
                         e.image,
                         e.rules,
                         ep.remarks,
                         e.start_date,
                         e.end_date,
                         e.created_at as event_created_at,
                         e.updated_at,
                         e.status as event_status,
                         e.short_description,
                         e.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         vp.description as vtuber_introduction,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                         END as category_ids,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                  FROM event_participants ep
                           INNER JOIN events e ON e.id=ep.event_id
                           LEFT JOIN users u ON e.user_id = u.id
                           LEFT JOIN vtuber_profiles vp ON e.vtuber_profile_id = vp.id
                            LEFT JOIN event_categories ec ON ep.event_id = ec.event_id
                            WHERE ep.vtuber_id=$5::BIGINT 
                          
                  GROUP BY ep.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image, e.title, e.description, e.image, e.rules, e.start_date, e.end_date, e.created_at, e.updated_at, e.status, e.short_description, e.slug, vp.description
                  ),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.event_id, f.vtuber_id, f.status, f.created_at, f.title, f.description, f.image, f.rules, f.remarks, f.start_date, f.end_date, f.event_created_at, f.updated_at, f.event_status, f.short_description, f.slug, f.userid, f.username, f.user_image, f.vtuberid, f.vtubername, f.vtuberimage, f.vtuber_introduction, f.category_ids, f.vote_count, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END,
       CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC
LIMIT $4 OFFSET $3
`

type GetEventParticipantByVtuberIDParams struct {
	Sort     string
	Order    string
	Offset   int32
	Limit    int32
	VtuberID int64
}

type GetEventParticipantByVtuberIDRow struct {
	ID                 int64
	EventID            int64
	VtuberID           int64
	Status             EventParticipantStatus
	CreatedAt          time.Time
	Title              string
	Description        string
	Image              string
	Rules              string
	Remarks            *string
	StartDate          time.Time
	EndDate            time.Time
	EventCreatedAt     time.Time
	UpdatedAt          time.Time
	EventStatus        EventStatus
	ShortDescription   string
	Slug               string
	Userid             *int64
	Username           *string
	UserImage          *string
	Vtuberid           *int64
	Vtubername         *string
	Vtuberimage        *string
	VtuberIntroduction *string
	CategoryIds        []int64
	VoteCount          int32
	Total              int64
}

func (q *Queries) GetEventParticipantByVtuberID(ctx context.Context, arg GetEventParticipantByVtuberIDParams) ([]GetEventParticipantByVtuberIDRow, error) {
	rows, err := q.db.Query(ctx, getEventParticipantByVtuberID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.VtuberID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetEventParticipantByVtuberIDRow
	for rows.Next() {
		var i GetEventParticipantByVtuberIDRow
		if err := rows.Scan(
			&i.ID,
			&i.EventID,
			&i.VtuberID,
			&i.Status,
			&i.CreatedAt,
			&i.Title,
			&i.Description,
			&i.Image,
			&i.Rules,
			&i.Remarks,
			&i.StartDate,
			&i.EndDate,
			&i.EventCreatedAt,
			&i.UpdatedAt,
			&i.EventStatus,
			&i.ShortDescription,
			&i.Slug,
			&i.Userid,
			&i.Username,
			&i.UserImage,
			&i.Vtuberid,
			&i.Vtubername,
			&i.Vtuberimage,
			&i.VtuberIntroduction,
			&i.CategoryIds,
			&i.VoteCount,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTopTenEventParticipantVtubers = `-- name: GetTopTenEventParticipantVtubers :many
SELECT vp.display_name, vp.image, vp.id, COALESCE(vote_count.count, 0) as total_votes
FROM event_participants ep INNER JOIN vtuber_profiles vp ON ep.vtuber_id = vp.id
FULL OUTER JOIN (SELECT sum(count) as count, event_participant_id FROM event_participant_votes WHERE event_participant_votes.event_id = $1 GROUP BY event_participant_id) vote_count ON vote_count.event_participant_id = ep.id
WHERE ep.event_id = $1 AND ep.status = 'accepted'
ORDER BY total_votes DESC
LIMIT 10
`

type GetTopTenEventParticipantVtubersRow struct {
	DisplayName string
	Image       *string
	ID          int64
	TotalVotes  int64
}

func (q *Queries) GetTopTenEventParticipantVtubers(ctx context.Context, eventID int64) ([]GetTopTenEventParticipantVtubersRow, error) {
	rows, err := q.db.Query(ctx, getTopTenEventParticipantVtubers, eventID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTopTenEventParticipantVtubersRow
	for rows.Next() {
		var i GetTopTenEventParticipantVtubersRow
		if err := rows.Scan(
			&i.DisplayName,
			&i.Image,
			&i.ID,
			&i.TotalVotes,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const participantInEvent = `-- name: ParticipantInEvent :one
INSERT INTO event_participants(event_id,vtuber_id) VALUES ($1,$2) RETURNING id, event_id, vtuber_id, status, remarks, created_at, updated_at
`

type ParticipantInEventParams struct {
	EventID  int64
	VtuberID int64
}

func (q *Queries) ParticipantInEvent(ctx context.Context, arg ParticipantInEventParams) (EventParticipant, error) {
	row := q.db.QueryRow(ctx, participantInEvent, arg.EventID, arg.VtuberID)
	var i EventParticipant
	err := row.Scan(
		&i.ID,
		&i.EventID,
		&i.VtuberID,
		&i.Status,
		&i.Remarks,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
