// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: userbillinginfos.sql

package userbillinginfosqueries

import (
	"context"
)

const addBillingInfo = `-- name: AddBillingInfo :one
INSERT INTO user_billing_infos (user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number,card_no)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11,$12)
RETURNING id, user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number, card_no, created_at, updated_at
`

type AddBillingInfoParams struct {
	UserID      int64
	GmoMemberID string
	FullName    string
	Address1    string
	Address2    *string
	City        string
	State       *string
	Country     string
	PostalCode  string
	CompanyName *string
	VatNumber   *string
	CardNo      string
}

func (q *Queries) AddBillingInfo(ctx context.Context, arg AddBillingInfoParams) (UserBillingInfo, error) {
	row := q.db.QueryRow(ctx, addBillingInfo,
		arg.UserID,
		arg.GmoMemberID,
		arg.FullName,
		arg.Address1,
		arg.Address2,
		arg.City,
		arg.State,
		arg.Country,
		arg.PostalCode,
		arg.CompanyName,
		arg.VatNumber,
		arg.CardNo,
	)
	var i UserBillingInfo
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.GmoMemberID,
		&i.FullName,
		&i.Address1,
		&i.Address2,
		&i.City,
		&i.State,
		&i.Country,
		&i.PostalCode,
		&i.CompanyName,
		&i.VatNumber,
		&i.CardNo,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const countBillingInfo = `-- name: CountBillingInfo :one
SELECT COUNT(*) FROM user_billing_infos WHERE user_id = $1
`

func (q *Queries) CountBillingInfo(ctx context.Context, userID int64) (int64, error) {
	row := q.db.QueryRow(ctx, countBillingInfo, userID)
	var count int64
	err := row.Scan(&count)
	return count, err
}

const deleteBillingInfo = `-- name: DeleteBillingInfo :exec
DELETE FROM user_billing_infos WHERE id = $1
`

func (q *Queries) DeleteBillingInfo(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deleteBillingInfo, id)
	return err
}

const getBillingInfoById = `-- name: GetBillingInfoById :one
SELECT id, user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number, card_no, created_at, updated_at FROM user_billing_infos WHERE id = $1
`

func (q *Queries) GetBillingInfoById(ctx context.Context, id int64) (UserBillingInfo, error) {
	row := q.db.QueryRow(ctx, getBillingInfoById, id)
	var i UserBillingInfo
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.GmoMemberID,
		&i.FullName,
		&i.Address1,
		&i.Address2,
		&i.City,
		&i.State,
		&i.Country,
		&i.PostalCode,
		&i.CompanyName,
		&i.VatNumber,
		&i.CardNo,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}

const getBillingInfoByUserId = `-- name: GetBillingInfoByUserId :many
SELECT id, user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number, card_no, created_at, updated_at FROM user_billing_infos WHERE user_id = $1
`

func (q *Queries) GetBillingInfoByUserId(ctx context.Context, userID int64) ([]UserBillingInfo, error) {
	rows, err := q.db.Query(ctx, getBillingInfoByUserId, userID)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []UserBillingInfo
	for rows.Next() {
		var i UserBillingInfo
		if err := rows.Scan(
			&i.ID,
			&i.UserID,
			&i.GmoMemberID,
			&i.FullName,
			&i.Address1,
			&i.Address2,
			&i.City,
			&i.State,
			&i.Country,
			&i.PostalCode,
			&i.CompanyName,
			&i.VatNumber,
			&i.CardNo,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updateBillingInfo = `-- name: UpdateBillingInfo :one
UPDATE user_billing_infos SET
    full_name = $2,
    address_1 = $3,
    address_2 = $4,
    city = $5,
    state = $6,
    country = $8,
    postal_code = $9,
    company_name = $10,
    vat_number = $7,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING id, user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number, card_no, created_at, updated_at
`

type UpdateBillingInfoParams struct {
	ID          int64
	FullName    string
	Address1    string
	Address2    *string
	City        string
	State       *string
	VatNumber   *string
	Country     string
	PostalCode  string
	CompanyName *string
}

func (q *Queries) UpdateBillingInfo(ctx context.Context, arg UpdateBillingInfoParams) (UserBillingInfo, error) {
	row := q.db.QueryRow(ctx, updateBillingInfo,
		arg.ID,
		arg.FullName,
		arg.Address1,
		arg.Address2,
		arg.City,
		arg.State,
		arg.VatNumber,
		arg.Country,
		arg.PostalCode,
		arg.CompanyName,
	)
	var i UserBillingInfo
	err := row.Scan(
		&i.ID,
		&i.UserID,
		&i.GmoMemberID,
		&i.FullName,
		&i.Address1,
		&i.Address2,
		&i.City,
		&i.State,
		&i.Country,
		&i.PostalCode,
		&i.CompanyName,
		&i.VatNumber,
		&i.CardNo,
		&i.CreatedAt,
		&i.UpdatedAt,
	)
	return i, err
}
