// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: posts.sql

package postsqueries

import (
	"context"
	"time"
)

const addPost = `-- name: AddPost :one
WITH inserted as (INSERT INTO posts (vtuber_profile_id, membership_only, description, title, name, media, media_type,
                                     category_id, short_description, slug, campaign_id) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
    RETURNING id, slug, vtuber_profile_id, membership_only, description, short_description, title, name, media, media_type, category_id, created_at, updated_at, deleted_at, campaign_id)
SELECT inserted.id, inserted.slug, inserted.vtuber_profile_id, inserted.membership_only, inserted.description, inserted.short_description, inserted.title, inserted.name, inserted.media, inserted.media_type, inserted.category_id, inserted.created_at, inserted.updated_at, inserted.deleted_at, inserted.campaign_id, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image
FROM inserted
         INNER JOIN vtuber_profiles vp ON inserted.vtuber_profile_id = vp.id
`

type AddPostParams struct {
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	ShortDescription string
	Slug             string
	CampaignID       *int64
}

type AddPostRow struct {
	ID               int64
	Slug             string
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	ShortDescription string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	CampaignID       *int64
	VtuberID         int64
	VtuberName       string
	VtuberImage      *string
}

func (q *Queries) AddPost(ctx context.Context, arg AddPostParams) (AddPostRow, error) {
	row := q.db.QueryRow(ctx, addPost,
		arg.VtuberProfileID,
		arg.MembershipOnly,
		arg.Description,
		arg.Title,
		arg.Name,
		arg.Media,
		arg.MediaType,
		arg.CategoryID,
		arg.ShortDescription,
		arg.Slug,
		arg.CampaignID,
	)
	var i AddPostRow
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.VtuberProfileID,
		&i.MembershipOnly,
		&i.Description,
		&i.ShortDescription,
		&i.Title,
		&i.Name,
		&i.Media,
		&i.MediaType,
		&i.CategoryID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.CampaignID,
		&i.VtuberID,
		&i.VtuberName,
		&i.VtuberImage,
	)
	return i, err
}

const deletePost = `-- name: DeletePost :exec
UPDATE posts
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1
`

func (q *Queries) DeletePost(ctx context.Context, id int64) error {
	_, err := q.db.Exec(ctx, deletePost, id)
	return err
}

const getPost = `-- name: GetPost :one
 SELECT id, slug, vtuber_profile_id, membership_only, description, short_description, title, name, media, media_type, category_id, created_at, updated_at, deleted_at, campaign_id FROM posts WHERE (id::TEXT = $1::TEXT OR slug::TEXT = $1::TEXT)
`

func (q *Queries) GetPost(ctx context.Context, id string) (Post, error) {
	row := q.db.QueryRow(ctx, getPost, id)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.VtuberProfileID,
		&i.MembershipOnly,
		&i.Description,
		&i.ShortDescription,
		&i.Title,
		&i.Name,
		&i.Media,
		&i.MediaType,
		&i.CategoryID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.CampaignID,
	)
	return i, err
}

const getPostByID = `-- name: GetPostByID :one
SELECT p.id, p.slug, p.vtuber_profile_id, p.membership_only, p.description, p.short_description, p.title, p.name, p.media, p.media_type, p.category_id, p.created_at, p.updated_at, p.deleted_at, p.campaign_id,  p.campaign_id, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image,
COALESCE(likes.like_count, 0) as likes,
COALESCE(comments.post_comments_count, 0) as comments,
CASE
               WHEN $1::BIGINT IS NULL THEN FALSE
WHEN EXISTS (SELECT 1
          FROM post_likes pl
          WHERE pl.post_id = p.id
            AND pl.user_id = $1::BIGINT)
 THEN TRUE
ELSE FALSE
END as has_liked
FROM posts as p
         INNER JOIN vtuber_profiles as vp ON vp.id = p.vtuber_profile_id
         LEFT JOIN (SELECT COUNT(id) AS like_count, post_id
        FROM post_likes
        GROUP BY post_id) AS likes ON likes.post_id = p.id
        LEFT JOIN (SELECT COUNT(id) AS post_comments_count, post_id
                   FROM post_comments
                   GROUP BY post_id) AS comments ON comments.post_id = p.id
WHERE (p.id::TEXT = $2::TEXT OR p.slug::TEXT = $2::TEXT)
  AND p.deleted_at IS NULL
`

type GetPostByIDParams struct {
	UserID *int64
	ID     string
}

type GetPostByIDRow struct {
	ID               int64
	Slug             string
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	ShortDescription string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	CampaignID       *int64
	CampaignID_2     *int64
	VtuberID         int64
	VtuberName       string
	VtuberImage      *string
	Likes            int64
	Comments         int64
	HasLiked         bool
}

func (q *Queries) GetPostByID(ctx context.Context, arg GetPostByIDParams) (GetPostByIDRow, error) {
	row := q.db.QueryRow(ctx, getPostByID, arg.UserID, arg.ID)
	var i GetPostByIDRow
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.VtuberProfileID,
		&i.MembershipOnly,
		&i.Description,
		&i.ShortDescription,
		&i.Title,
		&i.Name,
		&i.Media,
		&i.MediaType,
		&i.CategoryID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.CampaignID,
		&i.CampaignID_2,
		&i.VtuberID,
		&i.VtuberName,
		&i.VtuberImage,
		&i.Likes,
		&i.Comments,
		&i.HasLiked,
	)
	return i, err
}

const getPostBySlug = `-- name: GetPostBySlug :one
SELECT  id, slug, vtuber_profile_id, membership_only, description, short_description, title, name, media, media_type, category_id, created_at, updated_at, deleted_at, campaign_id FROM posts WHERE slug = $1
`

func (q *Queries) GetPostBySlug(ctx context.Context, slug string) (Post, error) {
	row := q.db.QueryRow(ctx, getPostBySlug, slug)
	var i Post
	err := row.Scan(
		&i.ID,
		&i.Slug,
		&i.VtuberProfileID,
		&i.MembershipOnly,
		&i.Description,
		&i.ShortDescription,
		&i.Title,
		&i.Name,
		&i.Media,
		&i.MediaType,
		&i.CategoryID,
		&i.CreatedAt,
		&i.UpdatedAt,
		&i.DeletedAt,
		&i.CampaignID,
	)
	return i, err
}

const getVtuberGalleries = `-- name: GetVtuberGalleries :many
WITH FILTERED AS (SELECT id, slug, vtuber_profile_id, membership_only, description, short_description, title, name, media, media_type, category_id, created_at, updated_at, deleted_at, campaign_id FROM posts WHERE deleted_at IS NULL AND media IS NOT NULL AND media_type IS NOT NULL AND vtuber_profile_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.id, f.slug, f.vtuber_profile_id, f.membership_only, f.description, f.short_description, f.title, f.name, f.media, f.media_type, f.category_id, f.created_at, f.updated_at, f.deleted_at, f.campaign_id, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN $2::TEXT = 'created_at' AND $3::TEXT = 'DESC' THEN created_at END DESC
LIMIT $5 OFFSET $4
`

type GetVtuberGalleriesParams struct {
	VtuberProfileID int64
	Sort            string
	Order           string
	Offset          int32
	Limit           int32
}

type GetVtuberGalleriesRow struct {
	ID               int64
	Slug             string
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	ShortDescription string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	CampaignID       *int64
	Total            int64
}

func (q *Queries) GetVtuberGalleries(ctx context.Context, arg GetVtuberGalleriesParams) ([]GetVtuberGalleriesRow, error) {
	rows, err := q.db.Query(ctx, getVtuberGalleries,
		arg.VtuberProfileID,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetVtuberGalleriesRow
	for rows.Next() {
		var i GetVtuberGalleriesRow
		if err := rows.Scan(
			&i.ID,
			&i.Slug,
			&i.VtuberProfileID,
			&i.MembershipOnly,
			&i.Description,
			&i.ShortDescription,
			&i.Title,
			&i.Name,
			&i.Media,
			&i.MediaType,
			&i.CategoryID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.CampaignID,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const listPosts = `-- name: ListPosts :many
WITH FILTERED AS (SELECT p.id, p.slug, p.vtuber_profile_id, p.membership_only, p.description, p.short_description, p.title, p.name, p.media, p.media_type, p.category_id, p.created_at, p.updated_at, p.deleted_at, p.campaign_id, vp.id as vtuber_id, vp.display_name as vtuber_name, vp.image as vtuber_image,
COALESCE(likes.like_count, 0) as likes,
COALESCE(comments.post_comments_count, 0) as comments,
              CASE
                             WHEN $5::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM post_likes pl
                        WHERE pl.post_id = p.id
                          AND pl.user_id = $5::BIGINT)
               THEN TRUE
           ELSE FALSE
           END as has_liked
                  FROM posts as p
                           INNER JOIN vtuber_profiles as vp ON vp.id = p.vtuber_profile_id
                     LEFT JOIN (SELECT COUNT(id) AS like_count, post_id
                    FROM post_likes
                    GROUP BY post_id) AS likes ON likes.post_id = p.id
         LEFT JOIN (SELECT COUNT(id) AS post_comments_count, post_id
                    FROM post_comments
                    GROUP BY post_id) AS comments ON comments.post_id = p.id
                  WHERE 
                  CASE 
                  WHEN $6::BIGINT IS NOT NULL THEN p.campaign_id = $6::BIGINT
                  WHEN $7::TEXT IS NOT NULL THEN vp.username = $7::TEXT AND campaign_id IS NULL
                  WHEN $8::BIGINT IS NOT NULL THEN p.vtuber_profile_id = $8::BIGINT 
                  ELSE TRUE
                  END
                    AND (p.category_id = COALESCE($9, p.category_id))
                    AND p.deleted_at IS NULL),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.id, f.slug, f.vtuber_profile_id, f.membership_only, f.description, f.short_description, f.title, f.name, f.media, f.media_type, f.category_id, f.created_at, f.updated_at, f.deleted_at, f.campaign_id, f.vtuber_id, f.vtuber_name, f.vtuber_image, f.likes, f.comments, f.has_liked, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'ASC' THEN id END ASC,
         CASE WHEN $1::TEXT = 'id' AND $2::TEXT = 'DESC' THEN id END DESC,
         CASE WHEN $1::TEXT = 'title' AND $2::TEXT = 'ASC' THEN title END ASC,
         CASE WHEN $1::TEXT = 'title' AND $2::TEXT = 'DESC' THEN title END DESC,
         CASE WHEN $1::TEXT = 'name' AND $2::TEXT = 'ASC' THEN name END ASC,
         CASE WHEN $1::TEXT = 'name' AND $2::TEXT = 'DESC' THEN name END DESC,
         CASE WHEN $1::TEXT = 'category_id' AND $2::TEXT = 'ASC' THEN category_id END ASC,
         CASE
             WHEN $1::TEXT = 'category_id' AND $2::TEXT = 'DESC' THEN category_id END DESC,
         CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN $1::TEXT = 'created_at' AND $2::TEXT = 'DESC' THEN created_at END DESC,
         CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'ASC' THEN updated_at END ASC,
         CASE WHEN $1::TEXT = 'updated_at' AND $2::TEXT = 'DESC' THEN updated_at END DESC
LIMIT $4 OFFSET $3
`

type ListPostsParams struct {
	Sort           string
	Order          string
	Offset         int32
	Limit          int32
	UserID         *int64
	CampaignID     *int64
	VtuberUsername *string
	VtuberID       *int64
	CategoryID     *int64
}

type ListPostsRow struct {
	ID               int64
	Slug             string
	VtuberProfileID  int64
	MembershipOnly   bool
	Description      string
	ShortDescription string
	Title            string
	Name             string
	Media            *string
	MediaType        *string
	CategoryID       int64
	CreatedAt        time.Time
	UpdatedAt        time.Time
	DeletedAt        *time.Time
	CampaignID       *int64
	VtuberID         int64
	VtuberName       string
	VtuberImage      *string
	Likes            int64
	Comments         int64
	HasLiked         bool
	Total            int64
}

func (q *Queries) ListPosts(ctx context.Context, arg ListPostsParams) ([]ListPostsRow, error) {
	rows, err := q.db.Query(ctx, listPosts,
		arg.Sort,
		arg.Order,
		arg.Offset,
		arg.Limit,
		arg.UserID,
		arg.CampaignID,
		arg.VtuberUsername,
		arg.VtuberID,
		arg.CategoryID,
	)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []ListPostsRow
	for rows.Next() {
		var i ListPostsRow
		if err := rows.Scan(
			&i.ID,
			&i.Slug,
			&i.VtuberProfileID,
			&i.MembershipOnly,
			&i.Description,
			&i.ShortDescription,
			&i.Title,
			&i.Name,
			&i.Media,
			&i.MediaType,
			&i.CategoryID,
			&i.CreatedAt,
			&i.UpdatedAt,
			&i.DeletedAt,
			&i.CampaignID,
			&i.VtuberID,
			&i.VtuberName,
			&i.VtuberImage,
			&i.Likes,
			&i.Comments,
			&i.HasLiked,
			&i.Total,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const updatePost = `-- name: UpdatePost :exec
UPDATE posts
SET title          = $1,
    description    = $2,
    media=$3,
    media_type=$4,
    name=$5,
    membership_only=$6,
    category_id=$7,
    short_description = $8,
    campaign_id = $9
WHERE id = $10
`

type UpdatePostParams struct {
	Title            string
	Description      string
	Media            *string
	MediaType        *string
	Name             string
	MembershipOnly   bool
	CategoryID       int64
	ShortDescription string
	CampaignID       *int64
	ID               int64
}

func (q *Queries) UpdatePost(ctx context.Context, arg UpdatePostParams) error {
	_, err := q.db.Exec(ctx, updatePost,
		arg.Title,
		arg.Description,
		arg.Media,
		arg.MediaType,
		arg.Name,
		arg.MembershipOnly,
		arg.CategoryID,
		arg.ShortDescription,
		arg.CampaignID,
		arg.ID,
	)
	return err
}
