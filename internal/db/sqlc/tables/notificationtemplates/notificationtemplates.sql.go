// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.29.0
// source: notificationtemplates.sql

package notificationtemplatesqueries

import (
	"context"
)

const getNotificationTemplatesByKey = `-- name: GetNotificationTemplatesByKey :many
SELECT title, description, json, deeplink, severity, key, language, created_at, updated_at from notification_templates WHERE key = $1
`

func (q *Queries) GetNotificationTemplatesByKey(ctx context.Context, key string) ([]NotificationTemplate, error) {
	rows, err := q.db.Query(ctx, getNotificationTemplatesByKey, key)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []NotificationTemplate
	for rows.Next() {
		var i NotificationTemplate
		if err := rows.Scan(
			&i.Title,
			&i.Description,
			&i.Json,
			&i.Deeplink,
			&i.Severity,
			&i.Key,
			&i.Language,
			&i.CreatedAt,
			&i.UpdatedAt,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}
