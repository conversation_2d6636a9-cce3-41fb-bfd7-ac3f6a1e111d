-- name: GetVtuberProfileByID :one
SELECT vp.*,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
FROM vtuber_profiles as vp
    LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
WHERE (vp.id::TEXT = sqlc.arg('id')::TEXT OR vp.username::TEXT=sqlc.arg('id')::TEXT) 
AND deleted_at IS NULL 
GROUP BY vp.id;

-- name: GetVtuberProfileByUserID :one
SELECT vp.*,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
FROM vtuber_profiles as vp
    LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
WHERE user_id = $1 
AND deleted_at IS NULL 
GROUP BY vp.id;

-- name: CreateVtuberProfile :one
INSERT INTO vtuber_profiles (
    user_id, display_name, furigana, image, banner_image, social_media_links,username
)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING *;

-- name: UpdateVtuberProfile :one
UPDATE vtuber_profiles
SET display_name = $1,
    furigana = $2,
    image = $3,
    banner_image = $4,
    social_media_links=$5,
    description = $6,
    is_plan_active = $7,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $8
RETURNING *;

-- name: VerifyVtuberProfile :exec
UPDATE users SET role = 'vtuber'
WHERE id = $1;



-- name: DeleteVtuberProfileByID :exec
UPDATE vtuber_profiles
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: ListVtuberProfiles :many
WITH FILTERED AS (SELECT vp.*,
      CASE 
        WHEN COUNT(vc.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(vc.category_id)::BIGINT[]
    END as category_ids
   FROM vtuber_profiles as vp
      LEFT JOIN vtuber_categories vc ON vp.id = vc.vtuber_profile_id
      WHERE display_name LIKE COALESCE('%' || sqlc.narg('display_name') || '%', display_name)
      AND (sqlc.narg('category_id')::BIGINT IS NULL OR vc.category_id = sqlc.narg('category_id')::BIGINT)
      AND deleted_at IS NULL
      GROUP BY vp.id
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.*, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'display_name' AND sqlc.arg('order')::TEXT = 'ASC' THEN display_name END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'display_name' AND sqlc.arg('order')::TEXT = 'DESC' THEN display_name END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'furigana' AND sqlc.arg('order')::TEXT = 'ASC' THEN furigana END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'furigana' AND sqlc.arg('order')::TEXT = 'DESC' THEN furigana END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');



-- name: GetVtuberUserId :one
SELECT user_id FROM vtuber_profiles WHERE id = $1;

-- name: GetVtuberId :one
SELECT id FROM vtuber_profiles WHERE user_id = $1;

-- name: GetVtuberProfileByUsername :one
SELECT * FROM vtuber_profiles
WHERE username = $1 ;
