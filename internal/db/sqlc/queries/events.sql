-- name: AddEvent :one
WITH inserted as (
    INSERT
        INTO events (vtuber_profile_id, user_id, title, description, image, rules, status, start_date,
                     end_date, short_description, participation_flow, benefits, requirements, overview, social_media_links,slug)
            VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16) RETURNING *)
SELECT inserted.id,
       inserted.title,
       inserted.description,
       inserted.image,
       inserted.rules,
       inserted.start_date,
       inserted.end_date,
       inserted.created_at,
       inserted.status,
       inserted.short_description,
       inserted.participation_flow,
       inserted.benefits,
       inserted.requirements,
       inserted.overview,
       inserted.social_media_links,
       inserted.slug,
       u.id            as userId,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage
FROM inserted
         LEFT JOIN users u ON inserted.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON inserted.vtuber_profile_id = vp.id;

-- name: GetAllEvents :many
WITH FILTERED AS (
  SELECT 
    events.id,
    events.title,
    events.description,
    events.image,
    events.rules,
    events.start_date,
    events.end_date,
    events.created_at,
    events.updated_at,
    events.status,
    events.short_description,
    events.participation_flow,
    events.benefits,
    events.requirements,
    events.overview,
    events.social_media_links,
    events.slug,
    u.id AS userId,
    u.full_name AS userName,
    u.image AS user_image,
    vp.id AS vtuberId,
    vp.display_name AS vtuberName,
    vp.image AS vtuberImage,
      CASE 
        WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
    END as category_ids
  FROM events
  LEFT JOIN users u 
    ON events.user_id = u.id
  LEFT JOIN vtuber_profiles vp 
    ON events.vtuber_profile_id = vp.id
  LEFT JOIN event_categories ec
    ON events.id = ec.event_id
  WHERE 
     (sqlc.narg('vtuber_id')::BIGINT IS NULL OR events.vtuber_profile_id = sqlc.narg('vtuber_id')::BIGINT)
    AND events.deleted_at IS NULL AND (sqlc.narg('category_id')::BIGINT IS NULL OR ec.category_id = sqlc.narg('category_id')::BIGINT)
    AND (sqlc.arg('is_admin')::boolean OR events.status = 'approved')
GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
),
COUNTED AS (
  SELECT COUNT(*) AS total
  FROM FILTERED
)
SELECT f.*, c.total
FROM FILTERED f, COUNTED c
ORDER BY 
  CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'ASC' THEN title END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'DESC' THEN title END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
  CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END,
  CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
LIMIT sqlc.arg('limit') 
OFFSET sqlc.arg('offset');


-- name: DeleteEventById :exec
UPDATE events
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;

-- name: UpdateEventById :exec
UPDATE events
SET title             = $1,
    description       = $2,
    image             = $3,
    rules             = $4,
    start_date        = $5,
    end_date          = $6,
    short_description = $7,
    participation_flow = $8,
    benefits          = $9,
    requirements      = $10,
    overview          = $11,
    social_media_links = $12
WHERE id = $13;

-- name: ApproveOrRejectEventById :exec
UPDATE events
SET status = $1
WHERE id = $2;


-- name: GetOneEvent :one
SELECT *
FROM events
WHERE (id::TEXT = sqlc.arg('id')::TEXT OR slug::TEXT = sqlc.arg('id')::TEXT);

-- name: GetEventById :one
SELECT events.id,
       events.title,
       events.description,
       events.image,
       events.rules,
       events.start_date,
       events.end_date,
       events.created_at,
       events.status,
       events.short_description,
       events.participation_flow,
       events.benefits,
       events.requirements,
       events.overview,
       events.social_media_links,
       events.slug,
       u.id            as user_id,
       u.full_Name     as userName,
       u.image         as user_image,
       vp.id           as vtuberId,
       vp.display_name as vtuberName,
       vp.image        as vtuberImage,
       CASE 
        WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
        ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
    END as category_ids
FROM events
         LEFT JOIN users u ON events.user_id = u.id 
         LEFT JOIN vtuber_profiles vp ON events.vtuber_profile_id = vp.id
         LEFT JOIN event_categories ec ON events.id = ec.event_id
WHERE (events.id::TEXT = sqlc.arg('id')::TEXT OR events.slug::TEXt = sqlc.arg('id')::TEXT) 
AND events.deleted_at IS NULL 
GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image;



-- name: GetMyEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                            events.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                        END as category_ids
                  FROM events
                           LEFT JOIN users u ON events.user_id = u.id
                           LEFT JOIN vtuber_profiles vp
                                     ON events.vtuber_profile_id = vp.id
                           LEFT JOIN event_categories ec
                            ON events.id = ec.event_id
                  WHERE events.deleted_at IS NULL AND events.vtuber_profile_id = sqlc.arg('vtuber_id')::BIGINT 
                  GROUP BY events.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
                  ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'ASC' THEN title
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'title' AND sqlc.arg('order')::TEXT = 'DESC' THEN title
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetUserEvents :many
WITH FILTERED AS (SELECT events.id,
                         events.title,
                         events.description,
                         events.image,
                         events.rules,
                         events.start_date,
                         events.end_date,
                         events.created_at,
                         events.updated_at,
                         events.status,
                         events.short_description,
                         events.participation_flow,
                         events.benefits,
                         events.requirements,
                         events.overview,
                         events.social_media_links,
                         events.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                          CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                        END as category_ids
                  FROM events
                           INNER JOIN users u ON events.user_id = u.id
                           LEFT JOIN event_categories ec ON events.id = ec.event_id
                  WHERE events.deleted_at IS NULL AND events.user_id = sqlc.arg('user_id')::BIGINT
                  
                  ),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at
             END
        DESC,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at
             END
        ,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at
             END
        DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetEventBySlug :one
SELECT * from events
WHERE slug = $1;
