-- name: AddVote :one
INSERT INTO event_participant_votes (event_participant_id,user_id,type,remarks,event_id,count)
VALUES ($1,$2,$3,$4,$5,$6) RETURNING *;

-- name: GetTodayVote :one
SELECT * FROM event_participant_votes
WHERE event_id = $1 AND
      user_id = $2 AND
      DATE(created_at) = CURRENT_DATE AND type='daily_point';

-- name: GetPlatformVote :one
SELECT * FROM event_participant_votes
WHERE event_participant_id = $1 AND type='platform_point';


-- name: UpdatePlatformVote :exec
UPDATE event_participant_votes SET user_id=$1, count=$2, remarks=$3
WHERE id=$4;
