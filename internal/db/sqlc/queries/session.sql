-- name: CreateSession :exec
INSERT INTO session (user_id, location, uuid, expires_at, ip_address, user_agent)
VALUES ($1, $2, $3, $4, $5, $6 );

-- name: GetSession :one
SELECT * FROM session WHERE uuid = $1 AND user_id = $2;

-- name: UpdateSession :exec
UPDATE session 
SET uuid = sqlc.arg('new_uuid'),
ip_address = $1,
user_agent = $2,
expires_at = $3
WHERE uuid = sqlc.arg('old_uuid') RETURNING *;

-- name: DeleteSession :exec
DELETE FROM session WHERE uuid = $1 AND user_id = $2;   

-- name: ListSessions :many
SELECT * FROM session WHERE user_id = $1 AND expired_at > CURRENT_TIMESTAMP;

-- name: DeleteSessionById :exec
DELETE FROM session WHERE id = $1;
