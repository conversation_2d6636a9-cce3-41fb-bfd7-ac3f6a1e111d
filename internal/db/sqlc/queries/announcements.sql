-- name: AddAnnouncement :one
INSERT INTO announcements ( image, description )
VALUES ($1, $2) RETURNING *;

-- name: GetAnnouncement :one
SELECT * FROM announcements LIMIT 1;

-- name: GetAnnouncementById :one
SELECT * FROM announcements WHERE id = $1;

-- name: ToggleAnnouncement :one
UPDATE announcements
SET active = NOT active, updated_at = CURRENT_TIMESTAMP
WHERE id = $1 RETURNING *;

-- name: UpdateAnnouncement :one
UPDATE announcements
SET image = $1, description = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING *;
