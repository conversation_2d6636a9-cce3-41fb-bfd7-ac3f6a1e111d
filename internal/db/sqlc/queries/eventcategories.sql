
-- name: AddEventCategories :copyfrom
INSERT INTO event_categories (event_id, category_id) VALUES ($1, $2);

-- name: DeleteEventCategories :exec
DELETE FROM event_categories WHERE event_id = $1;

-- name: GetEventCategories :many
SELECT DISTINCT(c.id), c.name, c.description, c.created_at, c.slug FROM event_categories
ec INNER JOIN categories c ON ec.category_id = c.id
INNER JOIN events e ON e.id = ec.event_id WHERE e.status = 'approved' AND e.deleted_at IS NULL
ORDER BY c.created_at DESC LIMIT 3;
