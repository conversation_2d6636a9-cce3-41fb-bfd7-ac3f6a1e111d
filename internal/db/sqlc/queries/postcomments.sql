-- name: AddPostComment :one
WITH inserted AS (INSERT INTO post_comments (post_id, user_id, parent_id, content, vtuber_id)
    VALUES ($1, $2, $3, $4, $5)
    RETURNING *)
SELECT inserted.*,
       u.full_name     as userName,
       u.id            as userId,
       u.image         as userImage,
       vp.id           as vtuberId,
       vp.display_name as vtuberDisplayName,
       vp.image        as vtuberImage
FROM inserted
         JOIN users u ON inserted.user_id = u.id
         LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id;


-- name: GetAllPostComments :many
WITH FILTERED AS (SELECT ps.id             AS id,
                         ps.id             AS user_id,
                         ps.vtuber_id      as comment_vtuber_id,
                         ps.content        AS content,
                         ps.post_id        AS post_id,
                         ps.parent_id      AS parent_id,
                         ps.created_at     AS created_at,
                         ps.updated_at     AS updated_at,
                         u.id              AS user_id_from_users,
                         u.full_name       AS full_name,
                         u.image           AS image,
                         vp.id             AS vtuber_id,
                         vp.display_name   AS vtuber_display_name,
                         vp.image          AS vtuber_image,
                         COUNT(vps.id) > 0 AS has_replies
                  FROM post_comments AS ps
                           LEFT JOIN
                       post_comments AS vps ON vps.parent_id = ps.id
                           INNER JOIN
                       users AS u ON u.id = ps.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                           INNER JOIN posts p ON p.id = ps.post_id
                  WHERE (ps.post_id::TEXT = sqlc.arg('post_id')::TEXT OR p.slug::TEXT = sqlc.arg('post_id')::TEXT)
                    AND ps.parent_id IS NULL
                  GROUP BY ps.id, ps.content, ps.post_id, ps.parent_id, ps.created_at, ps.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.id END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: GetPostCommentById :one
SELECT *
FROM post_comments
WHERE id = $1;

-- name: DeletePostCommentById :exec
DELETE
FROM post_comments
WHERE id = $1;

-- name: UpdatePostCommentById :exec
UPDATE post_comments
SET content = $1
WHERE id = $2;

-- name: GetAllRepliesOfComment :many
WITH FILTERED AS (SELECT ps.id             AS id,
                         ps.id             AS user_id,
                            ps.vtuber_id      as comment_vtuber_id,
                         ps.content        AS content,
                         ps.post_id        AS post_id,
                         ps.parent_id      AS parent_id,
                         ps.created_at     AS created_at,
                         ps.updated_at     AS updated_at,
                         u.id AS user_id_from_users,
                         u.full_name AS full_name,
                         u.image AS image,
                         vp.id AS vtuber_id,
                         vp.display_name AS vtuber_display_name,
                         vp.image AS vtuber_image,
                         COUNT(vps.id) > 0 AS has_replies
                  FROM post_comments AS ps
                           LEFT JOIN
                       post_comments AS vps ON vps.parent_id = ps.id
                           INNER JOIN
                       users AS u ON u.id = ps.user_id
                            LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ps.parent_id = $1
                  GROUP BY ps.id, ps.content, ps.post_id, ps.parent_id, ps.created_at, ps.updated_at, u.id, vp.id),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.id END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');