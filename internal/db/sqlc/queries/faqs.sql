-- name: CreateFaq :one
    INSERT INTO faqs ( question, response, index, active, language, tag)
    VALUES ($1, $2, $3, $4, $5, $6) RETURNING *;

-- name: GetAllFaqs :many
SELECT * FROM faqs WHERE active= COALESCE(sqlc.narg('isActive'), active) AND language = COALESCE(sqlc.narg('language'), language) ORDER BY index ASC;

-- name: GetFaqById :one
SELECT * FROM faqs WHERE id = $1;

-- name: UpdateFaqById :exec
UPDATE faqs SET question = $1, response = $2, index = $3, language = $4, tag = $5 WHERE id = $6 RETURNING *;

-- name: DeleteFaqById :exec
DELETE FROM faqs WHERE id = $1;

-- name: ToggleFaqActive :exec
UPDATE faqs SET active = $1 WHERE id = $2 RETURNING *;
