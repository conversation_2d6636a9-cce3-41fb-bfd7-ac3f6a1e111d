-- name: AddVtuberUserSubscription :one
INSERT INTO vtuber_user_subscriptions
(vtuber_id, user_id, vtuber_plan_id, is_recurring, expires_on)
VALUES ($1, $2, $3, $4, $5) RETURNING *;

-- name: GetVtuberUserSubById :one
SELECT cs.id, cs.vtuber_id,cs.vtuber_plan_id,cs.created_at,cs.is_recurring,cs.expires_on,cs.updated_at, u.id as user_id, u.full_name as user_name, u.image as user_image , u.email as user_email
FROM vtuber_user_subscriptions cs
         INNER JOIN users u ON cs.user_id = u.id WHERE cs.id=$1 AND cs.deleted_at IS NULL;

-- name: GetVtuberUserSubscription :many
WITH FILTERED AS (SELECT cs.id, cs.vtuber_id,cs.vtuber_plan_id,cs.created_at,cs.is_recurring,cs.expires_on,cs.updated_at, u.id as user_id, u.full_name as user_name, u.image as user_image , u.email as user_email
   FROM vtuber_user_subscriptions cs
   INNER JOIN users u ON cs.user_id = u.id
   WHERE
   cs.vtuber_id = COALESCE(sqlc.narg('vtuber_id'),cs.vtuber_id) AND
    cs.user_id = COALESCE(sqlc.narg('user_id'),cs.user_id) AND
    cs.vtuber_plan_id = COALESCE(sqlc.narg('vtuber_plan_id'),cs.vtuber_plan_id) AND cs.deleted_at IS NULL
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.*, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'is_recurring' AND sqlc.arg('order')::TEXT = 'ASC' THEN is_recurring END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'is_recurring' AND sqlc.arg('order')::TEXT = 'DESC' THEN is_recurring END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'expires_on' AND sqlc.arg('order')::TEXT = 'ASC' THEN expires_on END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'expires_on' AND sqlc.arg('order')::TEXT = 'DESC' THEN expires_on END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetVtuberUserSubscriptionByVtuberIdAndUserIdAndVtuberSubId :one
SELECT * FROM vtuber_user_subscriptions WHERE
user_id = $1 AND
vtuber_id = $2 AND
vtuber_plan_id = $3 AND deleted_at IS NULL;

-- name: DeleteVtuberUserSubscription :exec
UPDATE vtuber_user_subscriptions SET deleted_at = CURRENT_TIMESTAMP WHERE id=$1;

-- name: UpdateVtuberUserSubscription :exec
UPDATE vtuber_user_subscriptions
SET is_recurring = $1 WHERE id = $2;

-- name: UpdateIfExist :one
UPDATE vtuber_user_subscriptions
SET is_recurring = $1, expires_on = $2 , vtuber_plan_id=$3
WHERE id=$4 RETURNING *;

-- name: IsUserSubscribed :one
SELECT COUNT(cs.id) FROM vtuber_user_subscriptions cs
INNER JOIN vtuber_profiles v ON v.id = cs.vtuber_id
WHERE (cs.vtuber_id::TEXT = sqlc.arg('vtuber_id')::TEXT OR v.username = sqlc.arg('vtuber_id')::TEXT) AND cs.user_id = $1 AND cs.deleted_at IS NULL;

-- name: DeleteUserVtuberSubscriptionByUserId :exec
UPDATE vtuber_user_subscriptions SET deleted_at = CURRENT_TIMESTAMP WHERE user_id = $1;


-- name: CheckRunningSubscription :one
SELECT * from vtuber_user_subscriptions WHERE user_id=$1 AND vtuber_id=$2 AND  DATE(expires_on) > CURRENT_DATE;
