-- name: GetAllStaticResource :many
SELECT * FROM static_data WHERE key = $1;

-- name: AddStaticResource :one
INSERT INTO static_data (key, value, language, data) VALUES ($1, $2, $3, $4) RETURNING *;

-- name: UpdateStaticResource :one
UPDATE static_data SET value = $1, data = $2 WHERE id = $3 RETURNING *;

-- name: DeleteStaticResource :exec
DELETE FROM static_data WHERE id = $1;

-- name: GetStaticResourceByKeyAndLanguage :one
SELECT * FROM static_data WHERE key = $1 AND language = $2;

-- name: GetStaticResourceById :one
SELECT * FROM static_data WHERE id = $1;
