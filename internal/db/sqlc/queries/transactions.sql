-- name: AddTransaction :one
INSERT INTO transactions  (user_id, amount, type, status, gmo_order_id, vtuber_id,details)
VALUES ($1, $2, $3, $4, $5, $6,$7)
RETURNING *;

-- name: GetTransactionByID :one
SELECT * FROM transactions WHERE id = $1;

-- name: GetAllTransactions :many
WITH FILTERED AS (SELECT *
FROM
    transactions
WHERE
    ((sqlc.narg('user_id')::bigint IS NULL) OR user_id = sqlc.narg('user_id')::bigint)
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: CheckGmoOrderIdExist :one
SELECT * FROM transactions WHERE gmo_order_id = $1;