-- name: AddVtuberGallery :one
INSERT INTO vtuber_galleries (vtuber_id, media, media_type, description)
VALUES ($1, $2, $3, $4) RETURNING *;

-- name: GetVtuberGalleryById :one
SELECT * FROM vtuber_galleries WHERE id = $1;

-- name: GetVtuberGalleries :many
WITH FILTERED AS (
    SELECT * FROM vtuber_galleries WHERE vtuber_id = $1
),
COUNTED AS (
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total
FROM FILTERED f, COUNTED c
ORDER BY
    -- Primary Sort
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,

    -- Secondary Sort (tie-breaker)
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC

LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: DeleteVtuberGalleryById :exec
DELETE FROM vtuber_galleries WHERE id = $1;

-- name: UpdateVtuberGalleryById :exec
UPDATE vtuber_galleries SET media = $1, media_type = $2, description = $3 WHERE id = $4;
