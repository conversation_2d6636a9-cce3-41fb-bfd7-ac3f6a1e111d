-- name: GetCreatorRequestByVtuberId :one
SELECT * FROM vtuber_requests c
INNER JOIN vtuber_profiles v 
ON v.user_id = c.user_id 
WHERE v.id = $1 AND
v.deleted_at IS NULL;


-- name: GetCreatorRequestByUserID :one
SELECT * FROM vtuber_requests WHERE user_id = $1;

-- name: ApplyForVtuberAccess :exec
INSERT INTO vtuber_requests (description, user_id) VALUES ($1, $2);

-- name: UpdateCreatorRequest :exec
UPDATE vtuber_requests SET status = 'pending' , reason = NULL , description = $1 WHERE id = $2 ;

-- name: GetAllCreatorRequests :many
WITH FILTERED AS (SELECT cr.*, u.full_name as name
   FROM vtuber_requests cr INNER JOIN users u ON u.id = cr.user_id),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.*, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'status' AND sqlc.arg('order')::TEXT = 'ASC' THEN status END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'status' AND sqlc.arg('order')::TEXT = 'DESC' THEN status END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'reason' AND sqlc.arg('order')::TEXT = 'ASC' THEN reason END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'reason' AND sqlc.arg('order')::TEXT = 'DESC' THEN reason END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetCreatorRequestByID :one
SELECT * FROM vtuber_requests WHERE id = $1;

-- name: DenyCreatorRequest :exec
UPDATE vtuber_requests SET status = 'rejected', reason = $1 WHERE id = $2;

-- name: ApproveCreatorRequest :exec
UPDATE vtuber_requests SET status = 'approved' WHERE id = $1;