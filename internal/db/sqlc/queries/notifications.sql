-- name: AddNotification :one
INSERT INTO notifications (title,title_jp, description,description_jp,  json, deeplink, severity, user_id, vtuber_id )
VALUES ($1, $2, $3, $4, $5, $6, $7, $8,$9)
RETURNING *;

-- name: GetNotification :one
SELECT * FROM notifications WHERE id = $1;

-- name: ListNotifications :many
WITH FILTERED AS (SELECT *
FROM notifications
WHERE user_id= COALESCE(sqlc.narg('user_id') , user_id)
AND vtuber_id=COALESCE(sqlc.narg('vtuber_id') , vtuber_id)
ORDER BY created_at DESC
),
COUNTED AS(
    SELECT COUNT(*) AS total FROM FILTERED
)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: ReadNotification :exec
UPDATE notifications SET is_read = true WHERE id = $1;

-- name: UnreadNotification :exec
UPDATE notifications SET is_read = false WHERE id = $1;

-- name: DeleteNotification :exec
DELETE FROM notifications WHERE id = $1;

-- name: GetNotificationCount :one
SELECT COUNT(id) FROM notifications WHERE user_id = $1 AND is_read=false;
