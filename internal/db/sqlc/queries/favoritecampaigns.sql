-- name: Add<PERSON>avoriteCampaign :one
INSERT INTO favorite_campaigns (campaign_id, user_id) VALUES ($1, $2) RETURNING *;

-- name: DeleteFavoriteCampaign :exec
DELETE FROM favorite_campaigns WHERE campaign_id = $1 AND user_id = $2;

-- name: GetFavoriteCampaign :many
WITH FILTERED AS (SELECT fc.*, c.name, c.thumbnail, c.short_description FROM favorite_campaigns fc INNER JOIN campaigns c ON fc.campaign_id = c.id WHERE fc.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetFavoriteCampaignByUserIdAndCampaignId :one
SELECT * FROM favorite_campaigns WHERE user_id = $1 AND campaign_id = $2;

-- name: CountFavoriteCampaign :one
SELECT COUNT(id) FROM favorite_campaigns WHERE campaign_id = $1;

-- name: PopularCampaign :one
SELECT COUNT(fc.id) as popular, fc.campaign_id FROM favorite_campaigns fc
INNER JOIN campaigns c ON c.id = fc.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY campaign_id ORDER BY popular DESC LIMIT 1;
