-- name: ParticipantInEvent :one
INSERT INTO event_participants(event_id,vtuber_id) VALUES ($1,$2) RETURNING *;

-- name: GetEventParticipantByEventID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id, ep.remarks, vp.social_media_links, ep.vtuber_id,ep.status, vp.id as event_vtuber_id, vp.display_name as vtuber_name, vp.furigana as vtuber_furigana, vp.description as vtuber_introduction, vp.image as vtuber_image,ep.created_at,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                         FROM event_participants ep
INNER JOIN vtuber_profiles vp ON vp.id=ep.vtuber_id
INNER JOIN events e ON e.id=ep.event_id 
WHERE (ep.event_id::TEXT=sqlc.arg('event_id')::TEXT OR e.slug::TEXT = sqlc.arg('event_id')::TEXT) AND ep.status='accepted'),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetEventParticipantByVtuberID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.created_at,
                         e.title,
                         e.description,
                         e.image,
                         e.rules,
                         ep.remarks,
                         e.start_date,
                         e.end_date,
                         e.created_at as event_created_at,
                         e.updated_at,
                         e.status as event_status,
                         e.short_description,
                         e.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         vp.description as vtuber_introduction,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                         END as category_ids,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                  FROM event_participants ep
                           INNER JOIN events e ON e.id=ep.event_id
                           LEFT JOIN users u ON e.user_id = u.id
                           LEFT JOIN vtuber_profiles vp ON e.vtuber_profile_id = vp.id
                            LEFT JOIN event_categories ec ON ep.event_id = ec.event_id
                            WHERE ep.vtuber_id=sqlc.arg('vtuber_id')::BIGINT 
                          
                  GROUP BY ep.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image, e.title, e.description, e.image, e.rules, e.start_date, e.end_date, e.created_at, e.updated_at, e.status, e.short_description, e.slug, vp.description
                  ),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetEventParticipantByEventIDAndVtuberID :one
SELECT * FROM event_participants WHERE event_id = $1 AND vtuber_id = $2;

-- name: ChangeStatus :exec
UPDATE event_participants SET status = $1 , remarks=$2  WHERE id = $3;

-- name: GetAllEventParticipants :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.created_at,
                         e.title,
                         e.description,
                         e.image,
                         e.rules,
                         e.start_date,
                         ep.remarks,
                         e.end_date,
                         e.created_at as event_created_at,
                         e.updated_at,
                         e.status as event_status,
                         e.short_description,
                         e.slug,
                         u.id            as userId,
                         u.full_Name     as userName,
                         u.image         as user_image,
                         vp.id           as vtuberId,
                         vp.display_name as vtuberName,
                         vp.image        as vtuberImage,
                         vp.description as vtuber_introduction,
                         v.id as event_vtuber_id, v.display_name as event_vtuber_name, v.furigana as event_vtuber_furigana, v.image as event_vtuber_image,
                         CASE 
                            WHEN COUNT(ec.category_id) = 0 THEN ARRAY[]::BIGINT[]
                            ELSE ARRAY_AGG(ec.category_id)::BIGINT[]
                         END as category_ids,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                  FROM event_participants ep
                           INNER JOIN events e ON e.id=ep.event_id
                           INNER JOIN vtuber_profiles v on v.id = ep.vtuber_id
                           LEFT JOIN users u ON e.user_id = u.id
                           LEFT JOIN vtuber_profiles vp ON e.vtuber_profile_id = vp.id
                           LEFT JOIN event_categories ec ON ep.event_id = ec.event_id
                  GROUP BY ep.id, u.id, u.full_Name, u.image, vp.id, vp.display_name, vp.image
                  ),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
    CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetEventParticipantByID :one
SELECT * FROM event_participants WHERE id = $1;

-- name: GetCreatorEventParticipation :many 
SELECT event_id FROM event_participants WHERE vtuber_id = $1 ORDER BY created_at DESC;


-- name: GetAllEventParticipantByEventID :many
WITH FILTERED AS (SELECT ep.id,ep.event_id,ep.vtuber_id,ep.status, ep.remarks, vp.id as event_vtuber_id, vp.display_name as vtuber_name, vp.furigana as vtuber_furigana, vp.description as vtuber_introduction, vp.image as vtuber_image,ep.created_at,
                         (CAST((SELECT COALESCE(SUM(epv.count), 0) FROM event_participant_votes epv WHERE event_participant_id = ep.id) AS INTEGER)) as vote_count
                         FROM event_participants ep
INNER JOIN vtuber_profiles vp ON vp.id=ep.vtuber_id WHERE ep.event_id::TEXT=sqlc.arg('event_id')::TEXT),
        COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END,
       CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: GetTopTenEventParticipantVtubers :many
SELECT vp.display_name, vp.image, vp.id, COALESCE(vote_count.count, 0) as total_votes
FROM event_participants ep INNER JOIN vtuber_profiles vp ON ep.vtuber_id = vp.id
FULL OUTER JOIN (SELECT sum(count) as count, event_participant_id FROM event_participant_votes WHERE event_participant_votes.event_id = $1 GROUP BY event_participant_id) vote_count ON vote_count.event_participant_id = ep.id
WHERE ep.event_id = $1 AND ep.status = 'accepted'
ORDER BY total_votes DESC
LIMIT 10;