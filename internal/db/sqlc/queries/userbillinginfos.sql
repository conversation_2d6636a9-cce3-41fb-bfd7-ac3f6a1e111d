-- name: AddBillingInfo :one
INSERT INTO user_billing_infos (user_id, gmo_member_id, full_name, address_1, address_2, city, state, country, postal_code, company_name, vat_number,card_no)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11,$12)
RETURNING *;


-- name: GetBillingInfoById :one
SELECT * FROM user_billing_infos WHERE id = $1;

-- name: GetBillingInfoByUserId :many
SELECT * FROM user_billing_infos WHERE user_id = $1;

-- name: UpdateBillingInfo :one
UPDATE user_billing_infos SET
    full_name = $2,
    address_1 = $3,
    address_2 = $4,
    city = $5,
    state = $6,
    country = $8,
    postal_code = $9,
    company_name = $10,
    vat_number = $7,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $1
RETURNING *;


-- name: DeleteBillingInfo :exec
DELETE FROM user_billing_infos WHERE id = $1;

-- name: CountBillingInfo :one
SELECT COUNT(*) FROM user_billing_infos WHERE user_id = $1;