-- name: AddUserDeliveryAddress :one
INSERT INTO user_delivery_address (
    user_id,
    recipient,
    phone_number,
    postal_code,
    prefecture,
    city,
    address_line1,
    address_line2,
    preferred_delivery_time,
    preferred_delivery_date
) VALUES (
    $1,
    $2,
    $3,
    $4,
    $5,
    $6,
    $7,
    $8,
    $9,
    $10 
) RETURNING *;


-- name: GetUserDeliveryAddressById :one
SELECT * FROM user_delivery_address
WHERE id = $1 ;

-- name: GetUserDeliveryAddressByUserId :one
SELECT * FROM user_delivery_address
WHERE user_id = $1;

-- name: UpdateUserDeliveryAddress :exec
UPDATE user_delivery_address
SET
    recipient = $1,
    phone_number = $2,
    postal_code = $3,
    prefecture = $4,
    city = $5,
    address_line1 = $6,
    address_line2 = $7,
    preferred_delivery_time = $8,
    preferred_delivery_date = $9,
    updated_at = CURRENT_TIMESTAMP
WHERE id = $10;

-- name: DeleteUserDeliveryAddress :exec
DELETE FROM user_delivery_address
WHERE id = $1;