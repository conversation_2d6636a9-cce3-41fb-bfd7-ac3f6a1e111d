-- name: CreateVerificationCode :one
INSERT INTO verification_codes (user_id, code, type, email, expires_at)
VALUES ($1, $2, $3, $4, $5) RETURNING *;

-- name: GetVerificationCode :one
SELECT * FROM verification_codes WHERE  
(sqlc.narg('user_id')::BIGINT IS NULL OR user_id = sqlc.narg('user_id')::BIGINT)
AND (sqlc.narg('email')::TEXT IS NULL OR email = sqlc.narg('email')::TEXT)
AND type = $1;

-- name: GetEmailChangeVerificationCode :one
SELECT * FROM verification_codes WHERE user_id = $1 AND type = $2;

-- name: UpdateVerificationCode :exec
UPDATE verification_codes
SET code = $1, expires_at = $2, updated_at = CURRENT_TIMESTAMP
WHERE id = $3 RETURNING *;

-- name: DeleteVerificationCode :exec
DELETE FROM verification_codes WHERE id = $1;