-- name: AddPostLike :one
INSERT INTO post_likes (post_id, user_id) VALUES
($1, $2)
RETURNING *;

-- name: GetPostLikeCount :one
SELECT COUNT(id) FROM post_likes WHERE post_id = $1;

-- name: DeletePostLikeByPostId :exec
DELETE FROM post_likes WHERE post_id = $1 AND user_id = $2;

-- name: GetPostLikeByUserIdAndPostId :one
SELECT * FROM post_likes WHERE user_id = $1 AND post_id = $2;

-- name: GetPostLikesOfUser :many
WITH FILTERED AS (SELECT *
   FROM post_likes
    WHERE user_id = $1
   ),
   COUNTED AS(
      SELECT COUNT(*) AS total FROM FILTERED
   )
   SELECT f.*, c.total FROM FILTERED f, COUNTED c
   ORDER BY
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN id END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'user_id' AND sqlc.arg('order')::TEXT = 'ASC' THEN user_id END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'user_id' AND sqlc.arg('order')::TEXT = 'DESC' THEN user_id END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN updated_at END ASC,
      CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN updated_at END DESC
   LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');