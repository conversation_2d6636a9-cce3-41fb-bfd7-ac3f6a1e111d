-- name: AddVtuberBanner :one
INSERT INTO vtuber_banners (vtuber_id, image)
VALUES ($1, $2) RETURNING *;

-- name: GetVtuberBannerByVtuberId :many
SELECT vtuber_banners.* FROM vtuber_banners INNER JOIN vtuber_profiles ON vtuber_banners.vtuber_id = vtuber_profiles.id WHERE vtuber_profiles.id::TEXT = sqlc.arg('id')::TEXT OR vtuber_profiles.username::TEXT = sqlc.arg('id')::TEXT ORDER BY vtuber_banners.created_at DESC;

-- name: DeleteVtuberBannerById :exec
DELETE FROM vtuber_banners WHERE id = $1;

-- name: UpdateVtuberBannerById :exec
UPDATE vtuber_banners SET image = $1 WHERE id = $2;

-- name: GetVtuberBannerById :one
SELECT * FROM vtuber_banners WHERE id = $1;

-- name: CountVtuberBannerByVtuberId :one
SELECT COUNT(*) FROM vtuber_banners WHERE vtuber_id = $1;