-- name: CreateAccount :one
INSERT INTO accounts (account_id, provider_id, user_id, access_token, refresh_token, id_token, access_token_expires, refresh_token_expires, scope, password , last_three_password)
VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10 , $11) RETURNING *;

-- name: GetAccountByEmail :one
SELECT sqlc.embed(users) , sqlc.embed(accounts) FROM accounts INNER JOIN users ON accounts.user_id = users.id WHERE accounts.provider_id = 'email' AND users.email = $1;

-- name: UpdateAccountPassword :exec
UPDATE accounts
SET password = $1, last_three_password = $2
WHERE id = $3 AND provider_id = 'email' AND user_id = $4
RETURNING *;

-- name: GetAccountByProviderAndAccountID :one
SELECT sqlc.embed(users) , sqlc.embed(accounts) FROM accounts INNER JOIN users ON accounts.user_id = users.id
WHERE accounts.provider_id = $1 AND accounts.account_id = $2;


-- name: UpdateAccountTokens :one
UPDATE accounts
SET access_token = $1, refresh_token = $2, id_token = $3,
    access_token_expires = $4, refresh_token_expires = $5, scope = $6
WHERE id = $7 AND provider_id = $8 AND user_id = $9
RETURNING *;