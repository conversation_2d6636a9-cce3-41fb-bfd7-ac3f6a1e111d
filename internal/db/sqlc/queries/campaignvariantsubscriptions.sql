-- name: AddCampaignVariantSubscription :one
INSERT INTO campaign_variant_subscriptions (user_id, campaign_variant_id, vtuber_id, price,campaign_id, comment)
VALUES ($1,$2,$3,$4,$5, $6) RETURNING *;

-- name: DeleteCampaignVariantSubscription :exec
DELETE FROM campaign_variant_subscriptions WHERE id=$1;

-- name: GetCampaignVariantSubscriptionByUserIdAndCampaignVariantId :one
SELECT * FROM campaign_variant_subscriptions WHERE user_id=$1 AND campaign_variant_id=$2;

-- name: GetCampaignVariantSubscriptionCount :one
SELECT COUNT(*) FROM campaign_variant_subscriptions WHERE campaign_variant_id=$1;

-- name: PopularCampaign :one
SELECT SUM(cvs.price) as price, cvs.campaign_id
FROM campaign_variant_subscriptions cvs 
INNER JOIN campaigns c ON c.id = cvs.campaign_id 
WHERE c.deleted_at IS NULL
GROUP BY cvs.campaign_id ORDER BY price DESC LIMIT 1;


-- name: GetUserCampaignVariantSubscriptions :many
WITH FILTERED AS (SELECT
                      cvs.*,
                      c.name as campaign_name,
                      c.thumbnail as campaign_thumbnail,
                      cv.title AS campaign_variant_title,
                      cv.description AS campaign_variant_description,
                      cv.image AS campaign_variant_image
                  FROM campaign_variant_subscriptions cvs
                           INNER JOIN campaign_variants cv ON cvs.campaign_variant_id = cv.id
                           INNER JOIN campaigns c ON c.id = cvs.campaign_id
                  WHERE cvs.user_id = $1
),
        COUNTED AS (SELECT COUNT(*) AS total
                    FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN created_at END ASC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN created_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');
