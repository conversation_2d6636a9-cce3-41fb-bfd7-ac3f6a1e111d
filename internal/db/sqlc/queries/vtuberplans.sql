-- name: <PERSON><PERSON><PERSON>tuberPlan :one
INSERT INTO vtuber_plans
    (vtuber_id, description, title, price, index, short_description, annual_price)
VALUES ($1, $2, $3, $4, $5, $6, $7)
RETURNING *;

-- name: FindVtuberPlanById :one
SELECT *
FROM vtuber_plans
WHERE id = $1
  AND deleted_at IS NULL;

-- name: GetVtuberPlanById :one
SELECT cs.*,
       CASE
           WHEN sqlc.narg('user_id')::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM vtuber_user_subscriptions cus
                        WHERE cus.vtuber_plan_id = cs.id AND cus.deleted_at IS NULL
                          AND cus.user_id = sqlc.narg('user_id')::BIGINT)
               THEN TRUE
           ELSE FALSE
           END AS subscribed
FROM vtuber_plans cs
WHERE cs.id = $1
  AND cs.deleted_at IS NULL;

-- name: GetVtuberPlanByIndexAndVtuberId :one
SELECT *
FROM vtuber_plans
WHERE vtuber_id = $1
  AND index = $2
  AND deleted_at IS NULL;

-- name: GetVtuberPlansByVtuber :many
SELECT cs.*,
       CASE
           WHEN sqlc.narg('user_id')::BIGINT IS NULL THEN FALSE
           WHEN EXISTS (SELECT 1
                        FROM vtuber_user_subscriptions cus
                        WHERE cus.vtuber_plan_id = cs.id AND cus.deleted_at IS NULL
                          AND cus.user_id = sqlc.narg('user_id')::BIGINT)
               THEN TRUE
           ELSE FALSE
           END AS subscribed
FROM vtuber_plans cs
WHERE cs.vtuber_id = $1
  AND cs.deleted_at IS NULL
ORDER BY cs.index ASC;

-- name: UpdateVtuberPlan :exec
UPDATE vtuber_plans
SET description = $1,
    title       = $2,
    price       = $3,
    index       = $4,
    short_description = $5,
    annual_price = $6
WHERE id = $7;

-- name: DeleteVtuberPlan :exec
UPDATE vtuber_plans
SET deleted_at = CURRENT_TIMESTAMP
WHERE id = $1;


-- name: CountVtuberPlans :one
SELECT COUNT(id) FROM vtuber_plans WHERE vtuber_id = $1 AND deleted_at IS NULL;  