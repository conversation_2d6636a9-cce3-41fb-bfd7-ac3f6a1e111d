-- name: Add<PERSON>avoriteVtuber :one
WITH inserted AS ( INSERT INTO favorite_vtubers (vtuber_id, user_id) VALUES ($1, $2) RETURNING *) 
SELECT inserted.*, vp.username FROM inserted INNER JOIN vtuber_profiles vp ON inserted.vtuber_id = vp.id;

-- name: DeleteFavoriteVtuber :exec
DELETE FROM favorite_vtubers WHERE vtuber_id = $1 AND user_id = $2;

-- name: GetFavoriteVtuber :many
WITH FILTERED AS (SELECT fv.*, vp.display_name as vtuber_name, vp.image as vtuber_image, vp.username FROM favorite_vtubers fv INNER JOIN vtuber_profiles vp ON fv.vtuber_id = vp.id WHERE fv.user_id = $1),
     COUNTED AS (SELECT COUNT(*) AS total FROM FILTERED)
SELECT f.*, c.total FROM FILTERED f, COUNTED c
ORDER BY
    created_at DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

-- name: GetFavoriteVtuberByUserIdAndVtuberId :one
SELECT * FROM favorite_vtubers WHERE user_id = $1 AND vtuber_id = $2;