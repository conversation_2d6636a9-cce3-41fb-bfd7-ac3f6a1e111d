-- name: AddEventComment :one
WITH inserted AS (INSERT INTO event_comments (event_id, user_id, parent_id, content, vtuber_id) VALUES ($1, $2, $3, $4,
                                                                                                       $5) RETURNING *)
SELECT inserted.*,
       u.full_name     as userName,
       u.id            as userId,
       u.image         as userImage,
       vp.id           as vtuberId,
       vp.display_name as vtuberDisplayName,
       vp.image        as vtuberImage
FROM inserted
         JOIN users u ON inserted.user_id = u.id
         LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id;

-- name: GetAllEventComments :many
WITH FILTERED AS (SELECT ec.id            AS id,
                         ec.user_id       AS user_id,
                         ec.vtuber_id     as comment_vtuber_id,
                         ec.content       AS content,
                         ec.event_id      AS event_id,
                         ec.parent_id     AS parent_id,
                         ec.created_at    AS created_at,
                         ec.updated_at    AS updated_at,
                         u.id             AS user_id_from_users,
                         u.full_name      AS full_name,
                         u.image          AS image,
                         vp.id            AS vtuber_id,
                         vp.display_name  AS vtuber_display_name,
                         vp.image         AS vtuber_image,
                         COUNT(ve.id) > 0 AS has_replies
                  FROM event_comments AS ec
                           LEFT JOIN
                       event_comments AS ve ON ve.parent_id = ec.id
                           INNER JOIN
                       users AS u ON u.id = ec.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ec.event_id = $1
                    AND ec.parent_id IS NULL
                  GROUP BY ec.id, ec.content, ec.event_id, ec.parent_id, ec.created_at, ec.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.id END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');


-- name: GetEventCommentById :one
SELECT *
FROM event_comments
WHERE id = $1;

-- name: DeleteEventCommentById :exec
DELETE
FROM event_comments
WHERE id = $1;

-- name: UpdateEventCommentById :exec
UPDATE event_comments
SET content = $1
WHERE id = $2;

-- name: GetAllRepliesOfEventComment :many
WITH FILTERED AS (SELECT ec.id            AS id,
                         ec.user_id       AS user_id,
                         ec.vtuber_id     as comment_vtuber_id,
                         ec.content       AS content,
                         ec.event_id      AS event_id,
                         ec.parent_id     AS parent_id,
                         ec.created_at    AS created_at,
                         ec.updated_at    AS updated_at,
                         u.id             AS user_id_from_users,
                         u.full_name      AS full_name,
                         u.image          AS image,
                         vp.id            AS vtuber_id,
                         vp.display_name  AS vtuber_display_name,
                         vp.image         AS vtuber_image,
                         COUNT(ve.id) > 0 AS has_replies
                  FROM event_comments AS ec
                           LEFT JOIN event_comments AS ve ON ve.parent_id = ec.id
                           INNER JOIN users AS u ON u.id = ec.user_id
                           LEFT JOIN vtuber_profiles vp ON u.id = vp.user_id
                  WHERE ec.parent_id = $1
                  GROUP BY ec.id, ec.content, ec.event_id, ec.parent_id, ec.created_at, ec.updated_at, u.id, vp.id),
     COUNTED AS (SELECT COUNT(*) AS total
                 FROM FILTERED)
SELECT f.*, c.total
FROM FILTERED f,
     COUNTED c
ORDER BY CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.id END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'id' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.id END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.created_at END,
         CASE
             WHEN sqlc.arg('sort')::TEXT = 'created_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.created_at END DESC,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'ASC' THEN f.updated_at END,
         CASE WHEN sqlc.arg('sort')::TEXT = 'updated_at' AND sqlc.arg('order')::TEXT = 'DESC' THEN f.updated_at END DESC
LIMIT sqlc.arg('limit') OFFSET sqlc.arg('offset');

