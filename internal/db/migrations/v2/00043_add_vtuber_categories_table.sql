-- +goose Up
-- +goose StatementBegin
CREATE TABLE vtuber_categories (
id BIGSERIAL PRIMARY KEY,
vtuber_profile_id BIGINT NOT NULL REFERENCES vtuber_profiles (id) ON DELETE CASCADE,
category_id BIGINT NOT NULL REFERENCES categories (id) ON DELETE CASCADE,
created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS vtuber_categories;
-- +goose StatementEnd