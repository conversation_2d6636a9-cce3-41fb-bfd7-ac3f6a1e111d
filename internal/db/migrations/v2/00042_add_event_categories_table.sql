-- +goose Up
-- +goose StatementBegin
CREATE TABLE event_categories (
id BIGSERIAL PRIMARY KEY,
event_id BIGINT NOT NULL REFERENCES events (id) ON DELETE CASCADE,
category_id BIGINT NOT NULL REFERENCES categories (id) ON DELETE CASCADE,
created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS event_categories;
-- +goose StatementEnd