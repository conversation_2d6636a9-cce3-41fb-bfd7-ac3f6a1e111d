-- +goose Up
-- +goose StatementBegin
CREATE TABLE user_delivery_address (
 id BIGSERIAL PRIMARY KEY,
 user_id BIGINT NOT NULL REFERENCES users (id) ON DELETE CASCADE,
 recipient VARCHAR(255) NOT NULL,
 phone_number <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
 postal_code <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
 prefecture VARCHAR(255) NOT NULL,
 city VARCHAR(255) NOT NULL,
 address_line1 VARCHAR(255) NOT NULL,
 address_line2 VARCHAR(255),
 preferred_delivery_time varchar(255),
 preferred_delivery_date DATE,
 created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
 updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
  constraint  user_delivery_address_user_id_unique unique (user_id)
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS user_delivery_address;
-- +goose StatementEnd