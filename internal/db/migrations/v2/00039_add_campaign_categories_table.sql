-- +goose Up
-- +goose StatementBegin
CREATE TABLE campaign_categories (
id BIGSERIAL PRIMARY KEY,
campaign_id BIGINT NOT NULL REFERENCES campaigns (id) ON DELETE CASCADE,
category_id BIGINT NOT NULL REFERENCES categories (id) ON DELETE CASCADE,
created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL,
updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP NOT NULL
);
-- +goose StatementEnd

-- +goose Down
-- +goose StatementBegin
DROP TABLE IF EXISTS campaign_categories;
-- +goose StatementEnd