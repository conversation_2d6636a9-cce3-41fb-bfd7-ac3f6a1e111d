package mail

import (
	"context"
	"github.com/nsp-inc/vtuber/packages/web"
	"log"
	"strings"
)

func SendCampaignPurchaseCompleteEmail(ctx context.Context, email string, title string, slug string, applicationDate string, planAmount string, systemFee string, total string, variantName string, variantDetails string) {
	var content strings.Builder
	var subject string

	var campaignUrl = "https://v-sai.com/campaign/" + slug

	lan := web.GetLanguage(ctx)
	switch lan {
	case "en-us":
		subject = "[V Festival] Thank you for your support (Notice of payment completion)"

		content.WriteString("<p>Thank you for using V Festival.</p>")
		content.WriteString("<p>Your support for the campaign has been completed as follows.</p>")
		content.WriteString("<p>---------------------------------</p>")
		content.WriteString("<p>■Campaign</p>")
		content.WriteString("<p>Title: " + title + "</p>")
		content.WriteString("<p>URL: " + campaignUrl + "</p>")
		content.WriteString("<p>■Application details</p>")
		content.WriteString("<p>Application date: " + applicationDate + "</p>")
		content.WriteString("<p>Payment method:  Credit Card</p>")
		content.WriteString("<p>Plan amount: " + planAmount + "</p>")
		//content.WriteString("<p>System usage fee: " + systemFee + "</p>")
		content.WriteString("<p>Total:  " + total + "</p>")
		content.WriteString("<p>■Return details (delivered when campaign is completed)</p>")
		content.WriteString("<p>Plan name:  " + variantName + "</p>")
		content.WriteString("<p>Return details:  " + variantDetails + "</p>")
		content.WriteString("<p>---------------------------------</p>")
		content.WriteString("<p>If you have any questions about this matter, please contact us using the contact form on the site.</p>")
		content.WriteString("<p>URL: " + websiteUrl + "</p>")
		content.WriteString("<p>Contact: V Festival Management Staff</p>")
		content.WriteString("<p>©2025 株式会社デジタルギア</p>")

	case "ja-jp":
		subject = "【V祭】ご支援ありがとうございます（決済完了のご案内）"

		content.WriteString("<p>V祭をご利用いただき、ありがとうございます。</p>")
		content.WriteString("<p>以下の内容にて、キャンペーンへのご支援が完了いたしました。</p>")
		content.WriteString("<p>---------------------------------</p>")
		content.WriteString("<p>キャンペーン</p>")
		content.WriteString("<p>タイトル: " + title + "</p>")
		content.WriteString("<p>URL: " + campaignUrl + "</p>")
		content.WriteString("<p>■お申込み内容</p>")
		content.WriteString("<p>申込日: " + applicationDate + "</p>")
		content.WriteString("<p>決済方法: クレジットカード</p>")
		content.WriteString("<p>プラン金額: " + planAmount + "</p>")
		//content.WriteString("<p>システム利用料: " + systemFee + "</p>")
		content.WriteString("<p>合計:  " + total + "</p>")
		content.WriteString("<p>■リターン内容（キャンペーン達成時にお届け）</p>")
		content.WriteString("<p>プラン名:  " + variantName + "</p>")
		content.WriteString("<p>リターン詳細:  " + variantDetails + "</p>")
		content.WriteString("<p>---------------------------------</p>")
		content.WriteString("<p>本件に関するご質問は、サイト内のお問い合わせフォームよりご連絡ください。</p>")
		content.WriteString("<p>URL: " + websiteUrl + "</p>")
		content.WriteString("<p>担当者: V祭運営スタッフ</p>")
		content.WriteString("<p>©2025 株式会社デジタルギア</p>")
	}

	mail := Mail{
		To:      []string{email},
		Subject: subject,
		Body:    []byte(content.String()),
		IsHtml:  true,
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending registration email:", err)
	}
}
