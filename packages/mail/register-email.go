package mail

import (
	"context"
	"github.com/nsp-inc/vtuber/packages/web"
	"log"
	"strings"
)

const myPageUrl = "https://v-sai.com/my-page"
const forgotPasswordUrl = "https://v-sai.com/forgot-password"
const websiteUrl = "https://v-sai.com"

func SendRegistrationEmail(ctx context.Context, email string, token string) {
	var content strings.Builder
	var subject string

	lan := web.GetLanguage(ctx)
	switch lan {
	case "en-us":
		subject = "V Festival Membership Registration"

		content.WriteString("Thank you for using V Festival.\n")
		content.WriteString("Please use the code below to continue your membership registration.\n")
		content.WriteString(token + "\n")
		content.WriteString("If you have any questions about this matter, please contact us using the contact form on the site.\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 株式会社デジタルギア")
	case "ja-jp":
		subject = "【V祭】メールアドレス認証のご案内"
		content.WriteString("V Festivalをご利用いただきありがとうございます。\n")
		content.WriteString("会員登録を続行するには、以下のコードをご利用ください。\n")
		content.WriteString(token + "\n")
		content.WriteString("ご不明な点がございましたら、サイトの問い合わせフォームよりお問い合わせください。\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 株式会社デジタルギア")
	}

	mail := Mail{
		To:      []string{email},
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending registration email:", err)
	}
}

func SendMemberRegisterMail(ctx context.Context, email *string, name string) {
	if email == nil {
		return
	}
	var content strings.Builder
	var subject string
	lan := web.GetLanguage(ctx)

	switch lan {
	case "en-us":
		subject = "[V Festival] Notice of membership registration completion"

		content.WriteString("Thank you for registering with V Festival.\n")
		content.WriteString("The following is the information you have registered. Please confirm.\n")
		content.WriteString("---------------------------------\n")
		content.WriteString("Name: " + name + "\n")
		content.WriteString("Email address: " + *email + "\n")
		content.WriteString("---------------------------------\n")
		content.WriteString("*Details of your registration information can be found at " + myPageUrl + " (login required).\n")
		content.WriteString("*If you have forgotten your password, you can reissue it at " + forgotPasswordUrl + ".\n")
		content.WriteString("If you have any questions about this matter, please contact us using the contact form on the site.\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 株式会社デジタルギア")
	case "ja-jp":
		subject = "【V祭】会員登録完了のお知らせ"
		content.WriteString("この度はV祭へご登録いただき、誠にありがとうございます。\n")
		content.WriteString("以下はご登録いただいた内容です。ご確認ください。\n")
		content.WriteString("---------------------------------\n")
		content.WriteString("名前: " + name + "\n")
		content.WriteString("メールアドレス: " + *email + "\n")
		content.WriteString("---------------------------------\n")
		content.WriteString("※登録情報の詳細は、" + myPageUrl + " からご確認いただけます（ログインが必要です）")
		content.WriteString("※パスワードをお忘れの際は " + forgotPasswordUrl + " より再発行いただけます。")
		content.WriteString("本件に関するご質問は、サイト内のお問い合わせフォームよりご連絡ください。")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 株式会社デジタルギア")
	}

	mail := Mail{
		To:      []string{*email},
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending registration email:", err)
	}
}
