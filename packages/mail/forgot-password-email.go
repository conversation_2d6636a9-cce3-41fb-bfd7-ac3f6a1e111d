package mail

import (
	"context"
	"github.com/nsp-inc/vtuber/packages/web"
	"log"
	"strings"
)

func ForgotPasswordEmail(ctx context.Context, email string, token string) {
	var content strings.Builder
	var subject string

	lan := web.GetLanguage(ctx)
	switch lan {
	case "en-us":
		subject = "[V Festival] Password Reset Information"
		content.WriteString("Thank you for using V Festival.")
		content.WriteString("To reset your password, please use the code below.\n")
		content.WriteString(token + "\n")
		content.WriteString("*If the code has expired, please try resetting your password again.\n")
		content.WriteString("If you did not request this, please ignore this email.\n")
		content.WriteString("If you have any questions, please contact us using the contact form on the site.\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 株式会社デジタルギア")
	case "ja-jp":
		subject = "【V祭】パスワード再設定のご案内"
		content.WriteString("V祭をご利用いただき、誠にありがとうございます。\n")
		content.WriteString("パスワードをリセットするには、以下のコードを使用してください。\n")
		content.WriteString(token + "\n")
		content.WriteString("※コードの有効期限が切れている場合は、再度パスワードの再設定をお試しください。\n")
		content.WriteString("これをリクエストしていない場合は、このメールを無視してください。\n")
		content.WriteString("ご不明な点がございましたら、サイトの問い合わせフォームよりお問い合わせください。\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 株式会社デジタルギア")
	}

	mail := Mail{
		To:      []string{email},
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending forgot password email:", err)
	}

}
