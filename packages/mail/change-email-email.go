package mail

import (
	"context"
	"github.com/nsp-inc/vtuber/packages/web"
	"log"
	"strings"
)

func SendChangeEmailSend(ctx context.Context, email string, token string) {
	var content strings.Builder
	var subject string

	lan := web.GetLanguage(ctx)
	switch lan {
	case "en-us":
		subject = "[V Festival] Notice of email address change"

		content.WriteString("Use the code below to continue changing your email address.\n")
		content.WriteString(token + "\n")
		content.WriteString("If you have any questions about this matter, please contact us using the contact form on the site.\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("Contact: V Festival Management Staff\n")
		content.WriteString("©2025 株式会社デジタルギア")
	case "ja-jp":
		subject = "【V祭】メールアドレス変更のご案内"
		content.WriteString("メールアドレスの変更を続行するには、以下のコードを使用してください。\n")
		content.WriteString(token + "\n")
		content.WriteString("ご不明な点がございましたら、サイトの問い合わせフォームよりお問い合わせください。\n")
		content.WriteString("URL: " + websiteUrl + "\n")
		content.WriteString("担当者: V祭運営スタッフ\n")
		content.WriteString("©2025 株式会社デジタルギア")
	}

	mail := Mail{
		To:      []string{email},
		Subject: subject,
		Body:    []byte(content.String()),
	}

	err := SendMail(mail)
	if err != nil {
		log.Println("Error sending registration email:", err)
	}
}
