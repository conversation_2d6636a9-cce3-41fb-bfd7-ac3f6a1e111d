syntax = "proto3";

package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message AddVtuberBannerRequest {
  string image = 1; // @gotag: validate:"required"
}

message VtuberBanner {
  int64 id = 1;
  int64 vtuber_id = 2;
  string image = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
}

message AddVtuberBannerResponse {
  VtuberBanner data = 1;
}

message DeleteVtuberBannerByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateVtuberBannerByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
  string image = 2; // @gotag: validate:"required"
}

message GetVtuberBannerByVtuberIdRequest {
  string vtuber_id = 1; // @gotag: validate:"required"
}

message GetVtuberBannerByVtuberIdResponse {
  repeated VtuberBanner data = 1;
}

message DeleteVtuberBannerByIdResponse {
  bool success = 1;
  string message = 2;
}

message UpdateVtuberBannerByIdResponse {
  bool success = 1;
  string message = 2;
}

service VtuberBannerService {
  rpc AddVtuberBanner(AddVtuberBannerRequest) returns (AddVtuberBannerResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc GetVtuberBannerByVtuberId(GetVtuberBannerByVtuberIdRequest) returns (GetVtuberBannerByVtuberIdResponse);
  rpc DeleteVtuberBannerById(DeleteVtuberBannerByIdRequest) returns (DeleteVtuberBannerByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc UpdateVtuberBannerById(UpdateVtuberBannerByIdRequest) returns (UpdateVtuberBannerByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
}
