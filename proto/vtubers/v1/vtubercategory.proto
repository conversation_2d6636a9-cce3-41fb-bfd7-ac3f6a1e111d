syntax = "proto3";

package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message VtuberCategory {
  int64 id = 1;
}

message GetAllVtuberCategoriesRequest {}

message GetVtuberCategoriesResponse {
  repeated VtuberCategory data = 1;
}

service VtuberCategoryService {
  rpc GetAllVtuberCategories(GetAllVtuberCategoriesRequest) returns (GetVtuberCategoriesResponse);
}
