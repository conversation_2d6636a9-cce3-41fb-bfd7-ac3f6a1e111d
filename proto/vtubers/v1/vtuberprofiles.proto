syntax = "proto3";
package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message AddVtuberProfileRequest {
  string display_name = 1; // @gotag: validate:"required"
  string furigana = 2; // @gotag: validate:"required"
  string image = 3;
  string banner_image = 4;
  optional string description = 5;
  api.shared.v1.SocialMediaLinks social_media_links = 6;
  string username = 7; // @gotag: validate:"required"
  repeated int64 categories = 8;
}

message AddVtuberProfileResponse {
  VtuberProfile data = 1;
}

message VtuberProfile {
  int64 id = 1;
  int64 user_id = 2;
  string display_name = 3;
  string furigana = 4;
  optional string image = 5;
  optional string banner_image = 6;
  api.shared.v1.SocialMediaLinks social_media_links = 7;
  optional string description = 8;
  google.protobuf.Timestamp created_at = 9;
  bool is_user_subscribed = 10;
  bool has_liked = 11;
  string username = 12;
  repeated int64 categories = 13;
  bool is_plan_active = 14;
}

message GetVtuberProfileByIdRequest {
  string id = 1; // @gotag: validate:"required"
}

message GetVtuberProfileRequest {}

message GetVtuberProfileByIdResponse {
  VtuberProfile data = 1;
}

message UpdateVtuberProfileRequest {
  int64 id = 1; // @gotag: validate:"required"
  string display_name = 2; // @gotag: validate:"required"
  string furigana = 3; // @gotag: validate:"required"
  string image = 4; // @gotag: validate:"required"
  string banner_image = 5; // @gotag: validate:"required"
  optional string description = 6;
  api.shared.v1.SocialMediaLinks social_media_links = 7;
  repeated int64 categories = 8;
  bool is_plan_active = 9;
}

message GetAllVtuberProfilesRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  optional string display_name = 2;
  optional int64 category_id = 3;
}

message GetAllVtuberProfilesResponse {
  repeated VtuberProfile data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message DeleteVtuberProfileByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message VerifyVtuberProfileRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message CreateVtuberProfileAccessRequest {
  string description = 1; // @gotag: validate:"required"
}

message UpdateVtuberProfileAccessRequest {
  string description = 1; // @gotag: validate:"required"
}

message UpdateVtuberProfileAccessResponse {
  bool success = 1;
  string message = 2;
}

message GetAllVtuberProfileAccessRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  optional string status = 2;
}

message GetAllVtuberProfileAccessResponse {
  repeated VtuberAccessRequest data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message VtuberAccessRequest {
  int64 id = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
  string status = 3; // @gotag: validate:"required"
  optional string reason = 4; // @gotag: validate:"required"
  google.protobuf.Timestamp created_at = 5;
  int64 user_id = 6; // @gotag: validate:"required"
  string name = 7; // @gotag: validate:"required"
}

message DenyVtuberProfileRequest {
  int64 id = 1; // @gotag: validate:"required"
  string reason = 2; // @gotag: validate:"required"
}

message UpdateVtuberProfileResponse {
  bool success = 1;
  string message = 2;
}

message VerifyVtuberProfileResponse {
  bool success = 1;
  string message = 2;
}

message CreateVtuberProfileAccessResponse {
  bool success = 1;
  string message = 2;
}

message DenyVtuberProfileResponse {
  bool success = 1;
  string message = 2;
}

service VtuberProfilesService {
  rpc AddVtuberProfile(AddVtuberProfileRequest) returns (AddVtuberProfileResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc GetVtuberProfileById(GetVtuberProfileByIdRequest) returns (GetVtuberProfileByIdResponse);
  rpc GetVtuberProfile(GetVtuberProfileRequest) returns (GetVtuberProfileByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc UpdateVtuberProfile(UpdateVtuberProfileRequest) returns (UpdateVtuberProfileResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "_user.vtuberId==id || _user.role=='admin'"
    };
  }
  rpc GetAllVtuberProfiles(GetAllVtuberProfilesRequest) returns (GetAllVtuberProfilesResponse) {}
  // rpc DeleteVtuberProfileById(DeleteVtuberProfileByIdRequest) returns (api.shared.v1.GenericResponse) {
  //   option (api.authz.v1.options) = {
  //     require: true
  //   };
  // }
  rpc VerifyVtuberProfile(VerifyVtuberProfileRequest) returns (VerifyVtuberProfileResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc ApplyVtuberAccess(CreateVtuberProfileAccessRequest) returns (CreateVtuberProfileAccessResponse) {
    option (api.authz.v1.options) = {require: true};
  }

  rpc GetMyVtuberAccessRequests(GetAllVtuberProfileAccessRequest) returns (VtuberAccessRequest) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllVtuberProfileAccess(GetAllVtuberProfileAccessRequest) returns (GetAllVtuberProfileAccessResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc DenyVtuberProfile(DenyVtuberProfileRequest) returns (DenyVtuberProfileResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }

  rpc UpdateVtuberAccessRequest(UpdateVtuberProfileAccessRequest) returns (UpdateVtuberProfileAccessResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
