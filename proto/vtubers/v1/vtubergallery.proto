syntax = "proto3";

package api.vtubers.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/vtubers/v1;vtubersv1";

message AddVtuberGalleryRequest {
  string media = 1; // @gotag: validate:"required"
  string media_type = 2; // @gotag: validate:"required,oneof=picture video"
  optional string description = 3;
}

message VtuberGallery {
  int64 id = 1;
  int64 vtuber_id = 2;
  string media = 3;
  string media_type = 4;
  optional string description = 5;
  google.protobuf.Timestamp created_at = 6;
  google.protobuf.Timestamp updated_at = 7;
}

message AddVtuberGalleryResponse {
  VtuberGallery data = 1;
}

message DeleteVtuberGalleryByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetVtuberGalleryByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetVtuberGalleryByIdResponse {
  VtuberGallery data = 1;
}

message GetVtuberGalleriesRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetVtuberGalleriesResponse {
  repeated VtuberGallery data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message UpdateVtuberGalleryByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
  string media = 2; // @gotag: validate:"required"
  string media_type = 3; // @gotag: validate:"required,oneof=picture video"
  optional string description = 4;
}

service VtuberGalleryService {
  rpc AddVtuberGallery(AddVtuberGalleryRequest) returns (AddVtuberGalleryResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc GetVtuberGalleryById(GetVtuberGalleryByIdRequest) returns (GetVtuberGalleryByIdResponse);
  rpc GetVtuberGalleries(GetVtuberGalleriesRequest) returns (GetVtuberGalleriesResponse);
  rpc DeleteVtuberGalleryById(DeleteVtuberGalleryByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc UpdateVtuberGalleryById(UpdateVtuberGalleryByIdRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
}
