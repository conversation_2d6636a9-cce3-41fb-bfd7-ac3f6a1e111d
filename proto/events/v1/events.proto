syntax = "proto3";

package api.events.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/events/v1;eventsv1";

message AddEventRequest {
  string title = 1; // @gotag: validate:"required"
  string description = 2;
  string image = 3; // @gotag: validate:"required"
  string rules = 5; // @gotag: validate:"required"
  google.protobuf.Timestamp start_date = 6;
  google.protobuf.Timestamp end_date = 7;
  repeated int64 categories = 8;
  optional bool as_vtuber = 9;
  string short_description = 10; // @gotag: validate:"required"
  string participation_flow = 11; // @gotag: validate:"required"
  string benefits = 12; // @gotag: validate:"required"
  string requirements = 13; // @gotag: validate:"required"
  string overview = 14; // @gotag: validate:"required"
  api.shared.v1.SocialMediaLinks social_media_links = 15;
}

message AddEventResponse {
  Event data = 1;
}

message Event {
  int64 id = 1;
  string title = 2;
  string description = 3;
  string image = 4;
  string rules = 5;
  google.protobuf.Timestamp start_date = 6;
  google.protobuf.Timestamp end_date = 7;
  repeated int64 categories = 8;
  google.protobuf.Timestamp created_at = 9;
  optional api.shared.v1.Profile user = 10;
  optional api.shared.v1.Profile vtuber = 11;
  string status = 12;
  bool has_participated = 13;
  string short_description = 14;
  string participation_flow = 15;
  string benefits = 16;
  string requirements = 17;
  string overview = 18;
  api.shared.v1.SocialMediaLinks social_media_links = 19;
  string slug = 20;
}

message GetAllEventsRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
  optional int64 vtuber_id = 2;
  optional int64 category_id = 3;
}

message GetAllEventsResponse {
  repeated Event data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetAllEventsByCategoryRequest {
  int64 category_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetEventByIdRequest {
  string id = 1; // @gotag: validate:"required"
}

message GetEventByIdResponse {
  Event data = 1;
}

message DeleteEventByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateEventByIdRequest {
  string title = 1; // @gotag: validate:"required"
  string description = 2;
  string image = 3;
  string rules = 4; // @gotag: validate:"required"
  google.protobuf.Timestamp start_date = 5;
  google.protobuf.Timestamp end_date = 6;
  repeated int64 categories = 7;
  int64 id = 8; // @gotag: validate:"required"
  string short_description = 9; // @gotag: validate:"required"
  string participation_flow = 10; // @gotag: validate:"required"
  string benefits = 11; // @gotag: validate:"required"
  string requirements = 12; // @gotag: validate:"required"
  string overview = 13; // @gotag: validate:"required"
  api.shared.v1.SocialMediaLinks social_media_links = 14;
}

message ApproveOrRejectEventRequest {
  int64 id = 1; // @gotag: validate:"required"
  string status = 2; // @gotag: validate:"omitempty,oneof=approved rejected"
}

message DeleteEventByIdResponse {
  bool success = 1;
  string message = 2;
}

message UpdateEventByIdResponse {
  bool success = 1;
  string message = 2;
}

message ApproveOrRejectEventResponse {
  bool success = 1;
  string message = 2;
}

service EventService {
  rpc AddEvent(AddEventRequest) returns (AddEventResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllEvents(GetAllEventsRequest) returns (GetAllEventsResponse);
  rpc GetEventById(GetEventByIdRequest) returns (GetEventByIdResponse);
  rpc DeleteEventById(DeleteEventByIdRequest) returns (DeleteEventByIdResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc UpdateEventById(UpdateEventByIdRequest) returns (UpdateEventByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllEventsByCategory(GetAllEventsByCategoryRequest) returns (GetAllEventsResponse);
  rpc ApproveOrRejectEvent(ApproveOrRejectEventRequest) returns (ApproveOrRejectEventResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetMyEvents(GetAllEventsRequest) returns (GetAllEventsResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetUserEvents(GetAllEventsRequest) returns (GetAllEventsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
