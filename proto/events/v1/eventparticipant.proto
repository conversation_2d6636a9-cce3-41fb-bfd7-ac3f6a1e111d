syntax = "proto3";

package api.events.v1;

import "authz/v1/authz.proto";
import "events/v1/events.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/events/v1;eventsv1";

message EventParticipation {
  int64 id = 1;
  int64 event_id = 2;
  int64 vtuber_id = 3;
  string status = 4; // @gotag: validate:"required,oneof=approved rejected"
  optional api.shared.v1.Profile vtuber = 5;
  optional Event event = 6;
  int64 vote_count = 7;
  optional string remarks = 8;
}

message AddEventParticipationRequest {
  int64 event_id = 1; // @gotag: validate:"required"
}

message GetCreatorEventParticipationRequest {}

message GetCreatorEventParticipationResponse {
  repeated int64 events = 1;
}

message GetEventParticipationOfVtuberRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetEventParticipantsByEventIdRequest {
  string event_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}
message GetEventParticipantsByEventIdResponse {
  repeated EventParticipation data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetMyEventParticipationRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}
message GetMyEventParticipationResponse {
  repeated EventParticipation data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message ChangeStatusRequest {
  int64 event_participation_id = 1; // @gotag: validate:"required"
  string status = 2; // @gotag: validate:"required,oneof=approved rejected"
  optional string reason = 3;
}

message GetAllEventParticipationRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}

message GetAllEventParticipationResponse {
  repeated EventParticipation data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetTopTenEventParticipantsVtuberRequest {
  int64 event_id = 1;
}

message GetTopTenEventParticipantsVtuberResponse {
  repeated EventParticipantVtuber data = 1;
}

message EventParticipantVtuber {
  string name = 1;
  optional string image = 2;
  int32 vote_count = 3;
  int64 id = 4;
}

message AddEventParticipationResponse {
  bool success = 1;
  string message = 2;
}

message ChangeStatusResponse {
  bool success = 1;
  string message = 2;
}

service EventParticipantService {
  rpc AddEventParticipation(AddEventParticipationRequest) returns (AddEventParticipationResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc GetEventParticipantsByEventId(GetEventParticipantsByEventIdRequest) returns (GetEventParticipantsByEventIdResponse);
  rpc GetMyEventParticipation(GetMyEventParticipationRequest) returns (GetMyEventParticipationResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc ChangeStatus(ChangeStatusRequest) returns (ChangeStatusResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllEventParticipation(GetAllEventParticipationRequest) returns (GetAllEventParticipationResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }

  rpc GetCreatorEventParticipation(GetCreatorEventParticipationRequest) returns (GetCreatorEventParticipationResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }

  rpc GetAllEventParticipantsByEventId(GetEventParticipantsByEventIdRequest) returns (GetEventParticipantsByEventIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }

  rpc GetAllEventParticipationOfVtuber(GetEventParticipationOfVtuberRequest) returns (GetAllEventParticipationResponse) {}

  rpc GetTopTenEventParticipantsVtubers(GetTopTenEventParticipantsVtuberRequest) returns (GetTopTenEventParticipantsVtuberResponse) {}
}
