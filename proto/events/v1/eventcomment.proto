syntax = "proto3";

package api.events.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/events/v1;eventsv1";

message AddEventCommentRequest {
  string content = 1; // @gotag: validate:"required"
  int64 event_id = 2; // @gotag: validate:"required"
  optional bool as_vtuber = 3;
  optional int64 parent_id = 4;
}

message AddEventCommentResponse {
  EventComment data = 1;
}

message GetAllEventCommentsRequest {
  int64 event_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetAllEventCommentsResponse {
  repeated EventComment data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message EventComment {
  int64 id = 1;
  string content = 2;
  int64 event_id = 3;
  optional int64 parent_id = 5;
  google.protobuf.Timestamp created_at = 6;
  api.shared.v1.Profile user = 7;
  optional api.shared.v1.Profile vtuber = 8;
  bool has_reply = 9;
}

message GetEventCommentByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetEventCommentByIdResponse {
  EventComment data = 1;
}

message DeleteEventCommentByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateEventCommentByIdRequest {
  string content = 1; // @gotag: validate:"required"
  int64 id = 2; // @gotag: validate:"required"
}

message UpdateEventCommentByIdResponse {
  bool success = 1;
  string message = 2;
}

message DeleteEventCommentByIdResponse {
  bool success = 1;
  string message = 2;
}

service EventCommentService {
  rpc AddEventComment(AddEventCommentRequest) returns (AddEventCommentResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "as_vtuber==true?_user.vtuberId!=null:true"
    };
  }
  rpc GetAllEventComments(GetAllEventCommentsRequest) returns (GetAllEventCommentsResponse) {}
  rpc GetAllRepliesOfEventComment(GetEventCommentByIdRequest) returns (GetAllEventCommentsResponse) {}
  rpc GetEventCommentById(GetEventCommentByIdRequest) returns (GetEventCommentByIdResponse) {}
  rpc DeleteEventCommentById(DeleteEventCommentByIdRequest) returns (DeleteEventCommentByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateEventCommentById(UpdateEventCommentByIdRequest) returns (UpdateEventCommentByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
