syntax = "proto3";

package api.events.v1;

import "authz/v1/authz.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/events/v1;eventsv1";

message AddVoteRequest {
  int64 event_participation_id = 1; // @gotag: validate:"required"
  bool from_daily_point = 2;
  int32 points = 3;
}

message GetDailyPointAvailableRequest {
  int64 event_id = 1; // @gotag: validate:"required"
}

message GetDailyPointAvailableResponse {
  bool available = 1;
}

message GivePlatformPointRequest {
  int64 event_participation_id = 1; // @gotag: validate:"required"
  string remarks = 2; // @gotag: validate:"required"
  int64 points = 3; // @gotag: validate:"required"
}

message GetPlatfromPoint {
  int64 event_participation_id = 1; // @gotag: validate:"required"
}

message AddVoteResponse {
  bool success = 1;
  string message = 2;
}

message GivePlatformPointResponse {
  bool success = 1;
  string message = 2;
}

service EventVoteService {
  rpc AddVote(AddVoteRequest) returns (AddVoteResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetDailyPointAvailable(GetDailyPointAvailableRequest) returns (GetDailyPointAvailableResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GivePlatformPoint(GivePlatformPointRequest) returns (GivePlatformPointResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetPlatfromPointOfEventParticipation(GetPlatfromPoint) returns (GivePlatformPointRequest) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
}
