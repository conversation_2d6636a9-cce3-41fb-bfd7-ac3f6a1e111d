syntax = "proto3";

package api.camapigns.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/profile.proto";

option go_package = "github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1";

message AddCampaignVariantSubscriptionRequest {
  int64 campaign_variant_id = 1; // @gotag: validate:"required"
  int64 user_billing_info_id = 2; // @gotag: validate:"required"
  optional string comment = 3;
}

message AddCampaignVariantSubscriptionResponse {
  CampaignVariantSubscription data = 1;
}

message CampaignVariantSubscription {
  int64 user_id = 1;
  int64 campaign_variant_id = 2;
  optional api.shared.v1.Profile profile = 3;
  int32 price = 4;
  google.protobuf.Timestamp created_at = 5;
  int64 vtuber_id = 6;
  optional string comment = 7;
}

service CampaignVariantSubscriptionService {
  rpc AddCampaignVariantSubscription(AddCampaignVariantSubscriptionRequest) returns (AddCampaignVariantSubscriptionResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
