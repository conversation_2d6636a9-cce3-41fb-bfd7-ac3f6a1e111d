syntax = "proto3";

package api.campaigns.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/nsp-inc/vtuber/api/campaigns/v1;campaignsv1";

message AddCampaignBannerRequest {
  string image = 1; // @gotag: validate:"required"
  int64 campaign_id = 2; // @gotag: validate:"required"
  int32 index = 3; // @gotag: validate:"required"
}

message AddCampaignBannerResponse {
  CampaignBanner data = 1;
}

message CampaignBanner {
  int64 id = 1;
  string image = 2;
  int32 index = 3;
  int64 campaign_id = 4;
  google.protobuf.Timestamp created_at = 6;
}

message GetCampaignBannerByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetCampaignBannerByIdResponse {
  CampaignBanner data = 1;
}

message DeleteCampaignBannerByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateCampaignBannerByIdRequest {
  string image = 1;
  int64 id = 2; // @gotag: validate:"required"
  int32 index = 3; // @gotag: validate:"required"
}

message GetBannerByCampaignIdRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
}

message GetBannerByCampaignIdResponse {
  repeated CampaignBanner data = 1;
}

message UpdateCampaignBannerResponse {
  bool success = 1;
  string message = 2;
}

message DeleteCampaignBannerResponse {
  bool success = 1;
  string message = 2;
}

service CampaignBannerService {
  rpc AddCampaignBanner(AddCampaignBannerRequest) returns (AddCampaignBannerResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
    };
  }
  rpc GetCampaignBannerById(GetCampaignBannerByIdRequest) returns (GetCampaignBannerByIdResponse);
  rpc DeleteCampaignBannerById(DeleteCampaignBannerByIdRequest) returns (DeleteCampaignBannerResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateCampaignBannerById(UpdateCampaignBannerByIdRequest) returns (UpdateCampaignBannerResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetBannerByCampaignId(GetBannerByCampaignIdRequest) returns (GetBannerByCampaignIdResponse);
}
