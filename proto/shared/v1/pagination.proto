syntax = "proto3";
package api.shared.v1;

option go_package = "github.com/nsp-inc/vtuber/api/shared/v1;sharedv1";

message PaginationRequest {
  optional int32 size = 1;
  optional int32 page = 2;
  optional string sort = 3;
  optional string order = 4;
}

message PaginationDetails {
  int32 total_items = 1;
  int32 total_pages = 2;
  int32 current_page = 3;
  int32 page_size = 4;
  int32 next_page = 5;
  int32 prev_page = 6;
  repeated PageLinks links = 7;
}

message PageLinks {
  optional bool is_dot = 1;
  optional int32 page = 2;
  optional bool is_next = 3;
  optional bool is_active = 4;
  optional bool is_previous = 5;
}
