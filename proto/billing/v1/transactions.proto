syntax = "proto3";

package billing.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";

option go_package = "github.com/nsp-inc/vtuber/api/billing/v1;billingv1";

message AddTransactionRequest {
  int32 amount = 1; // @gotag: validate:"required"
  optional int64 camp_variant_id = 2;
  optional int64 creator_subscription_id = 3;
  string status = 4; // @gotag: validate:"required"
  bool is_recurring = 5;
  google.protobuf.Timestamp expires_on = 8;
}

message AddTransactionResponse {
  Transaction data = 2;
}

message Transaction {
  int64 id = 1;
  int64 user_id = 2;
  int32 amount = 3;
  string type = 4;
  optional int64 camp_variant_id = 5;
  optional int64 creator_subscription_id = 6;
  string status = 7;
  google.protobuf.Timestamp created_at = 8;
}

message GetAllTransactionsRequest {
  optional int64 creator_subscription_id = 1;
  optional int64 user_id = 2;
  optional int64 camp_variant_id = 3;
  api.shared.v1.PaginationRequest pagination = 4;
}

message GetAllTransactionsResponse {
  repeated Transaction data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message GetAllTransactionsOfUserRequest {
  optional int64 creator_subscription_id = 1;
  optional int64 camp_variant_id = 3;
  api.shared.v1.PaginationRequest pagination = 4;
}

message GetTransactionByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetTransactionByIdResponse {
  Transaction data = 2;
}

service TransactionService {
  rpc AddTransaction(AddTransactionRequest) returns (AddTransactionResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllTransactions(GetAllTransactionsRequest) returns (GetAllTransactionsResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllTransactionsOfUser(GetAllTransactionsOfUserRequest) returns (GetAllTransactionsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetTransactionById(GetTransactionByIdRequest) returns (GetTransactionByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
