syntax = "proto3";

package api.billing.v1;

import "authz/v1/authz.proto";

option go_package = "github.com/nsp-inc/vtuber/api/billing/v1;billingv1";

message AddBillingInfoRequest {
  string full_name = 1; // @gotag: validate:"required"
  string address_1 = 2; // @gotag: validate:"required"
  optional string address_2 = 3;
  string city = 4; // @gotag: validate:"required"
  string state = 5; // @gotag: validate:"required"
  string country = 6; // @gotag: validate:"required"
  string postal_code = 7; // @gotag: validate:"required"
  optional string company_name = 8;
  optional string vat_number = 9;
  string card_no = 10; // @gotag: validate:"required"
  string card_expiry = 11; // @gotag: validate:"required"
  string card_cvc = 12; // @gotag: validate:"required"
}

message AddBillingInfoResponse {
  BillingInfo data = 1;
}

message BillingInfo {
  int64 id = 1;
  string full_name = 2;
  string address_1 = 3;
  optional string address_2 = 4;
  string city = 5;
  optional string state = 6;
  string country = 7;
  string postal_code = 8;
  optional string company_name = 9;
  optional string vat_number = 10;
  string card_no = 11;
  string card_expiry = 12;
}

message GetBillingInfoRequest {}
message GetBillingInfoResponse {
  repeated BillingInfo data = 1;
}

message UpdateBillingInfoRequest {
  int64 id = 1; // @gotag: validate:"required"
  string full_name = 2; // @gotag: validate:"required"
  string address_1 = 3; // @gotag: validate:"required"
  optional string address_2 = 4;
  string city = 5; // @gotag: validate:"required"
  string state = 6; // @gotag: validate:"required"
  string country = 7; // @gotag: validate:"required"
  string postal_code = 8; // @gotag: validate:"required"
  optional string company_name = 9;
  optional string vat_number = 10;
}

message UpdateBillingInfoResponse {
  bool success = 1;
  string message = 2;
}

message DeleteBillingInfoResponse {
  bool success = 1;
  string message = 2;
}

message DeleteBillingInfoRequest {
  int64 id = 1; // @gotag: validate:"required"
}

service BillInfoService {
  rpc AddBillingInfo(AddBillingInfoRequest) returns (AddBillingInfoResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetBillingInfo(GetBillingInfoRequest) returns (GetBillingInfoResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateBillingInfo(UpdateBillingInfoRequest) returns (UpdateBillingInfoResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeleteBillingInfo(DeleteBillingInfoRequest) returns (DeleteBillingInfoResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
