syntax = "proto3";
package api.notifications.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/notifications/v1;notificationsv1";

message GetNotificationsRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}

message GetUserNotificationsResponse {
  repeated Notification data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message Notification {
  int64 id = 1;
  string title = 2;
  string description = 3;
  optional string json = 4;
  google.protobuf.Timestamp created_at = 5;
  optional string deep_link = 6;
  string severity = 7;
  string titleJp = 8;
  string descriptionJp = 9;
  bool is_read = 10;
}

message MarkNotificationAsReadRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message NotificationCountResponse {
  int64 count = 1;
}

message NotificationCountRequest {}

message MarkNotificationAsReadResponse {
  bool success = 1;
  string message = 2;
}

message MarkNotificationAsUnReadRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message MarkNotificationAsUnReadResponse {
  bool success = 1;
  string message = 2;
}

message DeleteNotificationByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message DeleteNotificationByIdResponse {
  bool success = 1;
  string message = 2;
}

service NotificationsService {
  rpc GetUserNotifications(GetNotificationsRequest) returns (GetUserNotificationsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetCreatorNotifications(GetNotificationsRequest) returns (GetUserNotificationsResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_vtuber: true
      expression: "_user.vtuberId!=null"
    };
  }
  rpc MarkNotificationAsRead(MarkNotificationAsReadRequest) returns (MarkNotificationAsReadResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc MarkNotificationAsUnRead(MarkNotificationAsUnReadRequest) returns (MarkNotificationAsUnReadResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeleteNotificationById(DeleteNotificationByIdRequest) returns (DeleteNotificationByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetNotificationCount(NotificationCountRequest) returns (NotificationCountResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
