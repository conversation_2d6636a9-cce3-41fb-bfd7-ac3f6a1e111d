syntax = "proto3";

package api.cms.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/cms/v1;cmsv1";

message AddFaqRequest {
  string question = 1; // @gotag: validate:"required"
  string response = 2; // @gotag: validate:"required"
  int32 index = 3; // @gotag: validate:"gte=0"
  optional bool is_active = 4;
  string language = 5; // @gotag: validate:"omitempty,oneof=en-us ja-jp"
  string tag = 6; // @gotag: validate:"required"
}

message AddFaqResponse {
  Faq data = 1;
}

message Faq {
  int64 id = 1;
  string question = 2;
  string response = 3;
  int32 index = 4;
  google.protobuf.Timestamp created_at = 5;
  bool is_active = 6;
  string language = 7;
  string tag = 8;
}

message GetAllFaqsRequest {
  optional string language = 1;
}

message GetAllFaqsResponse {
  repeated Faq data = 1;
}

message GetFaqRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetFaqResponse {
  Faq data = 1;
}

message UpdateFaqRequest {
  int64 id = 1; // @gotag: validate:"required"
  string question = 2; // @gotag: validate:"required"
  string response = 3; // @gotag: validate:"required"
  int32 index = 4; // @gotag: validate:"gte=0"
  string language = 5; // @gotag: validate:"omitempty,oneof=en-us ja-jp"
  string tag = 6; // @gotag: validate:"required"
}

message DeleteFaqRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message ToogleFaqStatusRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetAllActiveFaqsRequest {
  optional string language = 1;
}

message UpdateFaqResponse {
  bool success = 1;
  string message = 2;
}

message DeleteFaqResponse {
  bool success = 1;
  string message = 2;
}

message ToogleFaqStatusResponse {
  bool success = 1;
  string message = 2;
}

service FaqService {
  rpc AddFaq(AddFaqRequest) returns (AddFaqResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllFaqs(GetAllFaqsRequest) returns (GetAllFaqsResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetFaq(GetFaqRequest) returns (GetFaqResponse);
  rpc UpdateFaq(UpdateFaqRequest) returns (UpdateFaqResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc DeleteFaq(DeleteFaqRequest) returns (DeleteFaqResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc ToogleFaqStatus(ToogleFaqStatusRequest) returns (ToogleFaqStatusResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllActiveFaqs(GetAllActiveFaqsRequest) returns (GetAllFaqsResponse);
}
