syntax = "proto3";

package api.cms.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";

option go_package = "github.com/nsp-inc/vtuber/api/cms/v1;cmsv1";

message AddAnnouncementRequest {
  string image = 1; // @gotag: validate:"required"
  string description = 2; // @gotag: validate:"required"
}

message AnnouncementResponse {
  Announcement data = 1;
}

message GetAnnouncementRequest {}

message Announcement {
  int64 id = 1;
  string image = 2;
  string content = 3;
  bool active = 4;
  google.protobuf.Timestamp created_at = 5;
}

message UpdateAnnouncementRequest {
  int64 id = 1; // @gotag: validate:"required"
  string image = 2; // @gotag: validate:"required"
  string content = 3; // @gotag: validate:"required"
}

message ToggleAnnouncementRequest {
  int64 id = 1; // @gotag: validate:"required"
}

service AnnouncementsService {
  rpc AddAnnouncement(AddAnnouncementRequest) returns (AnnouncementResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }

  rpc UpdateAnnouncement(UpdateAnnouncementRequest) returns (AnnouncementResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }

  rpc ToggleAnnouncement(ToggleAnnouncementRequest) returns (AnnouncementResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }

  rpc GetAnnouncement(GetAnnouncementRequest) returns (AnnouncementResponse) {}
}
