syntax = "proto3";

package api.taxonomy.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/taxonomy/v1;taxonomyv1";

message AddCategoryRequest {
  string name = 1; // @gotag: validate:"required,min=1"
  string description = 2; // @gotag: validate:"required"
  optional string image = 3;
}

message AddCategoryResponse {
  Category data = 1;
}

message GetCategoryRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetCategoryResponse {
  Category data = 1;
}

message UpdateCategoryRequest {
  int64 id = 1; // @gotag: validate:"required"
  string name = 2; // @gotag: validate:"required"
  string description = 3; // @gotag: validate:"required"
  string image = 4;
}

message DeleteCategoryRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message Category {
  int64 id = 1;
  string name = 2;
  string description = 3;
  google.protobuf.Timestamp created_at = 4;
  string slug = 5;
}

message GetEventCategoriesRequest {}

message GetEventCategoriesResponse {
  repeated Category categories = 1;
}
message GetAllCategoriesRequest {}
message GetAllCategoriesResponse {
  repeated Category categories = 1;
}

service CategoryService {
  rpc AddCategory(AddCategoryRequest) returns (AddCategoryResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetAllCategories(GetAllCategoriesRequest) returns (GetAllCategoriesResponse);
  rpc GetCategory(GetCategoryRequest) returns (GetCategoryResponse);
  rpc UpdateCategory(UpdateCategoryRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc DeleteCategory(DeleteCategoryRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc GetEventCategories(GetEventCategoriesRequest) returns (GetEventCategoriesResponse);
}
