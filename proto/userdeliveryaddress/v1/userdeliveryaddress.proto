syntax = "proto3";

package api.userdeliveryaddress.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";

option go_package = "github.com/nsp-inc/vtuber/api/userdeliveryaddress/v1;userdeliveryaddress";

message AddUserDeliveryAddressRequest {
  string recipient = 1; // @gotag: validate:"required"
  string phone_number = 2; // @gotag: validate:"required"
  string postal_code = 3; // @gotag: validate:"required"
  string prefecture = 4; // @gotag: validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"
  string city = 5; // @gotag: validate:"required"
  string address_line1 = 6; // @gotag: validate:"required"
  optional string address_line2 = 7;
  optional string preferred_delivery_time = 8;
  optional google.protobuf.Timestamp preferred_delivery_date = 9;
}

message AddUserDeliveryAddressResponse {
  UserDeliveryAddress data = 1;
}

message UserDeliveryAddress {
  int64 id = 1;
  string recipient = 2;
  string phone_number = 3;
  string postal_code = 4;
  string prefecture = 5;
  string city = 6;
  string address_line1 = 7;
  optional string address_line2 = 8;
  optional string preferred_delivery_time = 9;
  optional google.protobuf.Timestamp preferred_delivery_date = 10;
  google.protobuf.Timestamp created_at = 11;
}

message GetCurrentUsersDeliveryAddressRequest {}
message GetCurrentUsersDeliveryAddressResponse {
  UserDeliveryAddress data = 1;
}

message GetUserDeliveryAddressRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetUserDeliveryAddressResponse {
  UserDeliveryAddress data = 1;
}

message DeleteUserDeliveryAddressRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdateUserDeliveryAddressRequest {
  int64 id = 1; // @gotag: validate:"required"
  string recipient = 2; // @gotag: validate:"required"
  string phone_number = 3; // @gotag: validate:"required"
  string postal_code = 4; // @gotag: validate:"required"
  string prefecture = 5; // @gotag: validate:"required,oneof=Hokkaido/北海道 Aomori/青森県 Iwate/岩手県 Miyagi/宮城県 Akita/秋田県 Yamagata/山形県 Fukushima/福島県 Ibaraki/茨城県 Tochigi/栃木県 Gunma/群馬県 Saitama/埼玉県 Chiba/千葉県 Tokyo/東京都 Kanagawa/神奈川県 Niigata/新潟県 Toyama/富山県 Ishikawa/石川県 Fukui/福井県 Yamanashi/山梨県 Nagano/長野県 Gifu/岐阜県 Shizuoka/静岡県 Aichi/愛知県 Mie/三重県 Shiga/滋賀県 Kyoto/京都府 Osaka/大阪府 Hyogo/兵庫県 Nara/奈良県 Wakayama/和歌山県 Tottori/鳥取県 Shimane/島根県 Okayama/岡山県 Hiroshima/広島県 Yamaguchi/山口県 Tokushima/徳島県 Kagawa/香川県 Ehime/愛媛県 Kochi/高知県 Fukuoka/福岡県 Saga/佐賀県 Nagasaki/長崎県 Kumamoto/熊本県 Oita/大分県 Miyazaki/宮崎県 Kagoshima/鹿児島県 Okinawa/沖縄県"
  string city = 6; // @gotag: validate:"required"
  string address_line1 = 7; // @gotag: validate:"required"
  optional string address_line2 = 8;
  optional string preferred_delivery_time = 9; // @gotag: validate:"oneof=2PM~4PM（午後2時～4時） 4PM~6PM（午後4時～6時）6PM~8PM（午後6時～8時）7PM~9PM（午後7時～
  optional google.protobuf.Timestamp preferred_delivery_date = 10;
}

service UserDeliveryAddressService {
  rpc AddUserDeliveryAddress(AddUserDeliveryAddressRequest) returns (AddUserDeliveryAddressResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetCurrentUserDeliveryAddress(GetCurrentUsersDeliveryAddressRequest) returns (GetCurrentUsersDeliveryAddressResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetUserDeliveryAddressById(GetUserDeliveryAddressRequest) returns (GetUserDeliveryAddressResponse) {
    option (api.authz.v1.options) = {
      require: true
      is_admin: true
    };
  }
  rpc DeleteUserDeliveryAddress(DeleteUserDeliveryAddressRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdateUserDeliveryAddress(UpdateUserDeliveryAddressRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
