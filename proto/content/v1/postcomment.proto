syntax = "proto3";

package api.content.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/content/v1;contentv1";

message AddPostCommentRequest {
  string content = 1; // @gotag: validate:"required"
  int64 post_id = 2; // @gotag: validate:"required"
  optional int64 parent_id = 4;
  optional bool as_vtuber = 5;
}

message AddPostCommentResponse {
  PostComment data = 1;
}

message GetAllPostCommentsRequest {
  string post_id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetAllPostCommentsResponse {
  repeated PostComment data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message PostComment {
  int64 id = 1;
  string content = 2;
  int64 post_id = 3;
  optional int64 parent_id = 5;
  google.protobuf.Timestamp created_at = 6;
  bool has_reply = 7;
  api.shared.v1.Profile user = 8;
  optional api.shared.v1.Profile vtuber = 9;
}

message GetPostCommentByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message GetPostRepliesOfCommentRequest {
  int64 id = 1; // @gotag: validate:"required"
  optional api.shared.v1.PaginationRequest pagination = 2;
}

message GetPostCommentByIdResponse {
  PostComment data = 1;
}

message DeletePostCommentByIdRequest {
  int64 id = 1; // @gotag: validate:"required"
}

message UpdatePostCommentByIdRequest {
  string content = 1; // @gotag: validate:"required"
  int64 id = 2; // @gotag: validate:"required"
}
message DeletePostCommentByIdResponse {
  bool success = 1;
  string message = 2;
}

message UpdatePostCommentByIdResponse {
  bool success = 1;
  string message = 2;
}

service PostCommentService {
  rpc AddPostComment(AddPostCommentRequest) returns (AddPostCommentResponse) {
    option (api.authz.v1.options) = {
      require: true
      expression: "as_vtuber==true?_user.vtuberId!=null:true"
    };
  }
  rpc GetAllPostComments(GetAllPostCommentsRequest) returns (GetAllPostCommentsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllReplesOfComment(GetPostRepliesOfCommentRequest) returns (GetAllPostCommentsResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetPostCommentById(GetPostCommentByIdRequest) returns (GetPostCommentByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeletePostCommentById(DeletePostCommentByIdRequest) returns (DeletePostCommentByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc UpdatePostCommentById(UpdatePostCommentByIdRequest) returns (UpdatePostCommentByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
