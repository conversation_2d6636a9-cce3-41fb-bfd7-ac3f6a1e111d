syntax = "proto3";

package api.content.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/content/v1;contentv1";

message AddPostLikeRequest {
  int64 post_id = 1; // @gotag: validate:"required"
}

message GetPostLikeCountRequest {
  int64 post_id = 1; // @gotag: validate:"required"
}

message GetAllPostLikeByUserRequest {
  optional api.shared.v1.PaginationRequest pagination = 1;
}
message GetAllPostLikeByUserResponse {
  repeated PostLike data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message PostLike {
  int64 user_id = 1;
  int64 post_id = 2;
  google.protobuf.Timestamp created_at = 3;
}

message GetPostLikeCountResponse {
  int64 count = 1;
}

message DeletePostLikeByIdRequest {
  int64 post_id = 1; // @gotag: validate:"required"
}

message DeletePostLikeByIdResponse {
  bool success = 1;
  string message = 2;
}

message AddPostLikeResponse {
  bool success = 1;
  string message = 2;
}

service PostLikeService {
  rpc AddPostLike(AddPostLikeRequest) returns (AddPostLikeResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetPostLikeCount(GetPostLikeCountRequest) returns (GetPostLikeCountResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetPostLikesOfUser(GetAllPostLikeByUserRequest) returns (GetAllPostLikeByUserResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeletePostLikeById(DeletePostLikeByIdRequest) returns (DeletePostLikeByIdResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
