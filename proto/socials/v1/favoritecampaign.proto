syntax = "proto3";

package api.socials.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";

option go_package = "github.com/nsp-inc/vtuber/api/socials/v1;socialsv1";

message AddFavoriteCampaignRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
}

message AddFavoriteCampaignResponse {
  FavoriteCampaign data = 1;
}

message FavoriteCampaignWithDetails {
  int64 campaign_id = 1;
  int64 user_id = 2;
  google.protobuf.Timestamp created_at = 3;
  int64 id = 4;
  string short_description = 5;
  string name = 6;
  string image = 7;
}

message FavoriteCampaign {
  int64 id = 1;
  int64 campaign_id = 2;
  int64 user_id = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
}

message GetAllFavoriteCampaignRequest {
  optional api.shared.v1.PaginationRequest pagination = 3;
}

message GetAllFavoriteCampaignResponse {
  repeated FavoriteCampaignWithDetails data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message DeleteFavoriteCampaignRequest {
  int64 campaign_id = 1; // @gotag: validate:"required"
}

service FavoriteCampaignService {
  rpc AddFavoriteCampaign(AddFavoriteCampaignRequest) returns (AddFavoriteCampaignResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllFavoriteCampaign(GetAllFavoriteCampaignRequest) returns (GetAllFavoriteCampaignResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeleteFavoriteCampaign(DeleteFavoriteCampaignRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
