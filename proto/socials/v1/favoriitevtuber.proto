syntax = "proto3";

package api.socials.v1;

import "authz/v1/authz.proto";
import "google/protobuf/timestamp.proto";
import "shared/v1/generic.proto";
import "shared/v1/pagination.proto";
import "shared/v1/profile.proto";
import "shared/v1/social_media_links.proto";
import "vtubers/v1/vtuberprofiles.proto";

option go_package = "github.com/nsp-inc/vtuber/api/socials/v1;socialsv1";

message AddFavoriteVtuberRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
}

message AddFavoriteVtuberResponse {
  FavoriteVtuber data = 1;
}

message FavoriteVtuberWithDetails {
  api.vtubers.v1.VtuberProfile vtuber = 1;
  int64 vtuber_id = 2;
  int64 user_id = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  int64 id = 6;
  string username = 7;
}

message FavoriteVtuber {
  int64 id = 1;
  int64 vtuber_id = 2;
  int64 user_id = 3;
  google.protobuf.Timestamp created_at = 4;
  google.protobuf.Timestamp updated_at = 5;
  string username = 6;
}

message GetAllFavoriteVtuberRequest {
  optional api.shared.v1.PaginationRequest pagination = 3;
}

message GetAllFavoriteVtuberResponse {
  repeated FavoriteVtuberWithDetails data = 1;
  api.shared.v1.PaginationDetails pagination_details = 2;
}

message DeleteFavoriteVtuberRequest {
  int64 vtuber_id = 1; // @gotag: validate:"required"
}

service FavoriteVtuberService {
  rpc AddFavoriteVtuber(AddFavoriteVtuberRequest) returns (AddFavoriteVtuberResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc GetAllFavoriteVtuber(GetAllFavoriteVtuberRequest) returns (GetAllFavoriteVtuberResponse) {
    option (api.authz.v1.options) = {require: true};
  }
  rpc DeleteFavoriteVtuber(DeleteFavoriteVtuberRequest) returns (api.shared.v1.GenericResponse) {
    option (api.authz.v1.options) = {require: true};
  }
}
